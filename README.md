# Quick setup on local dev env

### backend service modulok indítása

````sh
~/Projektek/ibt

$ <NAME_EMAIL>:v3/ibtdevelopment/Market%20Partnerport%C3%A1l/PartnerPortal

$ cd PartnerPortal

$ dotnet --version
6.0.402

$ node --version
v16.14.0

$ npm --version
8.3.1

$ npm install
... wait some minutes! ;-)

$ dotnet build

- Cmd+Shift+P > Open External Terminal

$ npx nx run Backend.Useradmin:serve --configuration=development
$ dotnet "watch" "--project" "./apps/backend/Useradmin/PartnerPortal.Backend.Useradmin.csproj" "run" "--configuration" "Debug" "--launchProfile" "PartnerPortal.Backend.Useradmin" "--args" "--no-hot-reload"

==> browse swagger: http://localhost:5227/swagger/index.html

$ npx nx run Backend.Contracts:serve --configuration=development
$ dotnet "watch" "--project" "./apps/backend/Contracts/PartnerPortal.Backend.Contracts.csproj" "run" "--configuration" "Debug" "--launchProfile" "PartnerPortal.Backend.Contracts" "--args" "--no-hot-reload"

==> browse swagger: http://localhost:5243/swagger/index.html

```sh
$ dotnet "run" "--project" "./PartnerPortal.AiBenchmark.Console.csproj"  "--launchProfile" "PartnerPortal.AiBenchmark.Console" "--args" "--no-hot-reload"
````

avagy

    Visual Studio Code / Nx Console / Projects / apps / backend /
    - Backend.Useradmin / serve > execute
    - Backend.Contracts / serve > execute

avagy:

```sh
$ nx serve Backend.Useradmin

$ nx serve Backend.Contracts
```

### BC mock service helyben indítása

To run both Mock services BC and ELO:

```sh
nx run soapui-mock-server:serve
```

### helyi portál frontend app indítása

    Visual Studio Code / Nx Console / Projects / apps / frontend /
    - frontend-portal-app / serve / development > execute

### További hasznos fejlesztői információk a Wiki-n

-   https://dev.azure.com/ibtdevelopment/Market%20Partnerport%C3%A1l/_wiki/wikis/Market-Partnerport%C3%A1l.wiki/120/Fejleszt%C5%91i-k%C3%B6rnyezetek-kiad%C3%A1sok-el%C5%91k%C3%A9sz%C3%ADt%C3%A9se-(draft)?anchor=helyi-fejleszt%C5%91i-k%C3%B6rnyezet

nx g @nx-dotnet/core:library --name=PartnerPortal.Backend.Document --language=C# --testTemplate=xunit --directory=backend/document --pathScheme=dotnet --template=classlib

### Formázás

Prettier a frontend vagy frontendel kapcsolatos kódokra.

```sh
nx format:write
nx format:check
```

C# kódokra a Csharpier extension használata. Minden ApiStub generálás után a következő parancsot kell futtatni:

```sh
dotnet csharpier format .
```

## EF core commands

Add migration:

```bash
cd libs/backend/shared/PartnerPortalDatabase && dotnet ef migrations add UpdateUploadType --startup-project=../../../../apps/backend/Useradmin --context=UserDbContext  && dotnet csharpier format .
```

Remove last migration:

```bash
cd libs/backend/shared/PartnerPortalDatabase && dotnet ef migrations remove --force --startup-project=../../../../apps/backend/Useradmin --context=UserDbContext && dotnet csharpier format .
```

## IIS env variables

This is where the encrypted variables are stored:
https://andrewlock.net/setting-environment-variables-in-iis-and-avoiding-app-pool-restarts/#using-the-iis-ui

### Deploying

Készíts egy DB backupot. A Db VM-be belépve - SQL Server Management Studio-ba rá mész a DB-re és készítesz egy backupot a D meghajtóra. Nevezd el a többihez hasonlóan!

Használhatod ezt a parancsot a backup készítéséhez:

dev:

```sql
BACKUP DATABASE [PartnerPortal] TO  DISK = N'D:\Microsoft SQL Server\MSSQL16.PPDBDEV\MSSQL\Backup\PartnerPortal-202504142157.bak' WITH NOFORMAT, NOINIT,  NAME = N'PartnerPortal-Full Database Backup', SKIP, NOREWIND, NOUNLOAD,  STATS = 10
GO
```

uat:

```sql
BACKUP DATABASE [PartnerPortalUAT] TO  DISK = N'D:\Microsoft SQL Server\MSSQL16.PPDBUAT\MSSQL\Backup\PartnerPortal-202505141412.bak' WITH NOFORMAT, NOINIT,  NAME = N'PartnerPortalUAT-Full Database Backup', SKIP, NOREWIND, NOUNLOAD,  STATS = 10
GO
```

-   Majd futtast a release.ps1 scriptet a rootban.
-   Ez létrehozza a release mappába a zip-et megadott environment-re.
-   Ezt átmásolod a 3 VM (Useradmin, Contracts, Frontend) C meghajtójára és futtatod a C meghajtón az install-marketon.ps1 scriptet (jobb klikk, run with powershell).
-   Az install-marketon.ps1 script az Ip alapján tudja melyik VM-en van.
-   A script létrehozza backupot és átmásolja a szükséges fájlokat.

```sh
pwsh tools/release.ps1
```

# Marketon Database Setup

This setup provides a Microsoft SQL Server 2022 development environment using Docker for the Marketon project.

**Start the database:**

```powershell
pwsh ./tools/db-manage.ps1 start
```

This will start SQL Server and automatically run all initialization scripts.

**See other commands:**

```powershell
pwsh ./tools/db-manage.ps1 help
```

## Connection String

For the .NET applications, use this connection string:

```csharp
"Server=localhost,1433;Database=Marketon;User Id=sa;Password=MyStrongPassword123!;TrustServerCertificate=true;"
```

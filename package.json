{"name": "partner-portal", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx serve", "build": "nx build --configuration development", "build:iis-dev": "nx build frontend-portal-app --configuration release.DEV --base-href /portal-app/", "build:iis-uat": "nx build frontend-portal-app --configuration release.UAT --base-href /portal-app/", "build:iis-prod": "nx build frontend-portal-app --configuration release.PROD --base-href /portal-app/", "test": "nx test", "prepare": "nx g @nx-dotnet/core:restore && dotnet husky install", "format": "nx format:write && dotnet csharpier format .", "format:check": "nx format:check", "format2": "prettier --version && prettier . --write && dotnet csharpier format .", "format-appsettings": "node tools/sortAppSettingsJson.js", "mocks": "nx run soapui-mock-server:serve"}, "private": true, "devDependencies": {"@angular-devkit/build-angular": "^15.2.10", "@angular-devkit/core": "~15.2.4", "@angular-devkit/schematics": "~15.2.4", "@angular-eslint/eslint-plugin": "15.2.0", "@angular-eslint/eslint-plugin-template": "15.2.0", "@angular-eslint/template-parser": "15.2.0", "@angular/cli": "^15.2.10", "@angular/compiler-cli": "15.2.3", "@angular/language-service": "15.2.3", "@ngrx/eslint-plugin": "15.3.0", "@ngrx/schematics": "15.3.0", "@nrwl/cli": "15.9.7", "@nx-dotnet/core": "2.1.1", "@nx/angular": "17.1.2", "@nx/cypress": "17.1.2", "@nx/eslint": "17.1.2", "@nx/eslint-plugin": "17.1.2", "@nx/jest": "17.1.2", "@nx/js": "17.1.2", "@nx/workspace": "17.1.2", "@openapitools/openapi-generator-cli": "^2.20.0", "@schematics/angular": "~15.2.4", "@trumbitta/nx-plugin-openapi": "1.5.1", "@types/crypto-js": "4.1.1", "@types/jest": "29.4.4", "@types/node": "18.11.18", "@typescript-eslint/eslint-plugin": "5.47.1", "@typescript-eslint/parser": "5.47.1", "@xmldom/xmldom": "^0.9.8", "cypress": "^13.5.0", "eslint": "8.30.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-cypress": "2.12.1", "jest": "29.4.3", "jest-environment-jsdom": "29.4.3", "jest-preset-angular": "13.0.0", "moment": "^2.29.4", "nx": "17.1.2", "prettier": "^2.8.4", "sort-json": "^2.0.1", "ts-jest": "29.0.5", "ts-node": "10.9.1", "typescript": "4.9.5"}, "dependencies": {"@angular/animations": "15.2.3", "@angular/cdk": "~15.0.0", "@angular/common": "15.2.3", "@angular/compiler": "15.2.3", "@angular/core": "15.2.3", "@angular/forms": "15.2.3", "@angular/platform-browser": "15.2.3", "@angular/platform-browser-dynamic": "15.2.3", "@angular/router": "15.2.3", "@auth0/angular-jwt": "5.1.2", "@ngrx/component": "15.3.0", "@ngrx/component-store": "15.3.0", "@ngrx/effects": "15.3.0", "@ngrx/entity": "15.3.0", "@ngrx/router-store": "15.3.0", "@ngrx/store": "15.3.0", "@ngrx/store-devtools": "15.3.0", "@ngx-translate/core": "^14.0.0", "crypto-js": "4.2.0", "highlight.js": "^11.11.1", "prettier-plugin-organize-imports": "^4.1.0", "primeflex": "3.3.0", "primeicons": "^6.0.1", "primeng": "^15.2.0", "rxjs": "7.8.0", "tslib": "2.4.1", "uuid": "^11.1.0", "vanilla-jsoneditor": "^3.3.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.19.3/xlsx-0.19.3.tgz", "zone.js": "0.12.0"}}
<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project
  id="97a84f5b-75b4-4617-9d43-e6d434dcbd9d"
  activeEnvironment="Default"
  name="Market ELO API"
  resourceRoot=""
  soapui-version="5.8.0"
  abortOnError="false"
  runType="SEQUENTIAL"
  xmlns:con="http://eviware.com/soapui/config"
>
  <con:settings />
  <con:interface
    xsi:type="con:WsdlInterface"
    id="a741d376-f1f9-433d-b528-a453c2a30218"
    wsaVersion="NONE"
    name="ELOWebServiceImplPortBinding"
    type="wsdl"
    bindingName="{http://webservice.market.module.eloex.ovitas.hu/}ELOWebServiceImplPortBinding"
    soapVersion="1_1"
    anonymous="optional"
    definition="file:/Users/<USER>/Documents/AzureDevOps/PartnerPortal_v1.x/libs/api-spec/api-spec-elo-market/src/ELOWebService.wsdl"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  >
    <con:settings />
    <con:definitionCache
      type="TEXT"
      rootPart="file:/Users/<USER>/Documents/AzureDevOps/PartnerPortal_v1.x/libs/api-spec/api-spec-elo-market/src/ELOWebService.wsdl"
    >
      <con:part>
        <con:url>file:/Users/<USER>/Documents/AzureDevOps/PartnerPortal_v1.x/libs/api-spec/api-spec-elo-market/src/ELOWebService.wsdl</con:url>
        <con:content>
          <![CDATA[<!--Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.8 svn-revision#13980.-->
<definitions targetNamespace="http://webservice.market.module.eloex.ovitas.hu/" name="ELOWebService" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:tns="http://webservice.market.module.eloex.ovitas.hu/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata">
  <types>
    <xs:schema version="1.0" targetNamespace="http://webservice.market.module.eloex.ovitas.hu/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="countElements" type="tns:countElements"/>
      <xs:element name="countElementsResponse" type="tns:countElementsResponse"/>
      <xs:element name="createDataBackup" type="tns:createDataBackup"/>
      <xs:element name="createDataBackupResponse" type="tns:createDataBackupResponse"/>
      <xs:element name="createEntity" type="tns:createEntity"/>
      <xs:element name="createEntityResponse" type="tns:createEntityResponse"/>
      <xs:element name="linkRecord" type="tns:linkRecord"/>
      <xs:element name="linkRecordResponse" type="tns:linkRecordResponse"/>
      <xs:element name="ping" type="tns:ping"/>
      <xs:element name="pingResponse" type="tns:pingResponse"/>
      <xs:element name="rejectEntity" type="tns:rejectEntity"/>
      <xs:element name="rejectEntityResponse" type="tns:rejectEntityResponse"/>
      <xs:element name="sendNotification" type="tns:sendNotification"/>
      <xs:element name="sendNotificationResponse" type="tns:sendNotificationResponse"/>
      <xs:element name="startWorkflow" type="tns:startWorkflow"/>
      <xs:element name="startWorkflowResponse" type="tns:startWorkflowResponse"/>
      <xs:element name="updateEntity" type="tns:updateEntity"/>
      <xs:element name="updateEntityResponse" type="tns:updateEntityResponse"/>
      <xs:element name="uploadFile" type="tns:uploadFile"/>
      <xs:element name="uploadFileResponse" type="tns:uploadFileResponse"/>
      <xs:element name="searchEntity" type="tns:searchEntity"/>
      <xs:element name="searchEntityResponse" type="tns:searchEntityResponse"/>
      <xs:complexType name="updateEntity">
        <xs:sequence>
          <xs:element name="request" type="tns:UpdateEntityRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="UpdateEntityRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="syncRequest" type="xs:boolean"/>
              <xs:element name="identifiers" type="tns:identifiers"/>
              <xs:element name="entityData" type="tns:entityData"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="baseRequest">
        <xs:sequence>
          <xs:element name="systemId" type="xs:string" default="BC" minOccurs="0"/>
          <xs:element name="user" type="xs:string"/>
          <xs:element name="companyId" type="xs:string"/>
          <xs:element name="entityType" type="xs:string"/>
          <xs:element name="timeStamp" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="identifiers">
        <xs:sequence>
          <xs:element name="eloGuid" type="xs:string" minOccurs="0"/>
          <xs:element name="eloRegId" type="xs:string" minOccurs="0"/>
          <xs:element name="bcId" type="xs:string" minOccurs="0"/>
          <xs:element name="bcRegId" type="xs:string" minOccurs="0"/>
          <xs:element name="extId" type="xs:string" minOccurs="0"/>
          <xs:element name="barCode" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="entityData">
        <xs:sequence>
          <xs:element name="partnerId" type="xs:string" minOccurs="0"/>
          <xs:element name="projectId" type="xs:string" minOccurs="0"/>
          <xs:element name="bcLink" type="xs:string" minOccurs="0"/>
          <xs:element name="fields" type="tns:entityDataField" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="entityDataField">
        <xs:sequence>
          <xs:element name="key" type="xs:string"/>
          <xs:element name="value" type="xs:string"/>
          <xs:element name="type" type="xs:string"/>
          <xs:element name="rowIndex" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="updateEntityResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:UpdateEntityResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="UpdateEntityResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence/>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="BaseResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:responseData"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="responseData">
        <xs:sequence>
          <xs:element name="code" type="xs:int"/>
          <xs:element name="message" type="xs:string" minOccurs="0"/>
          <xs:element name="timeStamp" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="uploadFile">
        <xs:sequence>
          <xs:element name="request" type="tns:UploadFileRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="UploadFileRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="identifiers" type="tns:identifiers"/>
              <xs:element name="file" type="tns:fileData"/>
              <xs:element name="linkIdentifiers" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="fileData">
        <xs:sequence>
          <xs:element name="path" type="xs:string" minOccurs="0"/>
          <xs:element name="mime" type="xs:string" minOccurs="0"/>
          <xs:element name="type" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="uploadFileResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:UploadFileResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="UploadFileResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence>
              <xs:element name="fileId" type="xs:string"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="countElements">
        <xs:sequence>
          <xs:element name="request" type="tns:CountElementsRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CountElementsRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="filter" type="tns:filter"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="filter">
        <xs:sequence>
          <xs:element name="companyId" type="xs:string"/>
          <xs:element name="projectId" type="xs:string"/>
          <xs:element name="mask" type="xs:string"/>
          <xs:element name="fields" type="tns:entityDataField" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="countElementsResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:CountElementsResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CountElementsResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence>
              <xs:element name="response" type="tns:responseData"/>
              <xs:element name="count" type="xs:int"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="ping">
        <xs:sequence/>
      </xs:complexType>
      <xs:complexType name="pingResponse">
        <xs:sequence>
          <xs:element name="return" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="rejectEntity">
        <xs:sequence>
          <xs:element name="request" type="tns:RejectEntityRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RejectEntityRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="identifiers" type="tns:identifiers"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="rejectEntityResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:RejectEntityResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RejectEntityResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence/>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="sendNotification">
        <xs:sequence>
          <xs:element name="request" type="tns:SendNotificationRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SendNotificationRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="addressedUsers" type="xs:string" maxOccurs="10"/>
              <xs:element name="message" type="xs:string"/>
              <xs:element name="identifiers" type="tns:identifiers" minOccurs="0"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="sendNotificationResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:SendNotificationResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SendNotificationResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence>
              <xs:element name="response" type="tns:responseData"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="createEntity">
        <xs:sequence>
          <xs:element name="request" type="tns:CreateEntityRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CreateEntityRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="identifiers" type="tns:identifiers"/>
              <xs:element name="entityData" type="tns:entityData"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="createEntityResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:CreateEntityResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CreateEntityResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence>
              <xs:element name="identifiers" type="tns:identifiers"/>
              <xs:element name="entityData" type="tns:entityData"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="startWorkflow">
        <xs:sequence>
          <xs:element name="request" type="tns:StartWorkflowRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="StartWorkflowRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="workflowTemplate" type="xs:string"/>
              <xs:element name="identifiers" type="tns:identifiers" minOccurs="0"/>
              <xs:element name="entityData" type="tns:entityData" minOccurs="0"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="startWorkflowResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:StartWorkflowResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="StartWorkflowResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence>
              <xs:element name="workflowId" type="xs:string"/>
              <xs:element name="identifiers" type="tns:identifiers" minOccurs="0"/>
              <xs:element name="entityData" type="tns:entityData" minOccurs="0"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="linkRecord">
        <xs:sequence>
          <xs:element name="request" type="tns:LinkRecordRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="LinkRecordRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="identifiers" type="tns:identifiers"/>
              <xs:element name="recordId" type="xs:string"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="linkRecordResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:LinkRecordResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="LinkRecordResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence/>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="createDataBackup">
        <xs:sequence>
          <xs:element name="request" type="tns:CreateDataBackupRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CreateDataBackupRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="identifiers" type="tns:identifiers"/>
              <xs:element name="backupId" type="xs:string" minOccurs="0"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="createDataBackupResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:CreateDataBackupResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CreateDataBackupResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence/>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="searchEntity">
        <xs:sequence>
          <xs:element name="request" type="tns:SearchEntityRequest"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SearchEntityRequest">
        <xs:complexContent>
          <xs:extension base="tns:baseRequest">
            <xs:sequence>
              <xs:element name="identifiers" type="tns:identifiers" minOccurs="0"/>
              <xs:element name="filter" type="tns:searchFilter" minOccurs="0"/>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="searchFilter">
        <xs:sequence>
          <xs:element name="projectId" type="xs:string" minOccurs="0"/>
          <xs:element name="fields" type="tns:searchEntityField" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="searchEntityField">
        <xs:sequence>
          <xs:element name="key" type="xs:string"/>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="searchEntityResponse">
        <xs:sequence>
          <xs:element name="response" type="tns:SearchEntityResponse"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SearchEntityResponse">
        <xs:complexContent>
          <xs:extension base="tns:BaseResponse">
            <xs:sequence>
              <xs:element name="resultList">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="resultEntity" type="tns:resultEntity" minOccurs="0" maxOccurs="unbounded"/>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="resultEntity">
        <xs:sequence>
          <xs:element name="identifiers" type="tns:identifiers"/>
          <xs:element name="entityData" type="tns:entityData"/>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </types>
  <message name="countElements">
    <part name="parameters" element="tns:countElements"/>
  </message>
  <message name="countElementsResponse">
    <part name="parameters" element="tns:countElementsResponse"/>
  </message>
  <message name="updateEntity">
    <part name="parameters" element="tns:updateEntity"/>
  </message>
  <message name="updateEntityResponse">
    <part name="parameters" element="tns:updateEntityResponse"/>
  </message>
  <message name="uploadFile">
    <part name="parameters" element="tns:uploadFile"/>
  </message>
  <message name="uploadFileResponse">
    <part name="parameters" element="tns:uploadFileResponse"/>
  </message>
  <message name="rejectEntity">
    <part name="parameters" element="tns:rejectEntity"/>
  </message>
  <message name="rejectEntityResponse">
    <part name="parameters" element="tns:rejectEntityResponse"/>
  </message>
  <message name="createEntity">
    <part name="parameters" element="tns:createEntity"/>
  </message>
  <message name="createEntityResponse">
    <part name="parameters" element="tns:createEntityResponse"/>
  </message>
  <message name="linkRecord">
    <part name="parameters" element="tns:linkRecord"/>
  </message>
  <message name="linkRecordResponse">
    <part name="parameters" element="tns:linkRecordResponse"/>
  </message>
  <message name="sendNotification">
    <part name="parameters" element="tns:sendNotification"/>
  </message>
  <message name="sendNotificationResponse">
    <part name="parameters" element="tns:sendNotificationResponse"/>
  </message>
  <message name="ping">
    <part name="parameters" element="tns:ping"/>
  </message>
  <message name="pingResponse">
    <part name="parameters" element="tns:pingResponse"/>
  </message>
  <message name="startWorkflow">
    <part name="parameters" element="tns:startWorkflow"/>
  </message>
  <message name="startWorkflowResponse">
    <part name="parameters" element="tns:startWorkflowResponse"/>
  </message>
  <message name="createDataBackup">
    <part name="parameters" element="tns:createDataBackup"/>
  </message>
  <message name="createDataBackupResponse">
    <part name="parameters" element="tns:createDataBackupResponse"/>
  </message>
  <message name="searchEntity">
    <part name="parameters" element="tns:searchEntity"/>
  </message>
  <message name="searchEntityResponse">
    <part name="parameters" element="tns:searchEntityResponse"/>
  </message>
  <portType name="ELOWebServiceImpl">
    <operation name="countElements">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/countElementsRequest" message="tns:countElements"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/countElementsResponse" message="tns:countElementsResponse"/>
    </operation>
    <operation name="updateEntity">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/updateEntityRequest" message="tns:updateEntity"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/updateEntityResponse" message="tns:updateEntityResponse"/>
    </operation>
    <operation name="uploadFile">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/uploadFileRequest" message="tns:uploadFile"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/uploadFileResponse" message="tns:uploadFileResponse"/>
    </operation>
    <operation name="rejectEntity">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/rejectEntityRequest" message="tns:rejectEntity"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/rejectEntityResponse" message="tns:rejectEntityResponse"/>
    </operation>
    <operation name="createEntity">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createEntityRequest" message="tns:createEntity"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createEntityResponse" message="tns:createEntityResponse"/>
    </operation>
    <operation name="linkRecord">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/linkRecordRequest" message="tns:linkRecord"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/linkRecordResponse" message="tns:linkRecordResponse"/>
    </operation>
    <operation name="sendNotification">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/sendNotificationRequest" message="tns:sendNotification"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/sendNotificationResponse" message="tns:sendNotificationResponse"/>
    </operation>
    <operation name="ping">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/pingRequest" message="tns:ping"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/pingResponse" message="tns:pingResponse"/>
    </operation>
    <operation name="startWorkflow">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/startWorkflowRequest" message="tns:startWorkflow"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/startWorkflowResponse" message="tns:startWorkflowResponse"/>
    </operation>
    <operation name="createDataBackup">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createDataBackupRequest" message="tns:createDataBackup"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createDataBackupResponse" message="tns:createDataBackupResponse"/>
    </operation>
    <operation name="searchEntity">
      <input wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/searchEntityRequest" message="tns:searchEntity"/>
      <output wsam:Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/searchEntityResponse" message="tns:searchEntityResponse"/>
    </operation>
  </portType>
  <binding name="ELOWebServiceImplPortBinding" type="tns:ELOWebServiceImpl">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
    <operation name="countElements">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="updateEntity">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="uploadFile">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="rejectEntity">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="createEntity">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="linkRecord">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="sendNotification">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="ping">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="startWorkflow">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="createDataBackup">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="searchEntity">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="ELOWebService">
    <port name="ELOWebServiceImplPort" binding="tns:ELOWebServiceImplPortBinding">
      <soap:address location="REPLACE_WITH_ACTUAL_URL"/>
    </port>
  </service>
</definitions>]]>
        </con:content>
        <con:type>http://schemas.xmlsoap.org/wsdl/</con:type>
      </con:part>
    </con:definitionCache>
    <con:endpoints>
      <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
      <con:endpoint>http://mac.local:8088/SoapUiMock/EloWebservice</con:endpoint>
    </con:endpoints>
    <con:operation
      id="33fe1bb2-a81b-47b7-8032-2c96d911ace7"
      isOneWay="false"
      action=""
      name="countElements"
      bindingOperationName="countElements"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="ae02c1bc-fd13-4cd9-91db-2fe15ac81aed" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:countElements>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <filter>
               <companyId>?</companyId>
               <projectId>?</projectId>
               <mask>?</mask>
               <!--1 or more repetitions:-->
               <fields>
                  <key>?</key>
                  <value>?</value>
                  <type>?</type>
                  <rowIndex>?</rowIndex>
               </fields>
            </filter>
         </request>
      </web:countElements>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/countElementsRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="a52753c6-c842-46e6-9b7e-6905981d54a8"
      isOneWay="false"
      action=""
      name="createDataBackup"
      bindingOperationName="createDataBackup"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="c7e6579f-04ef-44a8-8ec4-41563014536e" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:createDataBackup>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
            <!--Optional:-->
            <backupId>?</backupId>
         </request>
      </web:createDataBackup>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createDataBackupRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="b59353b5-255b-44f0-a2e2-94f4b735bc24"
      isOneWay="false"
      action=""
      name="createEntity"
      bindingOperationName="createEntity"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="101b9589-65f9-4bf7-8555-88b6f83c61fb" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:createEntity>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
            <entityData>
               <!--Optional:-->
               <partnerId>?</partnerId>
               <!--Optional:-->
               <projectId>?</projectId>
               <!--Optional:-->
               <bcLink>?</bcLink>
               <!--Zero or more repetitions:-->
               <fields>
                  <key>?</key>
                  <value>?</value>
                  <type>?</type>
                  <rowIndex>?</rowIndex>
               </fields>
            </entityData>
         </request>
      </web:createEntity>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createEntityRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="8f3385b1-c0ba-4300-9193-87bf7eeae685"
      isOneWay="false"
      action=""
      name="linkRecord"
      bindingOperationName="linkRecord"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="9e550723-4c1e-4f8f-9e83-c8ae164d41f9" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:linkRecord>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
            <recordId>?</recordId>
         </request>
      </web:linkRecord>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/linkRecordRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="b54c2aed-a125-489e-bf21-fca10f551848"
      isOneWay="false"
      action=""
      name="ping"
      bindingOperationName="ping"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="099dfd07-b4a5-4597-bf72-e1c027ed564f" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:ping/>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/pingRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="6b37e812-574a-42e9-99d9-f8fb0af0c9b5"
      isOneWay="false"
      action=""
      name="rejectEntity"
      bindingOperationName="rejectEntity"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="9ed03f45-2820-4b0a-b512-5030fa4a8be5" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:rejectEntity>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
         </request>
      </web:rejectEntity>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/rejectEntityRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="2db6e792-3acb-499a-95a4-728c8a417743"
      isOneWay="false"
      action=""
      name="sendNotification"
      bindingOperationName="sendNotification"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="c83cc669-64b3-436e-84af-3a18b338aa84" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:sendNotification>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <!--1 to 10 repetitions:-->
            <addressedUsers>?</addressedUsers>
            <message>?</message>
            <!--Optional:-->
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
         </request>
      </web:sendNotification>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/sendNotificationRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="8aa69aee-7b9d-433b-a5ab-5c5092a2de3a"
      isOneWay="false"
      action=""
      name="startWorkflow"
      bindingOperationName="startWorkflow"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="33079138-eed6-4465-9f2d-78b04ae2865f" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:startWorkflow>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <workflowTemplate>?</workflowTemplate>
            <!--Optional:-->
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
            <!--Optional:-->
            <entityData>
               <!--Optional:-->
               <partnerId>?</partnerId>
               <!--Optional:-->
               <projectId>?</projectId>
               <!--Optional:-->
               <bcLink>?</bcLink>
               <!--Zero or more repetitions:-->
               <fields>
                  <key>?</key>
                  <value>?</value>
                  <type>?</type>
                  <rowIndex>?</rowIndex>
               </fields>
            </entityData>
         </request>
      </web:startWorkflow>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/startWorkflowRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="2f9491d4-3f96-43e8-a52f-b89f766b69ee"
      isOneWay="false"
      action=""
      name="updateEntity"
      bindingOperationName="updateEntity"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="229b73f3-768f-4013-b75b-52da4766ae3e" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:updateEntity>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <syncRequest>?</syncRequest>
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
            <entityData>
               <!--Optional:-->
               <partnerId>?</partnerId>
               <!--Optional:-->
               <projectId>?</projectId>
               <!--Optional:-->
               <bcLink>?</bcLink>
               <!--Zero or more repetitions:-->
               <fields>
                  <key>?</key>
                  <value>?</value>
                  <type>?</type>
                  <rowIndex>?</rowIndex>
               </fields>
            </entityData>
         </request>
      </web:updateEntity>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/updateEntityRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="dd8653be-7207-4002-bbcd-fa512dcb6925"
      isOneWay="false"
      action=""
      name="uploadFile"
      bindingOperationName="uploadFile"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="686769d9-b76b-415b-b161-14b012949758" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:uploadFile>
         <request>
            <!--Optional:-->
            <systemId>?</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
            <file>
               <!--Optional:-->
               <path>?</path>
               <!--Optional:-->
               <mime>?</mime>
               <!--Optional:-->
               <type>?</type>
            </file>
            <!--Zero or more repetitions:-->
            <linkIdentifiers>?</linkIdentifiers>
         </request>
      </web:uploadFile>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/uploadFileRequest"
        />
      </con:call>
    </con:operation>
    <con:operation
      id="0a994c6c-00fc-4294-b284-abd01a0e5860"
      isOneWay="false"
      action=""
      name="searchEntity"
      bindingOperationName="searchEntity"
      type="Request-Response"
      inputName=""
      receivesAttachments="false"
      sendsAttachments="false"
      anonymous="optional"
    >
      <con:settings />
      <con:call id="8a69aeeb-1f84-4cb8-b0a5-301a09f8db05" name="Request 1">
        <con:settings />
        <con:encoding>UTF-8</con:encoding>
        <con:endpoint>REPLACE_WITH_ACTUAL_URL</con:endpoint>
        <con:request>
          <![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:searchEntity>
         <request>
            <!--Optional:-->
            <systemId>BC</systemId>
            <user>?</user>
            <companyId>?</companyId>
            <entityType>?</entityType>
            <timeStamp>?</timeStamp>
            <!--Optional:-->
            <identifiers>
               <!--Optional:-->
               <eloGuid>?</eloGuid>
               <!--Optional:-->
               <eloRegId>?</eloRegId>
               <!--Optional:-->
               <bcId>?</bcId>
               <!--Optional:-->
               <bcRegId>?</bcRegId>
               <!--Optional:-->
               <extId>?</extId>
               <!--Optional:-->
               <barCode>?</barCode>
            </identifiers>
            <!--Optional:-->
            <filter>
               <!--Optional:-->
               <projectId>?</projectId>
               <!--Zero or more repetitions:-->
               <fields>
                  <key>?</key>
                  <value>?</value>
               </fields>
            </filter>
         </request>
      </web:searchEntity>
   </soapenv:Body>
</soapenv:Envelope>]]>
        </con:request>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/searchEntityRequest"
        />
      </con:call>
    </con:operation>
  </con:interface>
  <con:mockService
    id="ee62f32f-c6f4-4cca-a5cd-a3f0fb20b97c"
    port="8088"
    path="/SoapUiMock/EloWebservice"
    host="mac.local"
    name="EloWebserviceMock"
  >
    <con:settings>
      <con:setting id="com.eviware.soapui.impl.wsdl.mock.WsdlMockService@require-soap-action"
        >false</con:setting
      >
    </con:settings>
    <con:properties />
    <con:mockOperation
      name="countElements"
      id="7dad1d1f-74b6-4cb5-a5da-a26fb77f196b"
      interface="ELOWebServiceImplPortBinding"
      operation="countElements"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="acd80008-e726-4f2e-81c5-d08dd963521e"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:countElementsResponse>
         <response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
            <count>?</count>
         </response>
      </web:countElementsResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/countElementsResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="createDataBackup"
      id="5b224084-7de0-4276-ba51-ad5bae592d44"
      interface="ELOWebServiceImplPortBinding"
      operation="createDataBackup"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="3947eba1-d1a7-4231-ab0a-44edffac510f"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:createDataBackupResponse>
         <response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
         </response>
      </web:createDataBackupResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createDataBackupResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="createEntity"
      id="cae827bb-ab3b-4d89-956e-d82c59fdfab7"
      interface="ELOWebServiceImplPortBinding"
      operation="createEntity"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="295d63d7-e2fe-4b66-8745-6f5fe323cef9"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:createEntityResponse>
         <response>
            <response>
               <code>0</code>
               <message>Mock - sikeres beküldés!</message>
               <timeStamp>2025-01-23T10:53:15</timeStamp>
            </response>
            <identifiers>
               <eloGuid>(F0A02D4C-2027-CC1A-D804-A84114B4582F)</eloGuid>
               <eloRegId>MARK/INV/2025/2494</eloRegId>
               <barCode/>
            </identifiers>
         </response>
      </web:createEntityResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createEntityResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="linkRecord"
      id="92047092-b238-4f17-a369-a95d417b3fb7"
      interface="ELOWebServiceImplPortBinding"
      operation="linkRecord"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="c6bd701e-2c4b-4959-bd86-f6a0cf04961d"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:linkRecordResponse>
         <response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
         </response>
      </web:linkRecordResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/linkRecordResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="ping"
      id="b67d53d4-ccfb-4fdf-bd9f-f472a581e49c"
      interface="ELOWebServiceImplPortBinding"
      operation="ping"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="dd82f0e0-4030-4675-8f7a-d2b6104ab5bc"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:pingResponse>
         <!--Optional:-->
         <return>?</return>
      </web:pingResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/pingResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="rejectEntity"
      id="049cc1a3-2be3-4757-8072-86588c9bb267"
      interface="ELOWebServiceImplPortBinding"
      operation="rejectEntity"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="a9bfef0c-16d1-4cd4-bd67-8ceaae3b9c28"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:rejectEntityResponse>
         <response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
         </response>
      </web:rejectEntityResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/rejectEntityResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="sendNotification"
      id="e882a5e1-b213-40d1-b00f-23fb509d451b"
      interface="ELOWebServiceImplPortBinding"
      operation="sendNotification"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="f8bd4f19-7bd3-4c19-8701-cb38bf57eae8"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:sendNotificationResponse>
         <response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
         </response>
      </web:sendNotificationResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/sendNotificationResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="startWorkflow"
      id="c1a60dc1-15b7-4a65-87cb-03cf7b8861e3"
      interface="ELOWebServiceImplPortBinding"
      operation="startWorkflow"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="b8799b4b-b30d-4699-8dc0-014cc135180a"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:startWorkflowResponse>
         <response>
            <response>
               <code>0</code>
               <timeStamp>20250430142530</timeStamp>
            </response>
            <workflowId>37720</workflowId>
            <identifiers>
               <eloGuid>(78D6157C-9060-B7CC-5A9D-160E4A48F8D3)</eloGuid>
               <eloRegId>MARK/INV/2025/0017</eloRegId>
               <barCode/>
            </identifiers>
         </response>
      </web:startWorkflowResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/startWorkflowResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="updateEntity"
      id="d441a77f-cbb4-4add-8ee4-0185451917b2"
      interface="ELOWebServiceImplPortBinding"
      operation="updateEntity"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="4e1d2484-c6b3-428e-b80d-bc10e37f0791"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:updateEntityResponse>
         <response>
            <response>
               <code>?</code>
               <!--Optional:-->
               <message>?</message>
               <timeStamp>?</timeStamp>
            </response>
         </response>
      </web:updateEntityResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/updateEntityResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="uploadFile"
      id="a4a27ac6-ed2b-44b4-b89b-15c067c7afb1"
      interface="ELOWebServiceImplPortBinding"
      operation="uploadFile"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="cad77e5b-318c-4d10-8f8f-c9dbc3ed936a"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[
          <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:uploadFileResponse>
         <response>
            <response>
               <code>0</code>
               <timeStamp>2025-01-23T10:53:15</timeStamp>
            </response>
            <fileId>(01BBAE6D-ABDC-DB79-635B-DB0D43A3D0F8)</fileId>
         </response>
      </web:uploadFileResponse>
   </soapenv:Body>
</soapenv:Envelope>
        ]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/uploadFileResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
    <con:mockOperation
      name="searchEntity"
      id="2fc3f30a-0025-47e3-90ff-6671d4281df7"
      interface="ELOWebServiceImplPortBinding"
      operation="searchEntity"
    >
      <con:settings />
      <con:defaultResponse>Response 1</con:defaultResponse>
      <con:dispatchStyle>SEQUENCE</con:dispatchStyle>
      <con:response
        name="Response 1"
        id="7cc3e00d-66e6-41b9-b9cb-d8b839d5d224"
        httpResponseStatus="200"
        encoding="UTF-8"
      >
        <con:settings />
        <con:responseContent>
          <![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.market.module.eloex.ovitas.hu/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:searchEntityResponse>
         <response>
            <response>
               <code>0</code>
               <message>OK</message>
               <timeStamp>2025-01-23T10:53:15</timeStamp>
            </response>
            <resultList>
            </resultList>
         </response>
      </web:searchEntityResponse>
   </soapenv:Body>
</soapenv:Envelope>]]>
        </con:responseContent>
        <con:wsaConfig
          mustUnderstand="NONE"
          version="200508"
          action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/searchEntityResponse"
        />
      </con:response>
      <con:dispatchConfig />
    </con:mockOperation>
  </con:mockService>
  <con:properties>
    <con:property>
      <con:name>Market ELO API</con:name>
      <con:value />
    </con:property>
  </con:properties>
  <con:wssContainer />
  <con:oAuth2ProfileContainer />
  <con:oAuth1ProfileContainer />
  <con:sensitiveInformation />
</con:soapui-project>

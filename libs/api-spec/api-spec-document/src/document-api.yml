openapi: 3.0.3
info:
  title: Market Partner Portal - documents - public/frontend REST API
  description: Partner port<PERSON><PERSON> fel<PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
  version: '1.0'
servers:
  - url: https://localhost:7047/contracts
    description: Local environment.

tags:
  - name: partnerDocument
    description: Partner document upload operations
  - name: secretaryDocument
    description: Secretary document upload operations
  - name: calendarManagement
    description: Calendar management operations for Administrator
  - name: partners
    description: Partnerek lekérdezése titkárok számára 
  - name: documentReport
    description: Document report operations

paths:
  /documents/{contextObjectId}/uploadFile:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/uploadFile'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/{contextObjectId}/revertFile/{fileId}:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/revertFile'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/{contextObjectId}/revertFiles:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/revertFiles'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/{contextObjectId}/commitFiles:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/commitFiles'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/{contextObjectId}/listFiles:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/listFiles'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/{contextObjectId}/downloadFile/{fileId}:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/downloadFile'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/{contextObjectId}/downloadZip:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/downloadZip'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/{contextObjectId}/deleteFiles:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/deleteFiles'
    parameters:
      - $ref: '#/components/parameters/context'
  /documents/getAttachmentConfigs:
    $ref: '../../api-spec-contracts/src/attachment-api.yml#/operations/getAttachmentConfigs'
    parameters:
      - $ref: '#/components/parameters/context'

  /documents/partner/document/draft:
    post:
      tags:
        - partnerDocument
      summary: Create new document upload draft
      operationId: createDocumentUploadDraft
      description: |
        Creates an document upload draft record. The draft will associate the uncommitted attachments
        (uploaded earlier) using the provided document upload ID.
      requestBody:
        description: Document upload draft details. The client supplies the ID.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload draft created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/partner/document/draft/{documentUploadId}:
    put:
      tags:
        - partnerDocument
      summary: Update document upload draft
      operationId: updateDocumentUploadDraft
      description: Update the details of an existing document upload draft.
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload draft ID.
          schema:
            type: string
            format: uuid
      requestBody:
        description: Updated draft details.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload draft updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/partner/document/{documentUploadId}:
    get:
      tags:
        - partnerDocument
      summary: Retrieve document upload details
      operationId: getDocumentUpload
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Document upload details retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
    delete:
      tags:
        - partnerDocument
      summary: Remove document upload
      operationId: removeDocumentUpload
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Document upload removed successfully.
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/partner/document/create-and-submit:
    post:
      tags:
        - partnerDocument
      summary: Create and submit document in a single operation
      operationId: createAndSubmitDocument
      description: |
        Creates a new document upload and immediately submits it in a single operation.
        This combines the functionality of creating a draft and submitting it.
      requestBody:
        description: Document details for creation and submission
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document created and submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/partner/document/submit/{documentUploadId}:
    post:
      tags:
        - partnerDocument
      summary: Submit document upload
      operationId: submitDocumentUpload
      description: |
        Submits the document upload, performing necessary validations (e.g. for a TIG document at least two attachments
        with one of each required document types) and commits the associated attachments.
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The ID of the document upload to be submitted.
          schema:
            type: string
            format: uuid
      requestBody:
        description: Document details for creation and submission
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload submitted successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/partner/document:
    post:
      tags:
        - partnerDocument
      summary: List all document uploads
      operationId: listDocumentUploads
      description: Retrieves a paginated list of document uploads with optional filtering by status or project number.
      parameters:
        - name: status
          in: query
          description: Optional filter to retrieve uploads matching a specific status.
          required: false
          schema:
            $ref: '#/components/schemas/UploadStatusEnum'
        - name: projectNo
          in: query
          description: Optional filter to retrieve uploads for a specific project.
          required: false
          schema:
            type: string
            nullable: true
        - name: page
          in: query
          description: Optional page number for pagination; defaults to 1.
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            nullable: true
        - name: pageSize
          in: query
          description: Optional number of items per page for pagination; defaults to 20.
          required: false
          schema:
            type: integer
            minimum: 1
            default: 20
            nullable: true
        - name: searchTerm
          in: query
          description: Optional search term to filter document uploads by project number or comment or id.
          required: false
          schema:
            type: string
            nullable: true
        - name: fromDate
          in: query
          description: Start date for filtering document uploads by creation date (inclusive)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: toDate
          in: query
          description: End date for filtering document uploads by creation date (inclusive)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: sortBy
          in: query
          schema:
            $ref: '#/components/schemas/SortByEnum'
            nullable: true
        - name: sortOrder
          in: query
          schema:
            $ref: '#/components/schemas/SortOrderEnum'
            nullable: true
      responses:
        '200':
          description: List of document uploads retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaginatedDocumentsResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/partner/document/counts:
    post:
      tags:
        - partnerDocument
      summary: Get document upload total counts
      operationId: getDocumentUploadTotalCounts
      description: Retrieves the counts of document uploads for Draft, Submitted and Total.
      parameters:
        - name: projectNo
          in: query
          description: Optional filter to retrieve counts for a specific project.
          required: false
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: List of document uploads retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDocumentUploadTotalCountsResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document/date-of-arrival:
    get:
      tags:
        - secretaryDocument
      summary: Get date of arrival for secretary
      operationId: getSecretaryDocumentDateOfArrival
      description: Retrieves the date of arrival for secretary user.
      responses:
        '200':
          description: Date of arrival retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryDateOfArrivalDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document/draft:
    post:
      tags:
        - secretaryDocument
      summary: Create new document upload draft for secretary
      operationId: createSecretaryDocumentUploadDraft
      description: |
        Creates an document upload draft record for secretary user.
      requestBody:
        description: Document upload draft details. The client supplies the ID.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSecretaryDocumentUploadDraftRequest'
      responses:
        '204':
          description: Document upload draft created successfully.
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document/draft/{documentUploadId}:
    put:
      tags:
        - secretaryDocument
      summary: Update document upload draft for secretary
      operationId: updateSecretaryDocumentUploadDraft
      description: Update the details of an existing document upload draft for secretary user.
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload draft ID.
          schema:
            type: string
            format: uuid
      requestBody:
        description: Updated draft details.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSecretaryDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload draft updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document/{documentUploadId}:
    get:
      tags:
        - secretaryDocument
      summary: Retrieve document upload details for secretary
      operationId: getSecretaryDocumentUpload
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Document upload details retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryDocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
    delete:
      tags:
        - secretaryDocument
      summary: Remove document upload for secretary
      operationId: removeSecretaryDocumentUpload
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Document upload removed successfully.
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document/create-and-submit:
    post:
      tags:
        - secretaryDocument
      summary: Create and submit document in a single operation for secretary
      operationId: createAndSubmitSecretaryDocument
      description: |
        Creates a new document upload and immediately submits it in a single operation for secretary user.
        This combines the functionality of creating a draft and submitting it.
      requestBody:
        description: Document details for creation and submission
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSecretaryDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document created and submitted successfully
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document/submit/{documentUploadId}:
    post:
      tags:
        - secretaryDocument
      summary: Submit document upload for secretary
      operationId: submitSecretaryDocumentUpload
      description: |
        Submits the document upload and commits the associated attachments. For secretary user.
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The ID of the document upload to be submitted.
          schema:
            type: string
            format: uuid
      requestBody:
        description: Document details for creation and submission
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSecretaryDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload submitted successfully.
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document:
    post:
      tags:
        - secretaryDocument
      summary: List secretary documents
      operationId: listSecretaryDocuments
      description: Retrieves a paginated list of secretary documents with optional filtering and sorting.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/TableResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/SecretaryDocumentListDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/document/counts:
    post:
      tags:
        - secretaryDocument
      summary: Get document upload total counts for secretary
      operationId: getSecretaryDocumentUploadTotalCounts
      description: Retrieves the counts of document uploads for Draft, Submitted and Total for secretary user.
      parameters:
        - name: projectNo
          in: query
          description: Optional filter to retrieve counts for a specific project.
          required: false
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: List of document uploads retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDocumentUploadTotalCountsResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/partner-documents:
    post:
      tags:
        - secretaryDocument
      summary: List partner documents
      operationId: listPartnerDocuments
      description: Retrieves a paginated list of partner documents with optional filtering and sorting.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/TableResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/SecretaryPartnerDocumentListDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/partner-documents/{documentUploadId}:
    get:
      tags:
        - secretaryDocument
      summary: Get partner document details
      operationId: getPartnerDocumentDetails
      description: Retrieves the details of a specific partner document.
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryPartnerDocumentDetailDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/manual-document/draft:
    post:
      tags:
        - secretaryDocument
      summary: Create new manual document upload draft
      operationId: createManualDocumentUploadDraft
      description: |
        Creates an document upload draft record. The draft will associate the uncommitted attachments
        (uploaded earlier) using the provided document upload ID.
      requestBody:
        description: Document upload draft details. The client supplies the ID.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSecretaryManualDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload draft created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryManualDocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/manual-document/draft/{documentUploadId}:
    put:
      tags:
        - secretaryDocument
      summary: Update manual document upload draft
      operationId: updateManualDocumentUploadDraft
      description: Update the details of an existing document upload draft.
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload draft ID.
          schema:
            type: string
            format: uuid
      requestBody:
        description: Updated draft details.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSecretaryManualDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload draft updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryManualDocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/manual-document/create-and-submit:
    post:
      tags:
        - secretaryDocument
      summary: Create and submit manual document in a single operation
      operationId: createAndSubmitManualDocument
      description: |
        Creates a new document upload and immediately submits it in a single operation.
        This combines the functionality of creating a draft and submitting it.
      requestBody:
        description: Document details for creation and submission
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSecretaryManualDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document created and submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryManualDocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/manual-document/submit/{documentUploadId}:
    post:
      tags:
        - secretaryDocument
      summary: Submit manual document upload
      operationId: submitManualDocumentUpload
      description: |
        Submits the document upload, performing necessary validations (e.g. for a TIG document at least two attachments
        with one of each required document types) and commits the associated attachments.
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The ID of the document upload to be submitted.
          schema:
            type: string
            format: uuid
      requestBody:
        description: Document details for creation and submission
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSecretaryManualDocumentUploadDraftRequest'
      responses:
        '200':
          description: Document upload submitted successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryManualDocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /documents/secretary/manual-document/{documentUploadId}:
    get:
      tags:
        - secretaryDocument
      summary: Retrieve document upload details
      operationId: getSecretaryManualDocumentUpload
      parameters:
        - name: documentUploadId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Document upload details retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecretaryManualDocumentUploadDto'
        default:
          $ref: '#/components/responses/ErrorResponse'
  /admin/calendar/{year}:
    get:
      tags:
        - calendarManagement
      summary: Get calendar configuration for a specific year
      description: Retrieves both weekday schedules and unusual workdays for the specified year
      parameters:
        - name: year
          in: path
          description: Year to retrieve calendar configuration for
          required: true
          schema:
            type: integer
            minimum: 2025
            maximum: 2100
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/YearlyCalendarConfigDto'
        '404':
          description: Configuration for year not found
  /admin/calendar/weekday-schedules/{year}:
    put:
      tags:
        - calendarManagement
      summary: Save or update weekday schedules for a year
      description: Saves or updates the weekday schedules for the specified year
      parameters:
        - name: year
          in: path
          description: Year to save calendar configuration for
          required: true
          schema:
            type: integer
            minimum: 2025
            maximum: 2100
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/YearlyCalendarConfigDto'
      responses:
        '204':
          description: Configuration saved successfully
        '400':
          description: Invalid input
  /admin/calendar/unusual-workdays/{year}:
    put:
      tags:
        - calendarManagement
      summary: Save or update unusual workdays for a year
      description: Saves or updates the unusual workdays for the specified year
      parameters:
        - name: year
          in: path
          description: Year to save calendar configuration for
          required: true
          schema:
            type: integer
            minimum: 2025
            maximum: 2100
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/YearlyCalendarConfigDto'
      responses:
        '204':
          description: Configuration saved successfully
        '400':
          description: Invalid input
  /partners:
    get:
      tags: 
        - partners
      summary: Partner adatok lekérdezése
      operationId: getPartners
      parameters:
        - name: searchTerm
          in: query
          description: Optional search term to filter partners by different fields
          required: false
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: Az összes partner lekérdezése Titkárok számára
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPartnersResponse'
        default:
          description: error occurred - see status code and problem object for more information.
          content:
            application/problem+json:
              schema:
                $ref: 'https://opensource.zalando.com/restful-api-guidelines/problem-1.0.1.yaml#/Problem'
  /admin/document-status-report:
    get:
      tags:
        - documentReport
      summary: Get simplified document status reports
      operationId: getDocumentStatusReports
      description: Retrieves a simplified list of documents with basic information for dashboard table
      parameters:
        - name: fromDate
          in: query
          description: Start date for filtering documents by creation date (inclusive)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: toDate
          in: query
          description: End date for filtering documents by creation date (inclusive)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: page
          in: query
          description: Page number for pagination.
          required: false
          schema:
            type: integer
            minimum: 1
            nullable: true
        - name: pageSize
          in: query
          description: Number of items per page for pagination.
          required: false
          schema:
            type: integer
            minimum: 1
            nullable: true
        - name: spIdentifier
          in: query
          description: Optional SP identifier to filter documents by SP identifier.
          required: false
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: List of simplified document status reports retrieved successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SimpleDocumentStatusReportDto'
                  totalRecords:
                    type: integer
                    description: Total number of records.
        default:
          $ref: '#/components/responses/ErrorResponse'

  /admin/document-status-report/{documentId}:
    get:
      tags:
        - documentReport
      summary: Get document workflow status by ID
      operationId: getDocumentStatusReportById
      description: Retrieves detailed workflow status for a specific document
      parameters:
        - name: documentId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Document workflow status retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentStatusReportDto'
        '404':
          description: Document not found.
        default:
          $ref: '#/components/responses/ErrorResponse'
          
  /admin/document-status-report/download-merged/{documentId}/{attachmentDocumentTypeId}:
    get:
      tags:
        - documentReport
      summary: Download document attachment
      operationId: downloadMergedDocument
      description: Downloads a specific attachment from a document
      parameters:
        - name: documentId
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: string
            format: uuid
        - name: attachmentDocumentTypeId
          in: path
          required: true
          description: The attachment document type ID.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: merged file tartalma
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '404':
          description: Document or attachment not found.
        default:
          $ref: '#/components/responses/ErrorResponse'

  /admin/retry-validation/{id}:
    post:
      tags:
        - documentReport
      summary: Retry validation for a document
      operationId: retryValidation
      description: Retries validation for a specific document
      parameters:
        - name: id
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Validation retried successfully.
        '404':
          description: Document not found.
        default:
          $ref: '#/components/responses/ErrorResponse'

  /admin/retry-analysis/{id}:
    post:
      tags:
        - documentReport
      summary: Retry analysis for a document
      operationId: retryAnalysis
      description: Retries analysis for a specific document
      parameters:
        - name: id
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Analysis retried successfully.
        '404':
          description: Document not found.
        default:
          $ref: '#/components/responses/ErrorResponse'

  /admin/retry-elo/{id}:
    post:
      tags:
        - documentReport
      summary: Retry ELO for a document
      operationId: retryElo
      description: Retries ELO for a specific document
      parameters:
        - name: id
          in: path
          required: true
          description: The document upload ID.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: ELO retried successfully.
        '404':
          description: Document not found.
        default:
          $ref: '#/components/responses/ErrorResponse'

  /admin/retry-all-validation:
    post:
      tags:
        - documentReport
      summary: Retry all validation
      operationId: retryAllValidation
      description: Retries all validation for all documents
      responses:
        '200':
          description: Validation retried successfully.
        default:
          $ref: '#/components/responses/ErrorResponse'
  /admin/download-based-on-sp:
    post:
      tags:
        - documentReport
      summary: Download based on SP
      operationId: downloadBasedOnSp
      description: Downloads documents based on multiple SP identifiers and returns a zip file
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                spIdentifiers:
                  type: array
                  items:
                    type: string
                  description: Array of SP identifiers to download documents for
              required:
                - spIdentifiers
      responses:
        '200':
          description: Zip file containing documents for the specified SP identifiers
          content:
            application/zip:
              schema:
                type: string
                format: binary
        default:
          $ref: '#/components/responses/ErrorResponse'

components:
  schemas:
    SimpleDocumentStatusReportDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier.
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        documentType:
          type: string
          enum:
            - Partner
            - Secretary
          description: Type of document - Partner or Secretary document.
      required:
        - id
        - createdDate
        - documentType

    DocumentStatusReportDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier.
        isSecretaryDocument:
          type: boolean
          description: Whether the document is a secretary document.
        secretaryUploadType:
          type: string
          description: Type of secretary document upload.
        contactNo:
          type: string
          description: Identifier of the associated partner.
          nullable: true
        projectNo:
          type: string
          description: Identifier of the associated project.
          nullable: true
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
          nullable: true
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        dateOfArrival:
          type: string
          format: date-time
          description: Érkeztetés dátuma.
          nullable: true
        secretaryDateOfArrival:
          type: string
          format: date-time
          description: Titkár által beállított érkeztetés dátuma.
          nullable: true
        uploadStatus:
          $ref: '#/components/schemas/UploadStatusEnum'
          description: The status of the document upload.
        totalAttachments:
          type: integer
          description: Total number of attachments in the document.
        analyzedDocuments:
          type: integer
          description: Number of documents that have been successfully analyzed.
        pendingAnalysisDocuments:
          type: integer
          description: Number of documents pending analysis.
        failedAnalysisDocuments:
          type: integer
          description: Number of documents where analysis failed.
        validatedDocuments:
          type: integer
          description: Number of documents that have been successfully validated.
        pendingValidationDocuments:
          type: integer
          description: Number of documents pending validation.
        failedValidationDocuments:
          type: integer
          description: Number of documents where validation failed.
        successEloDocuments:
          type: integer
          description: Number of documents where ELO has been done successfully.
        pendingEloDocuments:
          type: integer
          description: Number of documents where ELO is pending.
        failedEloDocuments:
          type: integer
          description: Number of documents where ELO has failed.
        analysisDetails:
          type: array
          description: Detailed analysis status for each attachment.
          items:
            $ref: '#/components/schemas/DocumentAnalysisDetailDto'
        validationDetails:
          type: array
          description: Detailed validation status for each attachment.
          items:
            $ref: '#/components/schemas/DocumentValidationDetailDto'
        eloDetails:
          type: array
          description: Detailed ELO status for each attachment.
          items:
            $ref: '#/components/schemas/DocumentEloDetailDto'
        classificationDetails:
          type: array
          description: Detailed classification status for each attachment.
          items:
            $ref: '#/components/schemas/DocumentClassificationDetailDto'
      required:
        - id
        - uploadStatus
        - processingStatus

    DocumentClassificationDetailDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the classification result.
        documentId:
          type: string
          format: uuid
          description: ID of the document.
        processingSourceId:
          type: integer
          description: ID of the attachment document type.
        documentType:
          $ref: '#/components/schemas/DocumentType'
          description: Type of document.
          nullable: true
        attachmentName:
          type: string
          description: Name of the attachment file.
        attachmentId:
          type: integer
          description: ID of the attachment.
        status:
          $ref: '#/components/schemas/DocumentClassificationStatusEnum'
          description: Status of the document classification.
        errorMessage:
          type: string
          description: Error message if classification failed.
          nullable: true
        retryCount:
          type: integer
          description: Number of times classification has been retried.
        classificationResultJson:
          type: string
          description: Classification result JSON.
          nullable: true
    
    DocumentEloDetailDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the ELO result.
        documentId:
          type: string
          format: uuid
          description: ID of the document.
        processingSourceId:
          type: integer
          description: ID of the attachment document type.
        attachmentName:
          type: string
          description: Name of the attachment file.
        attachmentId:
          type: integer
          description: ID of the attachment.
        status:
          $ref: '#/components/schemas/DocumentEloStatusEnum'
          description: Status of the document ELO.
        errorMessage:
          type: string
          description: Error message if ELO failed.
          nullable: true
        retryCount:
          type: integer
          description: Number of times ELO has been retried.
        createRequestXml:
          type: string
          description: Request XML sent to ELO.
        createResponseXml:
          type: string
          description: Response XML received from ELO.
        uploadRequestXml:
          type: string
          description: Request XML sent to ELO.
        uploadResponseXml:
          type: string
          description: Response XML received from ELO.
        workflowRequestXml:
          type: string
          description: Request XML sent to ELO.
        workflowResponseXml:
          type: string
          description: Response XML received from ELO.
          
    DocumentAnalysisDetailDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the analysis result.
        processingSourceId:
          type: integer
          description: ID of the attachment document type.
        documentType:
          $ref: '#/components/schemas/DocumentType'
          description: Type of document.
          nullable: true
        attachmentName:
          type: string
          description: Name of the attachment file.
        attachmentId:
          type: integer
          description: ID of the attachment.
        status:
          $ref: '#/components/schemas/DocumentAnalysisStatusEnum'
          description: Status of the document analysis.
        errorMessage:
          type: string
          description: Error message if analysis failed.
          nullable: true
        retryCount:
          type: integer
          description: Number of times analysis has been retried.
        azureDocumentAiResultJson:
          type: string
          description: Azure Document AI result JSON.
          nullable: true
      required:
        - id
        - attachmentDocumentTypeId
        - documentType
        - attachmentName
        - status

    DocumentValidationDetailDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the validation result.
        processingSourceId:
          type: integer
          description: ID of the attachment document type.
        documentType:
          $ref: '#/components/schemas/DocumentType'
          description: Type of document.
          nullable: true
        attachmentName:
          type: string
          description: Name of the attachment file.
        attachmentId:
          type: integer
          description: ID of the attachment.
        status:
          $ref: '#/components/schemas/DocumentValidationStatusEnum'
          description: Status of the document validation.
        errorMessage:
          type: string
          description: Error message if validation failed.
          nullable: true
        validationResultJson:
          type: array
          items:
            $ref: '#/components/schemas/ValidationJsonDto'
          nullable: true
        retryCount:
          type: integer
          description: Number of times validation has been retried.
      required:
        - id
        - attachmentDocumentTypeId
        - documentType
        - attachmentName
        - status

    DocumentAnalysisStatusEnum:
      type: string
      description: The status of document analysis.
      enum:
        - Pending
        - Processing
        - Success
        - Failed
        - Timeout
    
    DocumentClassificationStatusEnum:
      type: string
      description: The status of document classification.
      enum:
        - Pending
        - Processing
        - Success
        - Failed
        - Timeout

    DocumentValidationStatusEnum:
      type: string
      description: The status of document validation.
      enum:
        - Pending
        - Processing
        - Success
        - TechnicalError
        - UnknownDocumentType
        - BcFailed

    DocumentEloStatusEnum:
      type: string
      description: The status of the document ELO.
      enum:
        - Pending
        - Processing
        - Success
        - Failed
    GetDocumentUploadTotalCountsResponse:
      type: object
      properties:
        total:
          type: integer
          description: Total number of document uploads.
        draft:
          type: integer
          description: Number of document uploads in draft status.
        submitted:
          type: integer
          description: Number of document uploads in submitted status.
    GetSecretaryDocumentUploadTotalCountsResponse:
      type: object
      properties:
        total:
          type: integer
          description: Total number of document uploads.
        draft:
          type: integer
          description: Number of document uploads in draft status.
        submitted:
          type: integer
          description: Number of document uploads in submitted status.
          
    DocumentUploadQueryRequest:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/UploadStatusEnum'
          description: Optional filter to retrieve uploads matching a specific status.
          nullable: true
        projectNo:
          type: string
          description: Optional filter to retrieve uploads for a specific project.
          nullable: true
        page:
          type: integer
          minimum: 1
          description: Page number for pagination.
          nullable: true
        pageSize:
          type: integer
          minimum: 1
          description: Number of items per page for pagination.
          nullable: true
    SecretaryDocumentUploadQueryRequest:
      type: object
      properties:
        contactNo:
          type: string
          description: Identifier of the associated partner.
          nullable: true
        projectNo:
          type: string
          description: Optional filter to retrieve uploads for a specific project.
          nullable: true
        page:
          type: integer
          minimum: 1
          description: Page number for pagination.
          nullable: true
        pageSize:
          type: integer
          minimum: 1
          description: Number of items per page for pagination.
          nullable: true
    DocumentUploadDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier (generated on the frontend).
        userId:
          type: integer
          description: Identifier of the uploading user.
        projectNo:
          type: string
          description: Identifier of the associated project.
          nullable: true
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
          nullable: true
        type:
          $ref: '#/components/schemas/AccountTypeEnum'
          nullable: true
        comment:
          type: string
          description: Optional comment.
        status:
          $ref: '#/components/schemas/UploadStatusEnum'
        processingStatus:
          $ref: '#/components/schemas/ProcessingStatusEnum'
          description: The status of the document processing.
          nullable: true
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/AttachmentDocumentTypeDto'
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        rowVersion:
          type: string
          format: byte
          description: The row version of the document upload.
      required:
        - id
        - userId
        - type
        - status
    SecretaryPartnerDocumentListDto: 
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier (generated on the frontend).
        spIdentifiers:
          type: array
          description: S/P azonosítók.
          items:
            type: string
          nullable: true
        partner:
          type: string
          description: Computed - ProfileName + VatNumber.
          nullable: true
        partnerContactNo:
          type: string
          description: Partner contact number.
          nullable: true
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        dateOfArrival:
          type: string
          format: date-time
          description: The date of arrival of the document.
          nullable: true
        processingStatus:
          $ref: '#/components/schemas/ProcessingStatusEnum'
          description: Computed status of the processing based on Analysis and Validation and ELO results.
          nullable: true
    SecretaryDocumentListDto: 
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier (generated on the frontend).
        spIdentifiers:
          type: array
          description: S/P azonosítók.
          items:
            type: string
          nullable: true
        contactName:
          type: string
          description: Computed - ProfileName + VatNumber.
          nullable: true
        uploader:
          type: string
          description: Name of the uploader.
          nullable: true
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        dateOfArrival:
          type: string
          format: date-time
          description: The date of arrival of the document.
          nullable: true
        status:
          $ref: '#/components/schemas/UploadStatusEnum'
        uploadType:
          $ref: '#/components/schemas/SecretaryUploadTypeEnum'
          description: The type of document upload.
          nullable: true
        processingStatus:
          $ref: '#/components/schemas/ProcessingStatusEnum'
          description: The status of the document processing.
          nullable: true
    SecretaryPartnerDocumentDetailDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier (generated on the frontend).
        userId:
          type: integer
          description: Identifier of the uploading user.
        projectNo:
          type: string
          description: Identifier of the associated project.
          nullable: true
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
          nullable: true
        partner:
          type: string
          description: Computed - ProfileName + VatNumber.
          nullable: true
        partnerContactNo:
          type: string
          description: Partner contact number.
        type:
          $ref: '#/components/schemas/AccountTypeEnum'
          nullable: true
        comment:
          type: string
          description: Optional comment.
        status:
          $ref: '#/components/schemas/UploadStatusEnum'
        dateOfArrival:
          type: string
          format: date-time
          description: Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla,
          nullable: true
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/SecretaryManualAttachmentDocumentTypeDto'
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        lastModDate:
          type: string
          format: date-time
          description: The date and time when the document upload was last updated.
    SecretaryDocumentUploadDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier (generated on the frontend).
        userId:
          type: integer
          description: Identifier of the uploading user.
        profileName:
          type: string
          description: Name of the uploading user.
          nullable: true
        contactNo:
          type: string
          description: Identifier of the associated partner.
          nullable: true
        contactName:
          type: string
          description: Name of the associated partner.
          nullable: true
        projectNo:
          type: string
          description: Identifier of the associated project.
          nullable: true
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
          nullable: true
        comment:
          type: string
          description: Optional comment.
        status:
          $ref: '#/components/schemas/UploadStatusEnum'
        dateOfArrival:
          type: string
          format: date-time
          description: Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla
          nullable: true
        secretaryDateOfArrival:
          type: string
          format: date-time
          description: Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla
          nullable: true
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/SecretaryAttachmentDocumentTypeDto'
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        lastModDate:
          type: string
          format: date-time
          description: The date and time when the document upload was last updated.
        rowVersion:
          type: string
          format: byte
          description: The row version of the document upload.
        processingStatus:
          $ref: '#/components/schemas/ProcessingStatusEnum'
          description: The status of the document processing.
          nullable: true
      required:
        - id
        - userId
        - status
    SecretaryManualDocumentUploadDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Document upload identifier (generated on the frontend).
        userId:
          type: integer
          description: Identifier of the uploading user.
        profileName:
          type: string
          description: Name of the uploading user.
          nullable: true
        type:
          $ref: '#/components/schemas/AccountTypeEnum'
          nullable: true
        contactNo:
          type: string
          description: Identifier of the associated partner.
          nullable: true
        contactName:
          type: string
          description: Name of the associated partner.
          nullable: true
        projectNo:
          type: string
          description: Identifier of the associated project.
          nullable: true
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
          nullable: true
        comment:
          type: string
          description: Optional comment.
        status:
          $ref: '#/components/schemas/UploadStatusEnum'
        dateOfArrival:
          type: string
          format: date-time
          description: Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla
          nullable: true
        secretaryDateOfArrival:
          type: string
          format: date-time
          description: Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla
          nullable: true
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/SecretaryManualAttachmentDocumentTypeDto'
        createdDate:
          type: string
          format: date-time
          description: The date and time when the document upload was created.
        lastModDate:
          type: string
          format: date-time
          description: The date and time when the document upload was last updated.
        rowVersion:
          type: string
          format: byte
          description: The row version of the document upload.
        processingStatus:
          $ref: '#/components/schemas/ProcessingStatusEnum'
          description: The status of the document processing.
          nullable: true
      required:
        - id
        - userId
        - status
    ProcessingStatusEnum:
      type: string
      description: The status of the document processing.
      enum:
        - Success
        - Default
        - Failed
        - Processing
    CreateDocumentUploadDraftRequest:
      type: object
      properties:
        contextId:
          type: string
          format: uuid
          description: Document upload identifier generated on the frontend.
        projectNo:
          type: string
          description: Identifier of the associated project.
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
        type:
          $ref: '#/components/schemas/AccountTypeEnum'
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/AttachmentDocumentTypeDto'
        comment:
          type: string
          description: Optional comment.
      required:
        - contextId
        - type
        - attachmentDocumentTypes
    CreateSecretaryManualDocumentUploadDraftRequest:
      type: object
      properties:
        contextId:
          type: string
          format: uuid
          description: Document upload identifier generated on the frontend.
        contactNo:
          type: string
          description: Identifier of the associated partner.
          nullable: true
        contactName:
          type: string
          description: Name of the associated partner.
          nullable: true
        projectNo:
          type: string
          description: Identifier of the associated project.
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
        type:
          $ref: '#/components/schemas/AccountTypeEnum'
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/AttachmentDocumentTypeDto'
        secretaryDateOfArrival:
          type: string
          format: date-time
          description: Arrival date of the document.
          nullable: true
        dateOfArrival:
          type: string
          format: date-time
          description: Arrival date of the document.
          nullable: true
        comment:
          type: string
          description: Optional comment.
      required:
        - contextId
        - type
        - attachmentDocumentTypes
    SecretaryDateOfArrivalDto:
      type: object
      properties:
        dateOfArrival:
          type: string
          format: date-time
          description: Érkeztetés dátuma.
      required:
        - dateOfArrival
    CreateSecretaryDocumentUploadDraftRequest:
      type: object
      properties:
        contextId:
          type: string
          format: uuid
          description: Document upload identifier generated on the frontend.
        contactNo: 
          type: string
          description: Identifier of the associated partner.
          nullable: true
        contactName:
          type: string
          description: Name of the associated partner.
          nullable: true
        projectNo:
          type: string
          description: Identifier of the associated project.
          nullable: true
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
          nullable: true
        dateOfArrival:
          type: string
          format: date-time
          description: Arrival date of the document.
          nullable: true
        secretaryDateOfArrival:
          type: string
          format: date-time
          description: Arrival date of the document.
          nullable: true
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/SecretaryAttachmentDocumentTypeDto'
        secretaryUploadType:
          $ref: '#/components/schemas/SecretaryUploadTypeEnum'
        comment:
          type: string
          description: Optional comment.
          nullable: true
      required:
        - contextId
        - attachmentDocumentTypes
    UpdateDocumentUploadDraftRequest:
      type: object
      properties:
        projectNo:
          type: string
        projectDescription:
          type: string
          description: Description of the associated project.
        contractNo:
          type: string
        type:
          $ref: '#/components/schemas/AccountTypeEnum'
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/AttachmentDocumentTypeDto'
        comment:
          type: string
          description: Optional comment.
        rowVersion:
          type: string
          format: byte
          description: The row version of the document upload.
      required:
        - type
        - attachmentDocumentTypes
    UpdateSecretaryManualDocumentUploadDraftRequest:
      type: object
      properties:
        projectNo:
          type: string
        projectDescription:
          type: string
          description: Description of the associated project.
        contractNo:
          type: string
        type:
          $ref: '#/components/schemas/AccountTypeEnum'
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/AttachmentDocumentTypeDto'
        secretaryDateOfArrival:
          type: string
          format: date-time
          description: Arrival date of the document.
          nullable: true
        contactNo:
          type: string
          description: Identifier of the associated partner.
          nullable: true
        contactName:
          type: string
          description: Name of the associated partner.
          nullable: true
        comment:
          type: string
          description: Optional comment.
        rowVersion:
          type: string
          format: byte
          description: The row version of the document upload.
      required:
        - type
        - attachmentDocumentTypes
    UpdateSecretaryDocumentUploadDraftRequest:
      type: object
      properties:
        contactNo:
          type: string
          description: Identifier of the associated partner.
          nullable: true
        contactName:
          type: string
          description: Name of the associated partner.
          nullable: true
        projectNo:
          type: string
          description: Identifier of the associated project.
          nullable: true
        projectDescription:
          type: string
          description: Description of the associated project.
          nullable: true
        contractNo:
          type: string
          description: Identifier of the associated contract.
          nullable: true
        secretaryDateOfArrival:
          type: string
          format: date-time
          description: Arrival date of the document.
          nullable: true
        attachmentDocumentTypes:
          type: array
          description: List of attachments and their associated document types.
          items:
            $ref: '#/components/schemas/SecretaryAttachmentDocumentTypeDto'
        comment:
          type: string
          description: Optional comment.
          nullable: true
        rowVersion:
          type: string
          format: byte
          description: The row version of the document upload.
      required:
        - attachmentDocumentTypes
    AttachmentDocumentTypeDto:
      type: object
      properties:
        attachment:
          $ref: '../../api-spec-contracts/src/attachment-api.yml#/components/schemas/FileInfo'
        spIdentifier:
          type: string
          description: S/P azonosító. A számla egyedi generált azonosítója mentéskor és beküldés után generáljuk.
          nullable: true
        documentType:
          $ref: '#/components/schemas/DocumentType'
          nullable: true
        processingStatus:
          $ref: '#/components/schemas/EloProcessingStatusEnum'
          description: The status of the ELO processing for this classification.
          nullable: true
      required:
        - attachment
    SecretaryManualAttachmentDocumentTypeDto:
      type: object
      properties:
        attachment:
          $ref: '../../api-spec-contracts/src/attachment-api.yml#/components/schemas/FileInfo'
        spIdentifier:
          type: string
          description: S/P azonosító. A számla egyedi generált azonosítója mentéskor és beküldés után generáljuk.
          nullable: true
        documentType:
          $ref: '#/components/schemas/DocumentType'
          nullable: true
        failureReason:
          type: string
          description: The reason of the processing failure.
          nullable: true
        processingStatus:
          $ref: '#/components/schemas/EloProcessingStatusEnum'
          description: The status of the ELO processing for this classification.
          nullable: true
      required:
        - attachment
    SecretaryAttachmentDocumentTypeDto:
      type: object
      properties:
        attachment:
          $ref: '../../api-spec-contracts/src/attachment-api.yml#/components/schemas/FileInfo'
        classifications:
          type: array
          items:
            $ref: '#/components/schemas/SecretaryClassificationDto'
          nullable: true
      required:
        - attachment
    EloProcessingStatusEnum:
      type: string
      description: The status of the elo processing for a classification.
      enum:
        - Success
        - Failed
        - Default
    SecretaryClassificationDto:
      type: object
      properties:
        spIdentifier:
          type: string
          description: S/P azonosító. A számla egyedi generált azonosítója classification után generáljuk.
          nullable: true
        processingStatus:
          $ref: '#/components/schemas/EloProcessingStatusEnum'
          description: The status of the ELO processing for this classification.
          nullable: true
        failureReason:
          type: string
          description: The reason of the processing failure.
          nullable: true
        documentType:
          $ref: '#/components/schemas/DocumentType'
          nullable: true
        startPage:
          type: integer
          description: The start page of the document.
          nullable: true
        endPage:
          type: integer
          description: The end page of the document.
          nullable: true
    AccountTypeEnum:
      type: string
      description: The type of document upload account.
      enum:
        - TigInvoice
        - NonTigAccount
        - CorrectionInvoice
        - AdvanceInvoice
    UploadStatusEnum:
      type: string
      description: The status of the document upload.
      enum:
        - Draft
        - Submitted
    DocumentType:
      type: string
      description: >
       The type of document attached to the document.
       Invoice - Számla – A legfontosabb dokumentum, amely tartalmazza az eladott termékek vagy szolgáltatások részleteit, az árat és az adókat.
       CompletionCert - Teljesítési igazolás – Igazolja, hogy a szolgáltatás vagy termék ténylegesen teljesült a megállapodás szerint.
       DeliveryNote - Szállítólevél – A kiszállított áruk listáját tartalmazza, amelyet az átvevő aláírhat az átvétel igazolására.
       Waybill - Fuvarlevél (CMR, belföldi fuvarlevél) – A fuvarozás során használatos dokumentum, amely az áruk szállításának részleteit rögzíti.
       Order - Megrendelés – A vevő által kibocsátott dokumentum, amely tartalmazza a megrendelt termékeket vagy szolgáltatásokat és azok feltételeit.
       Contract - Szerződés vagy keretszerződés – A felek közötti megállapodás, amely meghatározza a számlázás alapját és a feltételeket.
       CorrectionInvoice - Helyesbítő számla – A korábban kiállított számla módosítására vagy visszatérítésre szolgáló dokumentum.
       AdvanceInvoice - Előlegszámla / Végszámla – Előlegfizetés esetén az előlegszámla rögzíti a részfizetést, a végszámla pedig a teljes összeget.
       ReceiptConfirmation - Átvételi elismervény – Igazolja, hogy a terméket vagy szolgáltatást átvették és elfogadták.
       Other - Egyéb
      enum:
        - Invoice
        - CompletionCert
        - DeliveryNote
        - Waybill
        - Order
        - Contract
        - CorrectionInvoice
        - AdvanceInvoice
        - ReceiptConfirmation
        - Other
    SortOrderEnum:
      type: string
      description: The direction of the sort.
      enum:
        - asc
        - desc
    SortByEnum:
      type: string
      description: The field to sort by. Extend it when needed.
      enum:
        - createdDate
    GetDocumentsResponse:
      properties:
        documents:
          type: array
          items:
            $ref: '#/components/schemas/DocumentUploadDto'
    TableRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: Page number (1-based)
          example: 1
        pageSize:
          type: integer
          minimum: 1
          maximum: 100
          default: 10
          description: Number of records per page
          example: 10
        globalFilter:
          type: string
          nullable: true
          description: Global search term applied across searchable fields
          example: "john"
        columnFilters:
          type: array
          items:
            $ref: '#/components/schemas/ColumnFilter'
          description: Column-specific filters
        sortFields:
          type: array
          items:
            $ref: '#/components/schemas/SortField'
          description: Sorting configuration
      required:
        - page
        - pageSize

    ColumnFilter:
      type: object
      properties:
        field:
          type: string
          description: Field name to filter on (supports nested properties like 'country.name')
          example: "status"
        matchMode:
          type: string
          enum: [contains, equals, startsWith, endsWith, lt, gt, in, dateIs, dateIsNot, dateBefore, dateAfter]
          default: "contains"
          description: Filter match mode
          example: "equals"
        value:
          type: object
          nullable: true
          description: Filter value (can be string, number, boolean, or array for 'in' matchMode)
          example: "qualified"
      required:
        - field
        - matchMode

    SortField:
      type: object
      properties:
        field:
          type: string
          description: Field name to sort by (supports nested properties)
          example: "name"
        order:
          type: string
          enum: [asc, desc]
          default: "asc"
          description: Sort direction
          example: "asc"
      required:
        - field
        - order

    TableResponse:
      type: object
      properties:
        data:
          type: array
          items: {}
          description: Array of records for current page
        totalRecords:
          type: integer
          description: Total number of records matching the filters
          example: 247
        page:
          type: integer
          description: Current page number
          example: 1
        pageSize:
          type: integer
          description: Number of records per page
          example: 10
      required:
        - data
        - totalRecords
        - page
        - pageSize

    GetPaginatedDocumentsResponse:
      properties:
        documents:
          type: array
          items:
            $ref: '#/components/schemas/DocumentUploadDto'
        totalCount:
          type: integer
          description: Total number of documents.
        page:
          type: integer
          description: Current page number.
        pageSize:
          type: integer
    YearlyCalendarConfigDto:
      type: object
      description: Complete calendar configuration for a specific year
      properties:
        year:
          type: integer
          format: int32
          example: 2025
        mondayStartTime:
          pattern: ^\d{2}:\d{2}$
          example: "08:00"
          type: string
          format: timeOnly
        mondayEndTime:
          pattern: ^\d{2}:\d{2}$
          example: "17:00"
          type: string
          format: timeOnly
        mondayIsWorkDay:
          type: boolean
          example: true
        tuesdayStartTime:
          pattern: ^\d{2}:\d{2}$
          example: "08:00"
          type: string
        tuesdayEndTime:
          pattern: ^\d{2}:\d{2}$
          example: "17:00"
          type: string
        tuesdayIsWorkDay:
          type: boolean
          example: true
        wednesdayStartTime:
          pattern: ^\d{2}:\d{2}$
          example: "08:00"
          type: string
        wednesdayEndTime:
          pattern: ^\d{2}:\d{2}$
          example: "17:00"
          type: string
        wednesdayIsWorkDay:
          type: boolean
          example: true
        thursdayStartTime:
          pattern: ^\d{2}:\d{2}$
          example: "08:00"
          type: string
        thursdayEndTime:
          pattern: ^\d{2}:\d{2}$
          example: "17:00"
          type: string
        thursdayIsWorkDay:
          type: boolean
          example: true
        fridayStartTime:
          pattern: ^\d{2}:\d{2}$
          example: "08:00"
          type: string
        fridayEndTime:
          pattern: ^\d{2}:\d{2}$
          example: "17:00"
          type: string
        fridayIsWorkDay:
          type: boolean
          example: true
        saturdayStartTime:
          pattern: ^\d{2}:\d{2}$
          example: "08:00"
          type: string
        saturdayEndTime:
          pattern: ^\d{2}:\d{2}$
          example: "17:00"
          type: string
        saturdayIsWorkDay:
          type: boolean
          example: false
        sundayStartTime:
          pattern: ^\d{2}:\d{2}$
          example: "08:00"
          type: string
        sundayEndTime:
          pattern: ^\d{2}:\d{2}$
          example: "17:00"
          type: string
        sundayIsWorkDay:
          type: boolean
          example: false
        unusualWorkdays:
          type: array
          items:
            $ref: '#/components/schemas/UnusualWorkdayDto'
        rowVersion:
          type: string
          format: byte
          description: The row version of the yearly calendar configuration.
      required:
        - year
        - weekdaySchedules
        - unusualWorkdays
        
    UnusualWorkdayDto:
      type: object
      properties:
        date:
          type: string
          format: date
          example: "2025-05-01"
        isWorkDay:
          type: boolean
          description: Whether this is a workday (true) or a day off (false)
        startTime:
          type: string
          format: time
          nullable: true
          example: "08:00"
          description: Required only if isWorkDay is true
        endTime:
          type: string
          format: time
          nullable: true
          example: "13:00"
          description: Required only if isWorkDay is true
        comment:
          type: string
          example: "Labor Day"
      required:
        - date
        - isWorkDay
    GetPartnersResponse:
      properties:
        partners:
          type: array
          items:
            $ref: '../../api-spec-useradmin/src/useradmin-api.yml#/components/schemas/Partner'

    ValidationJsonDto:
      type: object
      properties:
        eloColumnName:
          type: string
          description: Name of the column being validated
          example: "VendorName"
        status:
          $ref: '#/components/schemas/ColumnStatus'
          example: "Ok"
        bcColumnValue:
          type: string
          nullable: true
          description: Value from the Business Central system
        documentAiColumnValue:
          type: string
          nullable: true
          description: Value extracted from the document by AI
        navColumnValue:
          type: string
          nullable: true
          description: Value from the NAV system
        valueOnly:
          type: string
          nullable: true
          description: Value only
      required:
        - eloColumnName
        - status
    SecretaryUploadTypeEnum:
      type: string
      enum:
        - PerCase
        - Batch
        - Manual
      description: The type of document upload.
    ColumnStatus:
      type: string
      enum:
        - Ok
        - Discrepancy
        - Missing
        - Warning
        - Info
        - NavDiscrepancy
        - Navsource
      description: |
        Status of column validation:
        * `Ok` - The value is correct
        * `Discrepancy` - There is a discrepancy between the two values
        * `Missing` - Value not found in document or no matching value in data source
        * `Warning` - There is a warning
        * `Info` - There is an info
        * `NavDiscrepancy` - There is a discrepancy between the two values from NAV
        * `Navsource` - The value is from NAV
  responses:
    ErrorResponse:
      description: error occurred - see status code and problem object for more information.
      content:
        application/problem+json:
          schema:
            $ref: 'https://opensource.zalando.com/restful-api-guidelines/problem-1.0.1.yaml#/Problem'
  parameters:
    context:
      name: contextObjectId
      in: path
      required: true
      description: a csatolmányok kontextusaként használni kívánt objektum - hoszt alkalmazásbeli azonosítója
      schema:
        type: string
        format: uuid
  securitySchemes:  # see https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.1.0.md#securitySchemeObject
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []
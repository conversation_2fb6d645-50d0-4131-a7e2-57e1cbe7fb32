using Microsoft.AspNetCore.Mvc.ModelBinding;
using PartnerPortal.Backend.Contracts.ContractsApiStub.Models;
using FileInfo = PartnerPortal.Backend.Contracts.ContractsApiStub.Models.FileInfo;

namespace PartnerPortal.Backend.Shared.Attachment;

public interface IAttachmentService
{
    // endpoint operations:
    Task<ListFilesResponse> UploadFile(
        string contextObjectId,
        string userId,
        Stream body,
        string contentType,
        ModelStateDictionary modelState,
        bool enableVersioning = true
    );

    Task RevertFile(string contextObjectId, int fileId, string userId);
    Task RevertFiles(string contextObjectId, string userId);
    Task CommitFiles(string contextObjectId, string userId, List<FileInfo> fileInfos);
    Task<ListFilesResponse> ListFiles(string contextObjectId, string userId, bool getStoredName = false);
    Task DeleteFiles(string contextObjectId, string userId);

    // utility for controller:
    Task<PartnerPortalDatabase.Entities.Attachment> GetAttachment(string contextObjectId, int fileId, string userId);

    Task<List<PartnerPortalDatabase.Entities.Attachment>> GetContextRelatedAttachments(
        string contextObjectId,
        string userId
    );

    Task<byte[]> CreateZipArchive(string contextObjectId, string userId);

    string StoredFilePath(string contextObjectId, string filePath);

    GetAttachmentConfigsResponse GetAttachmentConfigs();

    Task<FileDownloadInfo> PrepareFileForDownload(string contextObjectId, int fileId, string userId);

    Task CopyAttachmentToNewContext(int attachmentId, string sourceContextId, string targetContextId, string userId);

    void SetConfiguration(string targetFilePath, long fileSizeLimit, int fileCountLimit, string[] permittedExtensions);
}

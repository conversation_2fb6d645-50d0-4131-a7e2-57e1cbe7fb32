using System.IO.Compression;
using System.Text.RegularExpressions;
using System.Web;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using MimeKit;
using PartnerPortal.Backend.Contracts.ContractsApiStub.Models;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.Commons;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using static PartnerPortal.Backend.Shared.Common.Utils.ConfigHelper;
using FileInfo = PartnerPortal.Backend.Contracts.ContractsApiStub.Models.FileInfo;

namespace PartnerPortal.Backend.Shared.Attachment;

public partial class AttachmentService : IAttachmentService
{
    private readonly ILogger<AttachmentService> _logger;
    private readonly UserDbContext _context;
    private readonly IConfiguration _config;
    private int _fileCountLimit;
    private string[] _permittedExtensions;
    private string _targetFilePath;
    private long _fileSizeLimit;

    // configure whether to convert user specified file name at upload to an internal trustedFileNameForFileStorage (random file name)
    private const bool TransformStoredFilename = false; // switched off due to shared folder access needed for consumption by BC/ELO

    private const int MaxFilenameLength = 155;

    // Get the default form options so that we can use them to set the default limits for request body data.
    private static readonly FormOptions DefaultFormOptions = new();

    public AttachmentService(ILogger<AttachmentService> logger, UserDbContext context, IConfiguration config)
    {
        _logger = logger;
        _context = context;
        _config = config;

        _targetFilePath = _config.GetConfigValue<string>("FileUpload:StoredFilesPath");
        _fileSizeLimit = _config.GetConfigValue<long>("FileUpload:FileSizeLimit");
        _fileCountLimit = _config.GetConfigValue<int>("FileUpload:FileCountLimit");
        _permittedExtensions = _config.GetConfigValue<string[]>("FileUpload:PermittedExtensions");
    }

    public void SetConfiguration(
        string targetFilePath,
        long fileSizeLimit,
        int fileCountLimit,
        string[] permittedExtensions
    )
    {
        _targetFilePath = targetFilePath;
        _fileSizeLimit = fileSizeLimit;
        _fileCountLimit = fileCountLimit;
        _permittedExtensions = permittedExtensions;
    }

    public async Task<ListFilesResponse> UploadFile(
        string contextObjectId,
        string userId,
        Stream body,
        string contentType,
        ModelStateDictionary modelState,
        bool enableVersioning = true
    )
    {
        if (!MultipartRequestHelper.IsMultipartContentType(contentType))
        {
            modelState.AddModelError("File", $"The request couldn't be processed (Error 1).");
            throw new ValidationException("ErrorWhenUploadingTheFile");
        }

        // Get boundary
        string boundary = MultipartRequestHelper.GetBoundary(
            MediaTypeHeaderValue.Parse(contentType),
            DefaultFormOptions.MultipartBoundaryLengthLimit
        );

        var reader = new MultipartReader(boundary, body);
        MultipartSection? section;
        ListFilesResponse response = new ListFilesResponse { Files = [] };
        try
        {
            section = await reader.ReadNextSectionAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during uploading file. Context: {id}", contextObjectId);
            throw new ValidationException("ErrorWhenUploadingTheFile", ex, ex.Message);
        }

        using var transaction = await _context.Database.BeginTransactionAsync();
        var newAttachments = new List<PartnerPortalDatabase.Entities.Attachment>();
        try
        {
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(
                    section.ContentDisposition,
                    out var contentDisposition
                );

                if (hasContentDispositionHeader)
                {
                    // This check assumes that there's a file
                    // present without form data. If form data
                    // is present, this method immediately fails
                    // and returns the model error.
                    if (!MultipartRequestHelper.HasFileContentDisposition(contentDisposition))
                    {
                        // ModelState.AddModelError("File", $"The request couldn't be processed (Error 2).");
                        throw new GeneralServiceException("UploadingFileContentDisposition");
                    }
                    else
                    {
                        // Check whether number of files stored for the same object reached maximum allowed count
                        int fileCount = await _context.Attachments.CountAsync(a => a.Context == contextObjectId);
                        if (fileCount >= _fileCountLimit)
                        {
                            _logger.LogInformation("File limit exceeded. Context: {id}", contextObjectId);
                            throw new ValidationException("FileCountLimitWarning", _fileCountLimit);
                        }

                        var (trustedFileNameForDisplay, trustedFileNameForFileStorage) = ProcessFileName(
                            contentDisposition,
                            contextObjectId,
                            enableVersioning
                        );

                        // **WARNING!**
                        // In the following example, the file is saved without
                        // scanning the file's contents. In most production
                        // scenarios, an anti-virus/anti-malware scanner API
                        // is used on the file before making the file available
                        // for download or for use by other systems.
                        // For more information, see the topic that accompanies
                        // this sample.
                        var streamedFileContent = await FileHelpers.ProcessStreamedFile(
                            section,
                            contentDisposition,
                            modelState,
                            _permittedExtensions,
                            _fileSizeLimit,
                            _logger
                        );

                        if (!modelState.IsValid)
                        {
                            _logger.LogError(
                                "Error during uploading file. Context: {id}, errors: {errors}",
                                contextObjectId,
                                string.Join(",\n  ", GetModelErrros(modelState))
                            );
                            throw new ValidationException(
                                "ErrorWhenUploadingTheFile",
                                string.Join(",\n  ", GetModelErrros(modelState))
                            );
                        }

                        string contextDirPath = ContextDirPath(contextObjectId);
                        Directory.CreateDirectory(contextDirPath);
                        using (
                            var targetStream = File.Create(
                                StoredFilePath(contextObjectId, trustedFileNameForFileStorage)
                            )
                        )
                        {
                            await targetStream.WriteAsync(streamedFileContent);

                            _logger.LogInformation(
                                "Uploaded file '{TrustedFileNameForDisplay}' saved to "
                                    + "'{TargetDirPath}' as {TrustedFileNameForFileStorage}",
                                trustedFileNameForDisplay,
                                contextDirPath,
                                trustedFileNameForFileStorage
                            );
                        }

                        // insert new record into Attachments table in the DB
                        string mime = MimeTypes.GetMimeType(contentDisposition.FileName.Value);
                        var attachment = new PartnerPortalDatabase.Entities.Attachment
                        {
                            Context = contextObjectId,
                            UploadName = trustedFileNameForDisplay,
                            StoredName = trustedFileNameForFileStorage,
                            MimeType = mime,
                            Committed = false,
                            CreatedBy = userId,
                            ModifiedBy = userId,
                        };
                        newAttachments.Add(attachment);
                        _context.Add(attachment);
                        await _context.SaveChangesAsync();

                        FileInfo item = new FileInfo
                        {
                            FileId = attachment.Id,
                            FileName = attachment.UploadName,
                            MimeType = attachment.MimeType,
                        };
                        response.Files.Add(item);
                    }
                }

                // Drain any remaining section body that hasn't been consumed and
                // read the headers for the next section.
                section = await reader.ReadNextSectionAsync();
            }

            await transaction.CommitAsync();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();

            foreach (var attachment in newAttachments)
            {
                File.Delete(StoredFilePath(contextObjectId, attachment.StoredName));
            }

            _logger.LogError(ex, "Error during file upload transaction. Context: {id}", contextObjectId);

            throw;
        }
        return response;
    }

    private (string trustedFileNameForDisplay, string trustedFileNameForFileStorage) ProcessFileName(
        ContentDispositionHeaderValue contentDisposition,
        string contextObjectId,
        bool enableVersioning
    )
    {
        var trustedFileNameForDisplay = ConvertFileName(
            contentDisposition.FileName.HasValue ? contentDisposition.FileName.Value : throw new ValidationException()
        );

        var trustedFileNameForFileStorage = TransformStoredFilename
            ? Path.GetRandomFileName()
            : trustedFileNameForDisplay;

        if (enableVersioning)
        {
            // For versioning mode, check against UploadName
            bool found = _context.Attachments.Any(a =>
                a.Context == contextObjectId && a.UploadName == trustedFileNameForDisplay
            );
            if (found)
            {
                _logger.LogInformation("File name collision. Context: {id}", contextObjectId);
                throw new ValidationException("FileNameExistWarning", trustedFileNameForDisplay);
            }

            // TODO log all kind of available filenames at this point!
            // this.logger.LogInformation("UploadFile: contentDisposition: {contentDisposition}", contentDisposition);
            // this.logger.LogInformation("UploadFile:   DispositionType: {DispositionType}", contentDisposition.DispositionType);
            // this.logger.LogInformation("UploadFile:   Name: {Name}", contentDisposition.Name);
            // this.logger.LogInformation("UploadFile:   FileName: {FileName}", contentDisposition.FileName);
            // this.logger.LogInformation("UploadFile:   FileNameStar: {FileNameStar}", contentDisposition.FileNameStar);

            // Remove spaces from file name if more than one spaces are next to each other, because BC can't handle it
            // If the new file name already exists then we need to prefix it with _{number}
            string noMultipleSpacesInFileName = NoConsecutiveSpacesRegex().Replace(trustedFileNameForFileStorage, " ");

            int count = 1;

            System.IO.FileInfo fi = new(noMultipleSpacesInFileName);
            string extension = fi.Extension;
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(noMultipleSpacesInFileName);
            _logger.LogDebug(
                "Extension: {extension}, File name without extension: {filename}",
                extension,
                fileNameWithoutExtension
            );

            // We have to allow uploading {filename}_{number} if it is not uploaded yet (there is no UploadName match)
            Match regex = FileNameVersioningRegex().Match(fileNameWithoutExtension);
            if (regex.Success)
            {
                fileNameWithoutExtension = regex.Groups[1].Value;
                count = int.Parse(regex.Groups[2].Value);
            }

            if (!string.Equals(trustedFileNameForFileStorage, noMultipleSpacesInFileName) || regex.Success)
            {
                // Increase count until available name found
                do
                {
                    trustedFileNameForFileStorage = string.Format(
                        "{0}_{1}{2}",
                        fileNameWithoutExtension,
                        count++,
                        extension
                    );
                    found = _context.Attachments.Any(a =>
                        a.Context == contextObjectId && a.StoredName == trustedFileNameForFileStorage
                    );
                } while (found);
            }

            // Extra check: no attachment record exists yet with the same stored file name under the same context
            found = _context.Attachments.Any(a =>
                a.Context == contextObjectId && a.StoredName == trustedFileNameForFileStorage
            );
            if (found)
            {
                _logger.LogInformation("File name collision. Context: {id}", contextObjectId);
                throw new ValidationException("FileNameExistWarning", trustedFileNameForDisplay);
            }
        }
        else
        {
            // For non-versioning mode, check against the file storage name
            bool found = _context.Attachments.Any(a =>
                a.Context == contextObjectId && a.UploadName == trustedFileNameForFileStorage
            );
            if (found)
            {
                _logger.LogInformation("File name collision. Context: {id}", contextObjectId);
                throw new ValidationException("FileNameExistWarning", trustedFileNameForFileStorage);
            }
        }

        return (trustedFileNameForDisplay, trustedFileNameForFileStorage);
    }

    public async Task RevertFile(string contextObjectId, int fileId, string userId)
    {
        _logger.LogTrace(
            "RevertFile called: contextObjectId: {contextObjectId}, fileId: {fileId}, userId: {userId}",
            contextObjectId,
            fileId,
            userId
        );

        var attachment = await _context.Attachments.FindAsync(fileId);

        if (attachment == null)
        {
            _logger.LogWarning(
                "RevertFile: uploaded file not found: contextObjectId: {contextObjectId}, fileId: {fileId}",
                contextObjectId,
                fileId
            );
            return;
        }

        if (attachment.Context != contextObjectId)
        {
            _logger.LogWarning(
                "RevertFile: uploaded file context mismatch: contextObjectId: {contextObjectId}, fileId: {fileId}",
                contextObjectId,
                fileId
            );
            throw new ForbiddenException("A feltöltött file nem a megadott mappához tartozik!");
        }

        _logger.LogTrace("RevertFile: attachment.StoredName: {StoredName}", attachment.StoredName);

        File.Delete(StoredFilePath(contextObjectId, attachment.StoredName));
        _context.Attachments.Remove(attachment);

        await _context.SaveChangesAsync();
    }

    public async Task RevertFiles(string contextObjectId, string userId)
    {
        _logger.LogTrace(
            "RevertFiles called: contextObjectId: {contextObjectId}, userId: {userId}",
            contextObjectId,
            userId
        );

        var query = await _context.Attachments.Where(a => a.Context == contextObjectId).ToListAsync();

        if (query.Count != 0)
        {
            foreach (var attachment in query)
            {
                _logger.LogTrace("RevertFiles: attachment.StoredName: {StoredName}", attachment.StoredName);

                bool removeAttachment =
                    attachment.Committed == false
                    || !File.Exists(StoredFilePath(contextObjectId, attachment.StoredName));

                // remove each referred file in file system, too
                if (removeAttachment)
                {
                    File.Delete(StoredFilePath(contextObjectId, attachment.StoredName));

                    // delete any pending ATTACHMENT object (i.e. with Committed = false) under Context = contextObjectId
                    _context.Attachments.Remove(attachment);
                }
            }

            await _context.SaveChangesAsync();
        }
    }

    public async Task CommitFiles(string contextObjectId, string userId, List<FileInfo> fileInfos)
    {
        _logger.LogTrace(
            "CommitFiles called: contextObjectId: {contextObjectId}, userId: {userId}",
            contextObjectId,
            userId
        );

        var attachmentIds = fileInfos.Select(f => f.FileId).ToList();
        var query = await _context
            .Attachments.Where(a =>
                attachmentIds.Contains(a.Id) && a.Context == contextObjectId && a.Committed == false
            )
            .ToListAsync();

        if (query.Count != 0)
        {
            foreach (var attachment in query)
            {
                _logger.LogTrace("CommitFiles: attachment.StoredName: {StoredName}", attachment.StoredName);

                // check existence of each referred file in file system, too
                if (File.Exists(StoredFilePath(contextObjectId, attachment.StoredName)))
                {
                    _logger.LogTrace("CommitFiles: File.Exists");

                    // set Committed for each ATTACHMENT object under Context = contextObjectId
                    attachment.Committed = true;
                    attachment.ModifiedBy = userId;
                }
            }

            await _context.SaveChangesAsync();
        }
    }

    public async Task<ListFilesResponse> ListFiles(string contextObjectId, string userId, bool getStoredName = false)
    {
        _logger.LogTrace(
            "ListFiles called: contextObjectId: {contextObjectId}, userId: {userId}",
            contextObjectId,
            userId
        );
        ListFilesResponse response = new ListFilesResponse { Files = [] };

        var query = await _context
            .Attachments.Where(a => a.Context == contextObjectId)
            .OrderBy(a => a.LastModDate)
            .ToListAsync();
        if (query.Count != 0)
        {
            foreach (var attachment in query)
            {
                _logger.LogTrace("ListFiles: attachment.StoredName: {StoredName}", attachment.StoredName);

                // TODO check also via Directory.Exists when split by context ...

                // check existence of each stored file
                if (File.Exists(this.StoredFilePath(contextObjectId, attachment.StoredName)))
                {
                    // append info about each referred file
                    FileInfo item = new FileInfo
                    {
                        FileId = attachment.Id,
                        FileName = getStoredName ? attachment.StoredName : attachment.UploadName,
                        MimeType = attachment.MimeType,
                    };
                    response.Files.Add(item);
                }
            }
        }

        return response;
    }

    public async Task<PartnerPortalDatabase.Entities.Attachment> GetAttachment(
        string contextObjectId,
        int fileId,
        string userId
    )
    {
        _logger.LogTrace(
            "GetAttachment called: contextObjectId: {contextObjectId}, fileId: {fileId}, userId: {userId}",
            contextObjectId,
            fileId,
            userId
        );
        PartnerPortalDatabase.Entities.Attachment? response = null;

        PartnerPortalDatabase.Entities.Attachment? attachment = await _context
            .Attachments.Where(a => a.Id == fileId && a.Context == contextObjectId)
            .FirstOrDefaultAsync();
        if (attachment != null)
        {
            _logger.LogTrace(
                "GetAttachment: attachment.UploadName: {UploadName}, attachment.StoredName: {StoredName}",
                attachment.UploadName,
                attachment.StoredName
            );

            // check existence of stored file
            if (File.Exists(StoredFilePath(contextObjectId, attachment.StoredName)))
            {
                response = attachment;
            }
        }

        if (response == null)
        {
            _logger.LogWarning("File '{id}' not found", contextObjectId);
            throw new ValidationException("FileForDownloadingNotFound", contextObjectId, fileId);
        }

        return response;
    }

    public async Task<FileDownloadInfo> PrepareFileForDownload(string contextObjectId, int fileId, string userId)
    {
        // Get attachment metadata
        PartnerPortalDatabase.Entities.Attachment attachment = await GetAttachment(contextObjectId, fileId, userId);

        // Get the physical path to the file
        string storedFilePath = StoredFilePath(contextObjectId, attachment.StoredName);

        // Read the File data into FileStream
        FileStream fileStream = new FileStream(storedFilePath, FileMode.Open, FileAccess.Read);

        return new FileDownloadInfo
        {
            FileStream = fileStream,
            MimeType = attachment.MimeType,
            FileName = attachment.UploadName,
        };
    }

    public async Task<List<PartnerPortalDatabase.Entities.Attachment>> GetContextRelatedAttachments(
        string contextObjectId,
        string userId
    )
    {
        _logger.LogTrace(
            "GetContextRelatedAttachments called: contextObjectId: {contextObjectId}, userId: {userId}",
            contextObjectId,
            userId
        );
        List<PartnerPortalDatabase.Entities.Attachment> response = [];
        List<PartnerPortalDatabase.Entities.Attachment> attachments = await _context
            .Attachments.Where(attachment => attachment.Context == contextObjectId)
            .ToListAsync();
        List<PartnerPortalDatabase.Entities.Attachment> notFoundAttachments = [];

        foreach (var attachment in attachments)
        {
            if (File.Exists(this.StoredFilePath(contextObjectId, attachment.StoredName)))
            {
                response.Add(attachment);
            }
            else
            {
                notFoundAttachments.Add(attachment);
            }
        }

        if (notFoundAttachments.Count != 0)
        {
            _logger.LogWarning(
                "File(s) not found: "
                    + string.Join(", ", notFoundAttachments.Select(attachment => attachment.UploadName))
            );
        }

        if (response.Count == 0)
        {
            _logger.LogWarning("File '{id}' not found", contextObjectId);
            throw new ValidationException("NoFileFoundForTheDocument", contextObjectId);
        }

        return response;
    }

    public async Task<byte[]> CreateZipArchive(string contextObjectId, string userId)
    {
        _logger.LogTrace(
            "CreateZipArchive called: contextObjectId: {contextObjectId}, userId: {userId}",
            contextObjectId,
            userId
        );

        // Get all attachments for the context
        List<PartnerPortalDatabase.Entities.Attachment> attachments = await GetContextRelatedAttachments(
            contextObjectId,
            userId
        );

        using MemoryStream ms = new MemoryStream();

        using (var zip = new ZipArchive(ms, ZipArchiveMode.Create, true))
        {
            foreach (var attachment in attachments)
            {
                string storedFilePath = StoredFilePath(contextObjectId, attachment.StoredName);
                var entry = zip.CreateEntry(attachment.UploadName);
                using var fileStream = new FileStream(storedFilePath, FileMode.Open, FileAccess.Read);
                using var entryStream = entry.Open();
                fileStream.CopyTo(entryStream);
            }
        }

        return ms.ToArray();
    }

    public async Task DeleteFiles(string contextObjectId, string userId)
    {
        _logger.LogTrace(
            "DeleteFiles called: contextObjectId: {contextObjectId}, userId: {userId}",
            contextObjectId,
            userId
        );

        await _context
            .Database.CreateExecutionStrategy()
            .ExecuteAsync(async () =>
            {
                var query = await _context.Attachments.Where(a => a.Context == contextObjectId).ToListAsync();

                if (query.Any())
                {
                    foreach (var attachment in query)
                    {
                        _logger.LogTrace("DeleteFiles: attachment.StoredName: {StoredName}", attachment.StoredName);

                        var filePath = StoredFilePath(contextObjectId, attachment.StoredName);
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                        }

                        _context.Attachments.Remove(attachment);
                    }

                    try
                    {
                        var dirPath = ContextDirPath(contextObjectId);
                        if (Directory.Exists(dirPath))
                        {
                            Directory.Delete(dirPath);
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "DeleteFiles: could not remove directory for: contextObjectId: {contextObjectId}",
                            contextObjectId
                        );
                    }

                    await _context.SaveChangesAsync();
                }
            });
    }

    private string ContextDirPath(string contextObjectId)
    {
        return Path.Combine(_targetFilePath, contextObjectId);
    }

    public string StoredFilePath(string contextObjectId, string filePath)
    {
        return Path.Combine(_targetFilePath, contextObjectId, filePath);
    }

    public async Task CopyAttachmentToNewContext(
        int attachmentId,
        string sourceContextId,
        string targetContextId,
        string userId
    )
    {
        _logger.LogTrace(
            "CopyAttachmentToNewContext called: attachmentId: {attachmentId}, sourceContextId: {sourceContextId}, targetContextId: {targetContextId}, userId: {userId}",
            attachmentId,
            sourceContextId,
            targetContextId,
            userId
        );

        var attachment = await _context.Attachments.FindAsync(attachmentId);
        if (attachment == null)
        {
            _logger.LogWarning("Attachment {AttachmentId} not found", attachmentId);
            return;
        }

        // Get the source file path
        var sourcePath = StoredFilePath(sourceContextId, attachment.StoredName);
        var targetPath = StoredFilePath(targetContextId, attachment.StoredName);

        // Create target directory if it doesn't exist
        var targetDirectory = Path.GetDirectoryName(targetPath);
        if (!string.IsNullOrEmpty(targetDirectory) && !Directory.Exists(targetDirectory))
        {
            Directory.CreateDirectory(targetDirectory);
        }

        // Copy the file to the new context
        if (File.Exists(sourcePath))
        {
            File.Copy(sourcePath, targetPath, true); // Overwrite if exists
            _logger.LogInformation("Copied attachment file from {SourcePath} to {TargetPath}", sourcePath, targetPath);
        }
        else
        {
            _logger.LogWarning("Source file {SourcePath} does not exist", sourcePath);
        }

        // Update the attachment context to point to the new location
        attachment.Context = targetContextId;
        attachment.ModifiedBy = userId;

        await _context.SaveChangesAsync();
        _logger.LogTrace("Attachment context updated to {TargetContextId}", targetContextId);
    }

    private string ConvertFileName(string fileName)
    {
        // ensure no harmless char sequences remain unencoded, meanwhile expected accented unicode charatcters keept as given
        var trustedFileName = Regex
            .Unescape(
                Regex.Replace(HttpUtility.UrlEncodeUnicode(Regex.Replace(fileName, "[\"*:<>?/\\|]", "-")), "%u", "\\u")
            )
            .Replace('+', ' ')
            .Replace("%2b", "+");

        // treat any too long file names by cutting at an appropriate length
        int len = trustedFileName.Length;
        if (len > MaxFilenameLength)
        {
            int dot = trustedFileName.LastIndexOf('.');
            if (dot > 0)
            {
                int ext = len - dot;
                int cut = MaxFilenameLength - ext;
                trustedFileName = trustedFileName.Substring(0, cut) + trustedFileName.Substring(dot);
            }
            else
            {
                trustedFileName = trustedFileName.Substring(0, MaxFilenameLength);
            }
        }

        return trustedFileName;
    }

    private static List<string> GetModelErrros(ModelStateDictionary modelState)
    {
        var errors = modelState
            .Values.Where(e => e.Errors.Count > 0)
            .SelectMany(e => e.Errors)
            .Select(e => e.ErrorMessage)
            .ToList();

        return errors;
    }

    public GetAttachmentConfigsResponse GetAttachmentConfigs()
    {
        GetAttachmentConfigsResponse response = new()
        {
            FileCountLimit = _fileCountLimit,
            FileSizeLimit = _fileSizeLimit,
            PermittedExtensions = [.. _permittedExtensions],
        };
        return response;
    }

    [GeneratedRegex("[ ]{2,}", RegexOptions.None)]
    private static partial Regex NoConsecutiveSpacesRegex();

    [GeneratedRegex(@"^(.+)_(\d+)$")]
    private static partial Regex FileNameVersioningRegex();
}

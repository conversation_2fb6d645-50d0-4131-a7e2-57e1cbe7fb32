{"name": "Backend.Shared.MarketEloApiClient.Test", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/backend/shared/PartnerPortal.Backend.Shared.MarketEloApiClient.Test", "targets": {"build": {"executor": "@nx-dotnet/core:build", "outputs": ["{workspaceRoot}/dist/libs/backend/shared/PartnerPortal.Backend.Shared.MarketEloApiClient.Test", "{workspaceRoot}/dist/intermediates/libs/backend/shared/PartnerPortal.Backend.Shared.MarketEloApiClient.Test"], "options": {"configuration": "Debug", "noDependencies": true}, "configurations": {"production": {"configuration": "Release"}}}, "test": {"executor": "@nx-dotnet/core:test", "options": {}}, "lint": {"executor": "@nx-dotnet/core:format"}}, "tags": []}
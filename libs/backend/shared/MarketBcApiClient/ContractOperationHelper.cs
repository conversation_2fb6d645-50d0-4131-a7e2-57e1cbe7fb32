using PartnerPortal.Backend.Shared.Common.Utils;

namespace PartnerPortal.Backend.Shared.MarketBcApiClient;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Shared.Common.Exceptions;

public class ContractOperationHelper : IContractOperationHelper
{
    private readonly IConfiguration _config;
    private readonly ILogger<ContractOperationHelper> _logger;
    private readonly MarketBcClient _proxy;

    public ContractOperationHelper(IConfiguration config, ILogger<ContractOperationHelper> logger, MarketBcClient proxy)
    {
        _config = config;
        _logger = logger;
        _proxy = proxy;
    }

    public async Task<List<ProjectEntityData>> GetProjects(string contactNo, string employeeNo, string projectNo)
    {
        _logger.LogInformation(
            "GetProjects called with parameters: contactNo: {contactNo}, projectNo: {projectNo}",
            contactNo,
            projectNo
        );

        GetProject request = new GetProject(contactNo, employeeNo, projectNo, new RootNodeName11());

        GetProject_Result? response;
        try
        {
            _logger.LogInformation("GetProjectAsync called");
            response = await _proxy.GetProjectAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {Message}",
                ex.Message
            );
            throw new BcApiException(ex);
        }

        ProjectEntityData[] projects = response.result.ProjectEntityData;
        CheckItemFromBcPresents(projects, "project", !string.IsNullOrEmpty(projectNo));
        return projects == null ? [] : [.. projects]; // ensure NOT null (also in case of empty result returned by BC).ToList();
    }

    public async Task<ContractEntityData> GetSingleContract(string contractNo, string contactNo, string projectNo)
    {
        if (string.IsNullOrEmpty(contractNo))
        {
            throw new GeneralServiceException("ContractNo must have a value to get single contract");
        }

        _logger.LogInformation(
            "GetContract called with parameters: contractNo: {contractNo}, contactNo: {contactNo}, "
                + "projectNo: {projectNo}",
            contractNo,
            contactNo,
            projectNo
        );

        GetContract request = new GetContract(contractNo, contactNo, projectNo, new RootNodeName3());

        GetContract_Result? response;
        try
        {
            _logger.LogInformation("GetContractAsync called");
            response = await _proxy.GetContractAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {Message}",
                ex.Message
            );
            throw new BcApiException(ex);
        }
        if (response.result.ContractEntityData.Length == 0)
        {
            _logger.LogError("Contract with id '{id}' was not found in bc database", contractNo);
        }

        if (response.result.ContractEntityData.Length > 1)
        {
            _logger.LogWarning(
                "Bc should give a single contract with id '{id}, but it returned multiple elements: {list}",
                contractNo,
                string.Join(", ", response.result.ContractEntityData.Select(c => c.DocumentNo))
            );
        }

        ContractEntityData contract = response.result.ContractEntityData.Single();
        return contract;
    }

    public async Task<List<ContractEntityData>> GetContracts(string contractNo, string contactNo, string projectNo)
    {
        _logger.LogInformation(
            "GetContracts called with parameters: contractNo: {contractNo}, contactNo: {contactNo}, "
                + "projectNo: {projectNo}",
            contractNo,
            contactNo,
            projectNo
        );

        GetContract request = new GetContract(contractNo, contactNo, projectNo, new RootNodeName3());

        GetContract_Result? response;
        try
        {
            _logger.LogInformation("GetContractAsync called");
            response = await _proxy.GetContractAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {Message}",
                ex.Message
            );
            throw new BcApiException(ex);
        }

        ContractEntityData[] contracts = response.result.ContractEntityData;
        CheckItemFromBcPresents(contracts, "contract", !string.IsNullOrEmpty(contractNo));
        return contracts == null ? [] : [.. contracts]; // ensure NOT null (also in case of empty result returned by BC)
    }

    public async Task<List<CompletionCertEntityData>> GetCompletionCertificate(
        string tigNo,
        string contactNo,
        string contractNo
    )
    {
        _logger.LogInformation(
            "GetCompletionCertificate called with parameters: tigNo: {tigNo}, BcUserNo: {contactNo}, contractNo: {contractNo}",
            tigNo,
            contactNo,
            contractNo
        );

        GetCompletionCertificate request = new GetCompletionCertificate(
            tigNo,
            contactNo,
            contractNo,
            new RootNodeName7()
        );

        GetCompletionCertificate_Result? response;
        try
        {
            _logger.LogInformation("GetCompletionCertificateAsync called");
            response = await _proxy.GetCompletionCertificateAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {Message}",
                ex.Message
            );
            throw new BcApiException(ex);
        }

        CompletionCertEntityData[] completionCerts = response.result.CompletionCertEntityData;
        CheckItemFromBcPresents(completionCerts, "completion certificate", !string.IsNullOrEmpty(tigNo));
        return completionCerts == null ? [] : [.. completionCerts]; // ensure NOT null (also in case of empty result returned by BC)
    }

    public async Task<List<CompletionCertEntityData>> CompletionCertificateCreate(
        string companyCode,
        string contractNo,
        string documentType
    )
    {
        _logger.LogInformation(
            "CompletionCertificateCreate called with parameters: companyCode: {companyCode}, contractNo: {contractNo}, documentType: {documentType}",
            companyCode,
            contractNo,
            documentType
        );

        RootNodeName6 reqNode = new RootNodeName6
        {
            RequestData = new RequestData4
            {
                CompanyCode = companyCode,
                ContractDocumentNo = contractNo,
                Timestamp = DateTime.Now.ToString("yyyyMMddHHmmss"),
                DocumentType = documentType,
            },
        };

        CompletionCertificateCreate request = new CompletionCertificateCreate(
            reqNode,
            new RootNodeName1(),
            new RootNodeName7()
        );

        CompletionCertificateCreate_Result? response;
        try
        {
            _logger.LogInformation("CompletionCertificateCreateAsync called");
            response = await _proxy.CompletionCertificateCreateAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {Message}",
                ex.Message
            );
            throw new BcApiException(ex);
        }

        // check BC response OK
        if (response?.result?.response?.Code == null || !response.result.response.Code.Equals("OK"))
        {
            _logger.LogError(
                "A szerződésre jelenleg nem hozható létre TIG igény, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {ResponseCode} {ErrorMessage}",
                response?.result?.response?.Code,
                response?.result?.response?.Message
            );
            throw new BcApiException("NotPossibleToCreateATig");
        }

        CompletionCertEntityData[] completionCerts = response.completionCertData.CompletionCertEntityData;
        CheckItemFromBcPresents(completionCerts, "completion certificate", true);
        return [.. completionCerts];
    }

    public async Task CompletionCertificateApprove(
        string companyCode,
        string completionCertNo,
        string completionCertDate,
        Lines1 lines,
        Attachments1 attachments
    )
    {
        _logger.LogInformation(
            "CompletionCertificateApprove called with parameters: companyCode: {companyCode}, completionCertNo: {completionCertNo}, completionCertDate: {completionCertDate}, lines: {lines}, attachments: {attachments}",
            companyCode,
            completionCertNo,
            completionCertDate,
            lines,
            attachments
        );

        RootNodeName5 reqNode = new RootNodeName5
        {
            RequestData = new RequestData3
            {
                CompanyCode = companyCode,
                CompletionCertDocumentNo = completionCertNo,
                Timestamp = DateTime.Now.ToString("yyyyMMddHHmmss"),
                CompletionCertificateDate = completionCertDate,
                Lines = lines,
                Attachments = attachments,
            },
        };

        CompletionCertificateApprove request = new CompletionCertificateApprove(reqNode, new RootNodeName1());

        CompletionCertificateApprove_Result? response;
        try
        {
            _logger.LogInformation("CompletionCertificateApproveAsync called");
            response = await _proxy.CompletionCertificateApproveAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {Message}",
                ex.Message
            );
            throw new BcApiException(ex);
        }

        // check BC response OK
        if (response?.result?.response?.Code == null || !response.result.response.Code.Equals("OK"))
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {ResponseCode} {ErrorMessage}",
                response?.result?.response?.Code,
                response?.result?.response?.Message
            );
            throw new BcApiException();
        }
    }

    public async Task CompletionCertificateDelete(string companyCode, string completionCertNo)
    {
        _logger.LogInformation(
            "CompletionCertificateDelete called with parameters: companyCode: {companyCode}, completionCertNo: {completionCertNo}",
            companyCode,
            completionCertNo
        );

        RootNodeName8 reqNode = new RootNodeName8
        {
            RequestData = new RequestData5
            {
                CompanyCode = companyCode,
                CompletionCertDocumentNo = completionCertNo,
                Timestamp = DateTime.Now.ToString("yyyyMMddHHmmss"),
            },
        };

        CompletionCertificateDelete request = new CompletionCertificateDelete(reqNode, new RootNodeName1());

        CompletionCertificateDelete_Result? response;
        try
        {
            _logger.LogInformation("CompletionCertificateDeleteAsync called");
            response = await _proxy.CompletionCertificateDeleteAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {Message}",
                ex.Message
            );
            throw new BcApiException(ex);
        }

        // check BC response OK
        if (response?.result?.response?.Code == null || !response.result.response.Code.Equals("OK"))
        {
            _logger.LogError(
                "BC oldali hiba, kérjük forduljon a Market Csoport ügyintézőjéhez.\n {ResponseCode} {ErrorMessage}",
                response?.result?.response?.Code,
                response?.result?.response?.Message
            );
            throw new BcApiException();
        }
    }

    private void CheckItemFromBcPresents(Object[] items, string itemType, bool isDetailRequest)
    {
        if ((items == null || items.Length == 0) && isDetailRequest)
        {
            _logger.LogError("There is no {itemType} with these parameters", itemType);
            throw new BcApiException(
                itemType switch
                {
                    "project" => "ProjectNotFound",
                    "contract" => "ContractNotFound",
                    "completion certificate" => "CompletionCertNotFound",
                    _ => "SourceNotFound",
                }
            );
        }

        _logger.LogDebug(
            "Response length: {Length} {itemType}s returned",
            (items == null ? 0 : items.Length),
            itemType
        );
    }
}

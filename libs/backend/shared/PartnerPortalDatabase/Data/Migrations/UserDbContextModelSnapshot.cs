﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;

#nullable disable

namespace PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data.Migrations
{
    [DbContext(typeof(UserDbContext))]
    partial class UserDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.15")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AdditionalWork.AdditionalWorkLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AdditionalWorkShellId")
                        .HasColumnType("int");

                    b.Property<string>("Amount")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("LineNo")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AdditionalWorkShellId");

                    b.ToTable("AdditionalWorkLines");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AdditionalWork.AdditionalWorkShell", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ApprovalStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AssignedTo")
                        .HasColumnType("int");

                    b.Property<int?>("AssignedToSubstitute")
                        .HasColumnType("int");

                    b.Property<string>("BaseContractDocumentNo")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<bool>("BcCheckFailed")
                        .HasColumnType("bit");

                    b.Property<string>("Comment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("CompanyCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentNo")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("ELOContractWorkflowStatus")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("FinalDeadline")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEscalated")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFilled")
                        .HasColumnType("bit");

                    b.Property<int?>("JudgeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ProjectNo")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("VatType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("AssignedToSubstitute");

                    b.HasIndex("JudgeId");

                    b.ToTable("AdditionalWorkShells");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("BcUserId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("EuVatRegistrationNo")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("ProfileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("VatRegistrationNo")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Attachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Committed")
                        .HasColumnType("bit");

                    b.Property<string>("Context")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("StoredName")
                        .IsRequired()
                        .HasMaxLength(155)
                        .HasColumnType("nvarchar(155)");

                    b.Property<string>("UploadName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.ToTable("Attachments");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AuditLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AdditionalData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("EventType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("bit");

                    b.Property<string>("ResourceId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ResourceType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("UserId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("ResourceId");

                    b.HasIndex("ResourceType");

                    b.HasIndex("Timestamp");

                    b.HasIndex("UserId");

                    b.HasIndex("ResourceType", "ResourceId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentAnalysisResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DocumentUploadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("ProcessingSourceId")
                        .HasColumnType("int");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DocumentUploadId");

                    b.HasIndex("ProcessingSourceId")
                        .IsUnique();

                    b.ToTable("DocumentAnalysisResults");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentClassificationResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DocumentUploadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentId");

                    b.HasIndex("DocumentUploadId");

                    b.ToTable("DocumentClassificationResults");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentEloResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BcId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BcRegId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreateRequestXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreateResponseXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DocumentUploadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EloGuid")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EloRegId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<string>("FileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("ProcessingSourceId")
                        .HasColumnType("int");

                    b.Property<string>("Result")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<string>("SearchRequestXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SearchResponseXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StartWorkflowRequestXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StartWorkflowResponseXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UploadRequestXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UploadResponseXml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkflowId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DocumentUploadId");

                    b.HasIndex("ProcessingSourceId")
                        .IsUnique();

                    b.ToTable("DocumentEloResults");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentProcessingSource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("nvarchar(34)");

                    b.Property<int>("DocumentType")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("SpIdentifier")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentId");

                    b.ToTable("DocumentProcessingSource");

                    b.HasDiscriminator().HasValue("DocumentProcessingSource");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ContractNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateOfArrival")
                        .HasColumnType("datetime2");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("nvarchar(34)");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ProjectDescription")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ProjectNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int?>("Type")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("DocumentUploads");

                    b.HasDiscriminator().HasValue("DocumentUpload");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentValidationResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DocumentUploadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("ProcessingSourceId")
                        .HasColumnType("int");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("ValidationResultJson")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DocumentUploadId");

                    b.HasIndex("ProcessingSourceId")
                        .IsUnique();

                    b.ToTable("DocumentValidationResults");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.SpIdentifierResult", b =>
                {
                    b.Property<string>("SpIdentifier")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.UnusualWorkday", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<TimeOnly>("EndTime")
                        .HasColumnType("time");

                    b.Property<bool>("IsWorkDay")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<TimeOnly>("StartTime")
                        .HasColumnType("time");

                    b.Property<int>("YearlyCalendarConfigId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("YearlyCalendarConfigId");

                    b.ToTable("UnusualWorkdays");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.YearlyCalendarConfig", b =>
                {
                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<TimeOnly?>("FridayEndTime")
                        .HasColumnType("time");

                    b.Property<bool>("FridayIsWorkDay")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("FridayStartTime")
                        .HasColumnType("time");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<TimeOnly?>("MondayEndTime")
                        .HasColumnType("time");

                    b.Property<bool>("MondayIsWorkDay")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("MondayStartTime")
                        .HasColumnType("time");

                    b.Property<TimeOnly?>("SaturdayEndTime")
                        .HasColumnType("time");

                    b.Property<bool>("SaturdayIsWorkDay")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("SaturdayStartTime")
                        .HasColumnType("time");

                    b.Property<TimeOnly?>("SundayEndTime")
                        .HasColumnType("time");

                    b.Property<bool>("SundayIsWorkDay")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("SundayStartTime")
                        .HasColumnType("time");

                    b.Property<TimeOnly?>("ThursdayEndTime")
                        .HasColumnType("time");

                    b.Property<bool>("ThursdayIsWorkDay")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("ThursdayStartTime")
                        .HasColumnType("time");

                    b.Property<TimeOnly?>("TuesdayEndTime")
                        .HasColumnType("time");

                    b.Property<bool>("TuesdayIsWorkDay")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("TuesdayStartTime")
                        .HasColumnType("time");

                    b.Property<byte[]>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<TimeOnly?>("WednesdayEndTime")
                        .HasColumnType("time");

                    b.Property<bool>("WednesdayIsWorkDay")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("WednesdayStartTime")
                        .HasColumnType("time");

                    b.HasKey("Year");

                    b.ToTable("YearlyCalendarConfigs");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.EmailHelper", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ApprovalStatus")
                        .HasColumnType("int");

                    b.Property<string>("AssignedToEmail")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssignedToPhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssignedToProfileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ELOWorkflowStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JudgeEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JudgePhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JudgeProfileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("EmailHelpers");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.EmailNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AWDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("AWEloApprove")
                        .HasColumnType("bit");

                    b.Property<bool>("AWEloApproved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("AWEloDeclined")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("AWEloMaking")
                        .HasColumnType("bit");

                    b.Property<bool>("AWEloSent")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("AWOpen")
                        .HasColumnType("bit");

                    b.Property<int>("AppUserId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("EloApprove")
                        .HasColumnType("bit");

                    b.Property<bool>("EloApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("EloDeclined")
                        .HasColumnType("bit");

                    b.Property<bool>("EloMaking")
                        .HasColumnType("bit");

                    b.Property<bool>("EloSent")
                        .HasColumnType("bit");

                    b.Property<bool>("EloVerify")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<bool>("Open")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("AppUserId")
                        .IsUnique();

                    b.ToTable("EmailNotifications");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.PartnerCache", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CompanyCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ContactBanks")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactNo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CountryRegionCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EuVatRegistrationNo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<string>("MobilePhoneNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PhoneNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PostCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("VatRegistrationNo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyName");

                    b.HasIndex("ContactNo");

                    b.HasIndex("Name");

                    b.HasIndex("VatRegistrationNo");

                    b.ToTable("PartnerCaches");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.PreparedMail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AWId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BcTigId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContractActivityDesc")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("HTMLTemplate")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Link")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MailStatus")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int?>("RequestId")
                        .HasColumnType("int");

                    b.Property<byte[]>("SavedContent")
                        .HasColumnType("varbinary(max)");

                    b.Property<int>("SendAttempt")
                        .HasColumnType("int");

                    b.Property<string>("TargetEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("PreparedMail");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Substitution", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("SubstituteId")
                        .HasColumnType("int");

                    b.Property<DateTime>("SubtitutionEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("SubtitutionStartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("SubstituteId");

                    b.ToTable("Substitutions");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.EndCompCertConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CompanyCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("PrimaryApproverEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PrimaryApproverName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PrimaryApproverPosition")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ProjectNo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SecondaryApproverEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SecondaryApproverName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SecondaryApproverPosition")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.ToTable("EndCompCertConfigurations");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.PartnerEndCompCertRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ApprovalStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ApprovedTimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("CompanyCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContractNo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("PartnerId")
                        .HasColumnType("int");

                    b.Property<string>("PrimaryApproverEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PrimaryApproverName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PrimaryApproverPosition")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ProjectNo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SecondaryApproverEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SecondaryApproverName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SecondaryApproverPosition")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("PartnerId");

                    b.ToTable("PartnerEndCompCertRequests");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigAdvance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdvanceInvoiceId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<int>("BcAdvanceLineId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<DateTime>("DeliverDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ProjectId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("ProjectTaskId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<int>("TigShellId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BcAdvanceLineId")
                        .IsUnique();

                    b.HasIndex("TigShellId");

                    b.ToTable("TigAdvances");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ActivityCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("ActivityCodeDescription")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("BcTigLineId")
                        .HasColumnType("int");

                    b.Property<string>("ContractNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("ContractSubType")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<double>("GrossAmount")
                        .HasColumnType("float");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("LineAmount")
                        .HasColumnType("float");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<double>("RemainingAmount")
                        .HasColumnType("float");

                    b.Property<int>("TigShellId")
                        .HasColumnType("int");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<double>("UnitPrice")
                        .HasColumnType("float");

                    b.Property<double>("VatAmount")
                        .HasColumnType("float");

                    b.Property<string>("VatGroup")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<double>("VatPercentage")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("TigShellId");

                    b.ToTable("TigLines");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigRetention", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<double>("Percentage")
                        .HasMaxLength(30)
                        .HasColumnType("float");

                    b.Property<string>("RetentionType")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<int>("TigShellId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TigShellId");

                    b.ToTable("TigRetentions");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigShell", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<string>("ApprovalStatus")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<int?>("AssignedTo")
                        .HasColumnType("int");

                    b.Property<string>("AssignedToCompanyId")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("AssignedToCompanyName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("AssignedToSubstitute")
                        .HasColumnType("int");

                    b.Property<bool>("BcCheckFailed")
                        .HasColumnType("bit");

                    b.Property<string>("BcContractId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("BcTigId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("Comment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("CompanyCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatorCompanyName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ELOWorkflowStatus")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("EndCompCertChecklistApproved")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEscalated")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFilled")
                        .HasColumnType("bit");

                    b.Property<int?>("JudgeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("PartialTigNo")
                        .HasColumnType("int");

                    b.Property<string>("ProjectId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("TigDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TigType")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("VatType")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("AssignedToSubstitute");

                    b.HasIndex("BcTigId")
                        .IsUnique();

                    b.HasIndex("JudgeId");

                    b.ToTable("TigShells");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.UserRegistration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("TokenStatus")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<DateTime?>("UseDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ValidTo")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("UserRegistrations");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.AttachmentDocumentType", b =>
                {
                    b.HasBaseType("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentProcessingSource");

                    b.Property<Guid>("DocumentUploadId")
                        .HasColumnType("uniqueidentifier");

                    b.HasIndex("DocumentUploadId");

                    b.HasDiscriminator().HasValue("AttachmentDocumentType");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentClassification", b =>
                {
                    b.HasBaseType("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentProcessingSource");

                    b.Property<double?>("Confidence")
                        .HasColumnType("float");

                    b.Property<int>("EndPage")
                        .HasColumnType("int");

                    b.Property<Guid>("SecretaryDocumentUploadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("StartPage")
                        .HasColumnType("int");

                    b.HasIndex("DocumentType");

                    b.HasIndex("SecretaryDocumentUploadId");

                    b.HasDiscriminator().HasValue("DocumentClassification");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.PartnerDocumentUpload", b =>
                {
                    b.HasBaseType("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload");

                    b.HasDiscriminator().HasValue("PartnerDocumentUpload");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.SecretaryDocumentUpload", b =>
                {
                    b.HasBaseType("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload");

                    b.Property<string>("ContactName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ContactNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("SecretaryDateOfArrival")
                        .HasColumnType("datetime2");

                    b.Property<int>("UploadType")
                        .HasColumnType("int");

                    b.HasDiscriminator().HasValue("SecretaryDocumentUpload");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AdditionalWork.AdditionalWorkLine", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AdditionalWork.AdditionalWorkShell", "AdditionalWorkShell")
                        .WithMany("AdditionalWorkLines")
                        .HasForeignKey("AdditionalWorkShellId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdditionalWorkShell");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AdditionalWork.AdditionalWorkShell", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "Judge")
                        .WithMany("AdditionalWorkShells")
                        .HasForeignKey("AssignedTo")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "SubstituteJudge")
                        .WithMany("SubstitutionAdditionalWorkShells")
                        .HasForeignKey("AssignedToSubstitute")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "FinalJudge")
                        .WithMany("JudgedAdditionalWorkShells")
                        .HasForeignKey("JudgeId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("FinalJudge");

                    b.Navigation("Judge");

                    b.Navigation("SubstituteJudge");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentAnalysisResult", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", "DocumentUpload")
                        .WithMany("DocumentAnalysisResults")
                        .HasForeignKey("DocumentUploadId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentProcessingSource", "ProcessingSource")
                        .WithMany()
                        .HasForeignKey("ProcessingSourceId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("DocumentUpload");

                    b.Navigation("ProcessingSource");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentClassificationResult", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", "DocumentUpload")
                        .WithMany("DocumentClassificationResults")
                        .HasForeignKey("DocumentUploadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Attachment");

                    b.Navigation("DocumentUpload");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentEloResult", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", "DocumentUpload")
                        .WithMany("DocumentEloResults")
                        .HasForeignKey("DocumentUploadId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentProcessingSource", "ProcessingSource")
                        .WithMany()
                        .HasForeignKey("ProcessingSourceId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("DocumentUpload");

                    b.Navigation("ProcessingSource");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentProcessingSource", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Attachment");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentValidationResult", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", "DocumentUpload")
                        .WithMany("DocumentValidationResults")
                        .HasForeignKey("DocumentUploadId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentProcessingSource", "ProcessingSource")
                        .WithMany()
                        .HasForeignKey("ProcessingSourceId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("DocumentUpload");

                    b.Navigation("ProcessingSource");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.UnusualWorkday", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.YearlyCalendarConfig", "YearlyCalendarConfig")
                        .WithMany("UnusualWorkdays")
                        .HasForeignKey("YearlyCalendarConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("YearlyCalendarConfig");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.EmailNotification", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "AppUser")
                        .WithOne("EmailNotification")
                        .HasForeignKey("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.EmailNotification", "AppUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AppUser");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.PreparedMail", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "User")
                        .WithMany("PreparedMails")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Substitution", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "UserEmployee")
                        .WithMany("SubstitutionEmployees")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "UserSubstitute")
                        .WithMany("SubstitutionSubtitutes")
                        .HasForeignKey("SubstituteId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("UserEmployee");

                    b.Navigation("UserSubstitute");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.PartnerEndCompCertRequest", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "Partner")
                        .WithMany("PartnerEndCompCertRequests")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigAdvance", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigShell", "TigShell")
                        .WithMany("TigAdvances")
                        .HasForeignKey("TigShellId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TigShell");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigLine", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigShell", "TigShell")
                        .WithMany("TigLines")
                        .HasForeignKey("TigShellId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TigShell");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigRetention", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigShell", "TigShell")
                        .WithMany("TigRetentions")
                        .HasForeignKey("TigShellId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TigShell");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigShell", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "User")
                        .WithMany("TigShells")
                        .HasForeignKey("AssignedTo")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "SubstituteUser")
                        .WithMany("SubstitutionTigShells")
                        .HasForeignKey("AssignedToSubstitute")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "Judge")
                        .WithMany("JudgedTigShells")
                        .HasForeignKey("JudgeId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Judge");

                    b.Navigation("SubstituteUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.UserRegistration", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", "User")
                        .WithOne("UserReg")
                        .HasForeignKey("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.UserRegistration", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.AttachmentDocumentType", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", "InvoiceUpload")
                        .WithMany("AttachmentDocumentTypes")
                        .HasForeignKey("DocumentUploadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InvoiceUpload");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentClassification", b =>
                {
                    b.HasOne("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.SecretaryDocumentUpload", "SecretaryDocumentUpload")
                        .WithMany("Classifications")
                        .HasForeignKey("SecretaryDocumentUploadId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("SecretaryDocumentUpload");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AdditionalWork.AdditionalWorkShell", b =>
                {
                    b.Navigation("AdditionalWorkLines");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.AppUser", b =>
                {
                    b.Navigation("AdditionalWorkShells");

                    b.Navigation("EmailNotification")
                        .IsRequired();

                    b.Navigation("JudgedAdditionalWorkShells");

                    b.Navigation("JudgedTigShells");

                    b.Navigation("PartnerEndCompCertRequests");

                    b.Navigation("PreparedMails");

                    b.Navigation("SubstitutionAdditionalWorkShells");

                    b.Navigation("SubstitutionEmployees");

                    b.Navigation("SubstitutionSubtitutes");

                    b.Navigation("SubstitutionTigShells");

                    b.Navigation("TigShells");

                    b.Navigation("UserReg")
                        .IsRequired();
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.DocumentUpload", b =>
                {
                    b.Navigation("AttachmentDocumentTypes");

                    b.Navigation("DocumentAnalysisResults");

                    b.Navigation("DocumentClassificationResults");

                    b.Navigation("DocumentEloResults");

                    b.Navigation("DocumentValidationResults");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.YearlyCalendarConfig", b =>
                {
                    b.Navigation("UnusualWorkdays");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Tig.TigShell", b =>
                {
                    b.Navigation("TigAdvances");

                    b.Navigation("TigLines");

                    b.Navigation("TigRetentions");
                });

            modelBuilder.Entity("PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document.SecretaryDocumentUpload", b =>
                {
                    b.Navigation("Classifications");
                });
#pragma warning restore 612, 618
        }
    }
}

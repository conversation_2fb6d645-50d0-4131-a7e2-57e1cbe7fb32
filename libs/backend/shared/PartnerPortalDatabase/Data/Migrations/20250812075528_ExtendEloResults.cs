﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data.Migrations
{
    /// <inheritdoc />
    public partial class ExtendEloResults : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SearchRequestXml",
                table: "DocumentEloResults",
                type: "nvarchar(max)",
                nullable: true
            );

            migrationBuilder.AddColumn<string>(
                name: "SearchResponseXml",
                table: "DocumentEloResults",
                type: "nvarchar(max)",
                nullable: true
            );
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(name: "SearchRequestXml", table: "DocumentEloResults");

            migrationBuilder.DropColumn(name: "SearchResponseXml", table: "DocumentEloResults");
        }
    }
}

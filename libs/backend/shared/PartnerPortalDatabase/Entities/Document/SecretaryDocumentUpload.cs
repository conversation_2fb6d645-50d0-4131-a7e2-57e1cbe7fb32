using System.ComponentModel.DataAnnotations;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

public class SecretaryDocumentUpload : DocumentUpload
{
    [MaxLength(255)]
    public string? ContactNo { get; set; }

    [MaxLength(255)]
    public string? ContactName { get; set; }

    public DateTime? SecretaryDateOfArrival { get; set; }

    public SecretaryUploadTypeEnum UploadType { get; set; }

    public ICollection<DocumentClassification> Classifications { get; set; } = [];

    public DateTime? GetEffectiveArrivalDate()
    {
        return SecretaryDateOfArrival ?? DateOfArrival;
    }

    public void UpdateAttachments(List<SecretaryAttachmentDocumentTypeDto> requestAttachments)
    {
        var existingAttachments = AttachmentDocumentTypes.ToDictionary(a => a.AttachmentId);
        var requestedAttachments = requestAttachments.ToDictionary(a => a.Attachment.FileId);

        var attachmentsToRemove = AttachmentDocumentTypes
            .Where(a => !requestedAttachments.ContainsKey(a.AttachmentId))
            .ToList();

        foreach (var attachment in attachmentsToRemove)
        {
            AttachmentDocumentTypes.Remove(attachment);
        }

        foreach (var requestAttachment in requestAttachments)
        {
            var attachmentId = requestAttachment.Attachment.FileId;

            if (!existingAttachments.ContainsKey(attachmentId))
            {
                AttachmentDocumentTypes.Add(
                    new AttachmentDocumentType { DocumentUploadId = Id, AttachmentId = attachmentId }
                );
            }
        }
    }
}

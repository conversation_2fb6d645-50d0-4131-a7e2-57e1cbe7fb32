using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

public class DocumentClassification : DocumentProcessingSource
{
    public Guid SecretaryDocumentUploadId { get; set; }
    public SecretaryDocumentUpload SecretaryDocumentUpload { get; set; } = default!;

    public required int StartPage { get; set; }
    public required int EndPage { get; set; }

    public double? Confidence { get; set; }
}

public class DocumentClassificationEntityTypeConfiguration : IEntityTypeConfiguration<DocumentClassification>
{
    public void Configure(EntityTypeBuilder<DocumentClassification> builder)
    {
        builder.HasIndex(d => d.DocumentType);
        builder
            .HasOne(d => d.SecretaryDocumentUpload)
            .WithMany(s => s.Classifications)
            .HasForeignKey(d => d.SecretaryDocumentUploadId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}

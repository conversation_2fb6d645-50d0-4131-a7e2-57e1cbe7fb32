using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

public abstract class DocumentUpload : EntityBase.EntityBase
{
    [Key]
    public Guid Id { get; set; }
    public int UserId { get; set; }
    public AppUser User { get; set; } = default!;

    [MaxLength(255)]
    public string? ProjectNo { get; set; }

    [MaxLength(255)]
    public string? ProjectDescription { get; set; }

    [MaxLength(255)]
    public string? ContractNo { get; set; }

    [MaxLength(255)]
    public string? Comment { get; set; }

    public UploadStatusEnum Status { get; set; }

    /// <summary>
    /// Érkeztetés dátuma. Ez a dátum azt jelzi, hogy hivatalosan mikor került be a számla a rendszerbe,
    /// és referencia pontként szolgál a számla tartalma alapján (például fizetési határidő meghatározásához),
    /// hogy mikor kell kifizetni az adott számlát.
    /// </summary>
    public DateTime? DateOfArrival { get; set; }

    public AccountTypeEnum? Type { get; set; }

    [Timestamp]
    public byte[] RowVersion { get; set; } = default!;

    public ICollection<AttachmentDocumentType> AttachmentDocumentTypes { get; set; } = default!;

    public ICollection<DocumentEloResult> DocumentEloResults { get; set; } = default!;

    public ICollection<DocumentValidationResult> DocumentValidationResults { get; set; } = default!;

    public ICollection<DocumentAnalysisResult> DocumentAnalysisResults { get; set; } = default!;

    public ICollection<DocumentClassificationResult> DocumentClassificationResults { get; set; } = default!;

    public void ValidateSubmit(out string? errorMessage)
    {
        if (AttachmentDocumentTypes.Count == 0)
        {
            errorMessage = "Invoice must have at least one attachment to be submitted";
            return;
        }

        var invoiceCount = AttachmentDocumentTypes.Count(x => x.IsInvoiceType);
        var completionCertificateCount = AttachmentDocumentTypes.Count(x =>
            x.DocumentType == DocumentType.CompletionCert
        );

        if (Type == AccountTypeEnum.TigInvoice)
        {
            if (!(invoiceCount > 0 && completionCertificateCount > 0))
            {
                errorMessage = "TIG invoices must have at least one invoice and one completion certificate";
                return;
            }
        }

        if (invoiceCount > 1 && completionCertificateCount > 1)
        {
            errorMessage =
                "Either multiple invoices with one completion certificate or multiple completion certificates with one invoice are allowed, but not both";
            return;
        }

        errorMessage = null;
    }

    public List<AttachmentDocumentType> GetProcessableAttachments()
    {
        return
        [
            .. AttachmentDocumentTypes.Where(x => x.IsInvoiceType || x.DocumentType == DocumentType.CompletionCert),
        ];
    }
}

public class DocumentUploadEntityTypeConfiguration : IEntityTypeConfiguration<DocumentUpload>
{
    public void Configure(EntityTypeBuilder<DocumentUpload> builder)
    {
        builder.UseTphMappingStrategy();
        builder
            .HasDiscriminator<string>("Discriminator")
            .HasValue<PartnerDocumentUpload>(nameof(PartnerDocumentUpload))
            .HasValue<SecretaryDocumentUpload>(nameof(SecretaryDocumentUpload));
    }
}

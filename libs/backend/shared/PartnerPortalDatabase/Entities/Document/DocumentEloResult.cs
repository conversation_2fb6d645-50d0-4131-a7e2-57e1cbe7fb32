using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

/// <summary>
/// Represents the result of an ELO job for an invoice in a document upload.
/// </summary>
public class DocumentEloResult : EntityBase.EntityBase
{
    public int Id { get; set; }

    public Guid DocumentUploadId { get; set; }

    public DocumentUpload DocumentUpload { get; set; } = default!;

    public int ProcessingSourceId { get; set; }
    public DocumentProcessingSource ProcessingSource { get; set; } = default!;

    public DocumentEloStatusEnum Status { get; set; }

    public string? Result { get; set; }

    [MaxLength(2500)]
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// The number of times the ELO flow has been retried manually.
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// The request XML sent to ELO.
    /// </summary>
    public string? CreateRequestXml { get; set; }

    /// <summary>
    /// The response XML received from ELO.
    /// </summary>
    public string? CreateResponseXml { get; set; }

    /// <summary>
    /// The request XML sent to ELO.
    /// </summary>
    public string? UploadRequestXml { get; set; }

    /// <summary>
    /// The response XML received from ELO.
    /// </summary>
    public string? UploadResponseXml { get; set; }

    /// <summary>
    /// The GUID of the entity created in ELO.
    /// </summary>
    public string? EloGuid { get; set; }

    /// <summary>
    /// The registration ID of the entity created in ELO.
    /// </summary>
    public string? EloRegId { get; set; }

    /// <summary>
    /// BC entitás azonosító
    /// </summary>
    public string? BcId { get; set; }

    /// <summary>
    /// BC könyvelt rekord azonosító
    /// </summary>
    public string? BcRegId { get; set; }

    /// <summary>
    /// The ID of the file uploaded to ELO. response of uploadFileResponse
    /// </summary>
    public string? FileId { get; set; }

    /// <summary>
    /// The request XML sent to ELO.
    /// </summary>
    public string? StartWorkflowRequestXml { get; set; }

    /// <summary>
    /// The response XML received from ELO.
    /// </summary>
    public string? StartWorkflowResponseXml { get; set; }

    /// <summary>
    /// The ID of the workflow started in ELO.
    /// </summary>
    public string? WorkflowId { get; set; }

    /// <summary>
    /// The request XML sent to ELO.
    /// </summary>
    public string? SearchRequestXml { get; set; }

    /// <summary>
    /// The response XML received from ELO.
    /// </summary>
    public string? SearchResponseXml { get; set; }

    public void SetErrorMessage(string? errorMessage)
    {
        if (errorMessage != null)
        {
            ErrorMessage = errorMessage.Length > 2500 ? errorMessage[..2500] : errorMessage;
        }
    }
}

public class DocumentEloResultEntityTypeConfiguration : IEntityTypeConfiguration<DocumentEloResult>
{
    public void Configure(EntityTypeBuilder<DocumentEloResult> builder)
    {
        builder
            .HasOne(d => d.DocumentUpload)
            .WithMany(u => u.DocumentEloResults)
            .HasForeignKey(d => d.DocumentUploadId)
            .OnDelete(DeleteBehavior.NoAction);

        builder
            .HasOne(d => d.ProcessingSource)
            .WithMany()
            .HasForeignKey(d => d.ProcessingSourceId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasIndex(d => d.DocumentUploadId);
        builder.HasIndex(d => d.ProcessingSourceId).IsUnique();
    }
}

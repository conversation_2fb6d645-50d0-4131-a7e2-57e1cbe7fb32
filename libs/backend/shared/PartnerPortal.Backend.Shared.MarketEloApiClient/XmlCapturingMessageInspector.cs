using System.ServiceModel;
using System.ServiceModel.Channels;
using System.ServiceModel.Description;
using System.ServiceModel.Dispatcher;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace PartnerPortal.Backend.Shared.MarketEloApiClient;

/// <summary>
/// Message inspector that captures the XML of SOAP requests for storage.
/// This is designed to work in a scoped service context where each client instance
/// has its own inspector instance.
/// </summary>
public class XmlCapturingMessageBehavior : IClientMessageInspector, IEndpointBehavior
{
    private readonly ILogger _logger;
    private string _lastRequestXml = string.Empty;
    private string _lastResponseXml = string.Empty;
    private readonly object _lockObject = new();

    public XmlCapturingMessageBehavior(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public object? BeforeSendRequest(ref Message request, IClientChannel channel)
    {
        try
        {
            lock (_lockObject)
            {
                _lastRequestXml = FormatMessage(ref request);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error capturing SOAP XML request");
        }

        return null;
    }

    public void AfterReceiveReply(ref Message reply, object correlationState)
    {
        try
        {
            lock (_lockObject)
            {
                _lastResponseXml = FormatMessage(ref reply);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error capturing SOAP XML response");
        }
    }

    /// <summary>
    /// Gets the XML of the last request made through this inspector instance
    /// </summary>
    public string GetLastRequestXml()
    {
        lock (_lockObject)
        {
            return _lastRequestXml;
        }
    }

    public string GetLastResponseXml()
    {
        lock (_lockObject)
        {
            return _lastResponseXml;
        }
    }

    private static string FormatMessage(ref Message message)
    {
        var buffer = message.CreateBufferedCopy(int.MaxValue);
        message = buffer.CreateMessage();

        var copy = buffer.CreateMessage();
        using var stream = new MemoryStream();
        using var writer = XmlWriter.Create(stream, new XmlWriterSettings { Indent = true });
        copy.WriteMessage(writer);
        writer.Flush();
        stream.Position = 0;

        using var reader = new StreamReader(stream);
        return reader.ReadToEnd();
    }

    // IEndpointBehavior implementation
    public void AddBindingParameters(ServiceEndpoint endpoint, BindingParameterCollection bindingParameters) { }

    public void ApplyClientBehavior(ServiceEndpoint endpoint, ClientRuntime clientRuntime)
    {
        clientRuntime.ClientMessageInspectors.Add(this);
    }

    public void ApplyDispatchBehavior(ServiceEndpoint endpoint, EndpointDispatcher endpointDispatcher) { }

    public void Validate(ServiceEndpoint endpoint) { }
}

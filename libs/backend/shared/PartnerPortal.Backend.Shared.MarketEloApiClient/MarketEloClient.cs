using System.ServiceModel;
using System.ServiceModel.Channels;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.Common.Utils;

namespace PartnerPortal.Backend.Shared.MarketEloApiClient;

public class MarketEloClient : IDisposable
{
    private readonly ELOWebServiceImplClient _client;
    private readonly XmlCapturingMessageBehavior _xmlCapturingBehavior;

    public MarketEloClient(IConfiguration configuration, ILogger<MarketEloClient> logger)
    {
        var endpoint = configuration.GetConfigValue<string>("EloConfiguration:Endpoint");
        var username = configuration.GetConfigValue<string>("EloConfiguration:Username");
        var password = configuration.GetConfigValue<string>("EloConfiguration:Password");

        var binding = CreateBinding(endpoint);
        var address = new EndpointAddress(endpoint);
        _client = new ELOWebServiceImplClient(binding, address);
        _client.ClientCredentials.UserName.UserName = username;
        _client.ClientCredentials.UserName.Password = password;
        _client.Endpoint.EndpointBehaviors.Add(new LoggingMessageBehavior(logger, "ELO API"));
        _client.Endpoint.EndpointBehaviors.Add(new RemoveMustUnderstandBehavior());
        _xmlCapturingBehavior = new XmlCapturingMessageBehavior(logger);
        _client.Endpoint.EndpointBehaviors.Add(_xmlCapturingBehavior);
    }

    private static Binding CreateBinding(string endpoint)
    {
        if (endpoint.StartsWith("https://"))
        {
            var binding = new BasicHttpsBinding(BasicHttpsSecurityMode.Transport)
            {
                MaxReceivedMessageSize = int.MaxValue,
                MaxBufferSize = int.MaxValue,
                ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max,
            };
            binding.Security.Transport.ClientCredentialType = HttpClientCredentialType.Basic;
            return binding;
        }
        else
        {
            var binding = new BasicHttpBinding(BasicHttpSecurityMode.TransportCredentialOnly)
            {
                MaxReceivedMessageSize = int.MaxValue,
                MaxBufferSize = int.MaxValue,
                ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max,
            };
            binding.Security.Transport.ClientCredentialType = HttpClientCredentialType.Basic;
            return binding;
        }
        ;
    }

    public async Task<(CreateEntityResponse response, string requestXml, string responseXml)> CreateEntityAsync(
        CreateEntityRequest request
    )
    {
        try
        {
            var response = await _client.createEntityAsync(request);
            string xmlRequest = _xmlCapturingBehavior.GetLastRequestXml();
            string xmlResponse = _xmlCapturingBehavior.GetLastResponseXml();
            return (response.response, xmlRequest, xmlResponse);
        }
        catch (Exception ex)
        {
            throw new EloApiException(ex);
        }
    }

    public async Task<(UploadFileResponse response, string requestXml, string responseXml)> UploadFileAsync(
        UploadFileRequest request
    )
    {
        try
        {
            var response = await _client.uploadFileAsync(request);
            string xmlRequest = _xmlCapturingBehavior.GetLastRequestXml();
            string xmlResponse = _xmlCapturingBehavior.GetLastResponseXml();
            return (response.response, xmlRequest, xmlResponse);
        }
        catch (Exception ex)
        {
            throw new EloApiException(ex);
        }
    }

    public async Task<(StartWorkflowResponse response, string requestXml, string responseXml)> StartWorkflowAsync(
        StartWorkflowRequest request
    )
    {
        try
        {
            var response = await _client.startWorkflowAsync(request);
            string xmlRequest = _xmlCapturingBehavior.GetLastRequestXml();
            string xmlResponse = _xmlCapturingBehavior.GetLastResponseXml();
            return (response.response, xmlRequest, xmlResponse);
        }
        catch (Exception ex)
        {
            throw new EloApiException(ex);
        }
    }

    public async Task<(SearchEntityResponse response, string requestXml, string responseXml)> SearchEntityAsync(
        SearchEntityRequest request
    )
    {
        try
        {
            var response = await _client.searchEntityAsync(request);
            string xmlRequest = _xmlCapturingBehavior.GetLastRequestXml();
            string xmlResponse = _xmlCapturingBehavior.GetLastResponseXml();
            return (response.response, xmlRequest, xmlResponse);
        }
        catch (Exception ex)
        {
            throw new EloApiException(ex);
        }
    }

    public void Dispose()
    {
        try
        {
            if (_client.State != CommunicationState.Closed)
            {
                _client.Close();
            }
        }
        catch (Exception)
        {
            _client.Abort();
        }

        GC.SuppressFinalize(this);
    }
}

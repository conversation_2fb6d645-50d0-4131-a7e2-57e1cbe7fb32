﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PartnerPortal.Backend.Shared.MarketEloApiClient
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", ConfigurationName="PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl")]
    public interface ELOWebServiceImpl
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/countElementsRe" +
            "quest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/countElementsRe" +
            "sponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.countElementsResponse1> countElementsAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.countElementsRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/updateEntityReq" +
            "uest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/updateEntityRes" +
            "ponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.updateEntityResponse1> updateEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.updateEntityRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/uploadFileReque" +
            "st", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/uploadFileRespo" +
            "nse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.uploadFileResponse1> uploadFileAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.uploadFileRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/rejectEntityReq" +
            "uest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/rejectEntityRes" +
            "ponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.rejectEntityResponse1> rejectEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.rejectEntityRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createEntityReq" +
            "uest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createEntityRes" +
            "ponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.createEntityResponse1> createEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.createEntityRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/linkRecordReque" +
            "st", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/linkRecordRespo" +
            "nse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.linkRecordResponse1> linkRecordAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.linkRecordRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/sendNotificatio" +
            "nRequest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/sendNotificatio" +
            "nResponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.sendNotificationResponse1> sendNotificationAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.sendNotificationRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/pingRequest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/pingResponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.pingResponse> pingAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.pingRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/startWorkflowRe" +
            "quest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/startWorkflowRe" +
            "sponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.startWorkflowResponse1> startWorkflowAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.startWorkflowRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createDataBacku" +
            "pRequest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/createDataBacku" +
            "pResponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.createDataBackupResponse1> createDataBackupAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.createDataBackupRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/searchEntityReq" +
            "uest", ReplyAction="http://webservice.market.module.eloex.ovitas.hu/ELOWebServiceImpl/searchEntityRes" +
            "ponse")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseResponse))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseRequest))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(resultEntity[]))]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.searchEntityResponse1> searchEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.searchEntityRequest1 request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class CountElementsRequest : baseRequest
    {
        
        private filter filterField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public filter filter
        {
            get
            {
                return this.filterField;
            }
            set
            {
                this.filterField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class filter
    {
        
        private string companyIdField;
        
        private string projectIdField;
        
        private string maskField;
        
        private entityDataField[] fieldsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string companyId
        {
            get
            {
                return this.companyIdField;
            }
            set
            {
                this.companyIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string projectId
        {
            get
            {
                return this.projectIdField;
            }
            set
            {
                this.projectIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string mask
        {
            get
            {
                return this.maskField;
            }
            set
            {
                this.maskField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("fields", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public entityDataField[] fields
        {
            get
            {
                return this.fieldsField;
            }
            set
            {
                this.fieldsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class entityDataField
    {
        
        private string keyField;
        
        private string valueField;
        
        private string typeField;
        
        private int rowIndexField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string key
        {
            get
            {
                return this.keyField;
            }
            set
            {
                this.keyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public int rowIndex
        {
            get
            {
                return this.rowIndexField;
            }
            set
            {
                this.rowIndexField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class resultEntity
    {
        
        private identifiers identifiersField;
        
        private entityData entityDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public entityData entityData
        {
            get
            {
                return this.entityDataField;
            }
            set
            {
                this.entityDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class identifiers
    {
        
        private string eloGuidField;
        
        private string eloRegIdField;
        
        private string bcIdField;
        
        private string bcRegIdField;
        
        private string extIdField;
        
        private string barCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string eloGuid
        {
            get
            {
                return this.eloGuidField;
            }
            set
            {
                this.eloGuidField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string eloRegId
        {
            get
            {
                return this.eloRegIdField;
            }
            set
            {
                this.eloRegIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string bcId
        {
            get
            {
                return this.bcIdField;
            }
            set
            {
                this.bcIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string bcRegId
        {
            get
            {
                return this.bcRegIdField;
            }
            set
            {
                this.bcRegIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string extId
        {
            get
            {
                return this.extIdField;
            }
            set
            {
                this.extIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string barCode
        {
            get
            {
                return this.barCodeField;
            }
            set
            {
                this.barCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class entityData
    {
        
        private string partnerIdField;
        
        private string projectIdField;
        
        private string bcLinkField;
        
        private entityDataField[] fieldsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string partnerId
        {
            get
            {
                return this.partnerIdField;
            }
            set
            {
                this.partnerIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string projectId
        {
            get
            {
                return this.projectIdField;
            }
            set
            {
                this.projectIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string bcLink
        {
            get
            {
                return this.bcLinkField;
            }
            set
            {
                this.bcLinkField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("fields", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public entityDataField[] fields
        {
            get
            {
                return this.fieldsField;
            }
            set
            {
                this.fieldsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class responseData
    {
        
        private int codeField;
        
        private string messageField;
        
        private string timeStampField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public int code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string message
        {
            get
            {
                return this.messageField;
            }
            set
            {
                this.messageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string timeStamp
        {
            get
            {
                return this.timeStampField;
            }
            set
            {
                this.timeStampField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SearchEntityResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CreateDataBackupResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LinkRecordResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(StartWorkflowResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CreateEntityResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SendNotificationResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RejectEntityResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CountElementsResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UploadFileResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UpdateEntityResponse))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class BaseResponse
    {
        
        private responseData responseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public responseData response
        {
            get
            {
                return this.responseField;
            }
            set
            {
                this.responseField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class SearchEntityResponse : BaseResponse
    {
        
        private resultEntity[] resultListField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public resultEntity[] resultList
        {
            get
            {
                return this.resultListField;
            }
            set
            {
                this.resultListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class CreateDataBackupResponse : BaseResponse
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class LinkRecordResponse : BaseResponse
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class StartWorkflowResponse : BaseResponse
    {
        
        private string workflowIdField;
        
        private identifiers identifiersField;
        
        private entityData entityDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string workflowId
        {
            get
            {
                return this.workflowIdField;
            }
            set
            {
                this.workflowIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public entityData entityData
        {
            get
            {
                return this.entityDataField;
            }
            set
            {
                this.entityDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class CreateEntityResponse : BaseResponse
    {
        
        private identifiers identifiersField;
        
        private entityData entityDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public entityData entityData
        {
            get
            {
                return this.entityDataField;
            }
            set
            {
                this.entityDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class SendNotificationResponse : BaseResponse
    {
        
        private responseData response1Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("response", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public responseData response1
        {
            get
            {
                return this.response1Field;
            }
            set
            {
                this.response1Field = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class RejectEntityResponse : BaseResponse
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class CountElementsResponse : BaseResponse
    {
        
        private responseData response1Field;
        
        private int countField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("response", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public responseData response1
        {
            get
            {
                return this.response1Field;
            }
            set
            {
                this.response1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public int count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class UploadFileResponse : BaseResponse
    {
        
        private string fileIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string fileId
        {
            get
            {
                return this.fileIdField;
            }
            set
            {
                this.fileIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class UpdateEntityResponse : BaseResponse
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class searchEntityField
    {
        
        private string keyField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string key
        {
            get
            {
                return this.keyField;
            }
            set
            {
                this.keyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class searchFilter
    {
        
        private string projectIdField;
        
        private searchEntityField[] fieldsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string projectId
        {
            get
            {
                return this.projectIdField;
            }
            set
            {
                this.projectIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("fields", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public searchEntityField[] fields
        {
            get
            {
                return this.fieldsField;
            }
            set
            {
                this.fieldsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class fileData
    {
        
        private string pathField;
        
        private string mimeField;
        
        private string typeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string path
        {
            get
            {
                return this.pathField;
            }
            set
            {
                this.pathField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string mime
        {
            get
            {
                return this.mimeField;
            }
            set
            {
                this.mimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SearchEntityRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CreateDataBackupRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LinkRecordRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(StartWorkflowRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CreateEntityRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SendNotificationRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RejectEntityRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CountElementsRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UploadFileRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UpdateEntityRequest))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class baseRequest
    {
        
        private string systemIdField;
        
        private string userField;
        
        private string companyIdField;
        
        private string entityTypeField;
        
        private string timeStampField;
        
        public baseRequest()
        {
            this.systemIdField = "BC";
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.ComponentModel.DefaultValueAttribute("BC")]
        public string systemId
        {
            get
            {
                return this.systemIdField;
            }
            set
            {
                this.systemIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string user
        {
            get
            {
                return this.userField;
            }
            set
            {
                this.userField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string companyId
        {
            get
            {
                return this.companyIdField;
            }
            set
            {
                this.companyIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string entityType
        {
            get
            {
                return this.entityTypeField;
            }
            set
            {
                this.entityTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string timeStamp
        {
            get
            {
                return this.timeStampField;
            }
            set
            {
                this.timeStampField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class SearchEntityRequest : baseRequest
    {
        
        private identifiers identifiersField;
        
        private searchFilter filterField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public searchFilter filter
        {
            get
            {
                return this.filterField;
            }
            set
            {
                this.filterField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class CreateDataBackupRequest : baseRequest
    {
        
        private identifiers identifiersField;
        
        private string backupIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string backupId
        {
            get
            {
                return this.backupIdField;
            }
            set
            {
                this.backupIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class LinkRecordRequest : baseRequest
    {
        
        private identifiers identifiersField;
        
        private string recordIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string recordId
        {
            get
            {
                return this.recordIdField;
            }
            set
            {
                this.recordIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class StartWorkflowRequest : baseRequest
    {
        
        private string workflowTemplateField;
        
        private identifiers identifiersField;
        
        private entityData entityDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string workflowTemplate
        {
            get
            {
                return this.workflowTemplateField;
            }
            set
            {
                this.workflowTemplateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public entityData entityData
        {
            get
            {
                return this.entityDataField;
            }
            set
            {
                this.entityDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class CreateEntityRequest : baseRequest
    {
        
        private identifiers identifiersField;
        
        private entityData entityDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public entityData entityData
        {
            get
            {
                return this.entityDataField;
            }
            set
            {
                this.entityDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class SendNotificationRequest : baseRequest
    {
        
        private string[] addressedUsersField;
        
        private string messageField;
        
        private identifiers identifiersField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("addressedUsers", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string[] addressedUsers
        {
            get
            {
                return this.addressedUsersField;
            }
            set
            {
                this.addressedUsersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string message
        {
            get
            {
                return this.messageField;
            }
            set
            {
                this.messageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class RejectEntityRequest : baseRequest
    {
        
        private identifiers identifiersField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class UploadFileRequest : baseRequest
    {
        
        private identifiers identifiersField;
        
        private fileData fileField;
        
        private string[] linkIdentifiersField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public fileData file
        {
            get
            {
                return this.fileField;
            }
            set
            {
                this.fileField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("linkIdentifiers", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string[] linkIdentifiers
        {
            get
            {
                return this.linkIdentifiersField;
            }
            set
            {
                this.linkIdentifiersField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/")]
    public partial class UpdateEntityRequest : baseRequest
    {
        
        private bool syncRequestField;
        
        private identifiers identifiersField;
        
        private entityData entityDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public bool syncRequest
        {
            get
            {
                return this.syncRequestField;
            }
            set
            {
                this.syncRequestField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public identifiers identifiers
        {
            get
            {
                return this.identifiersField;
            }
            set
            {
                this.identifiersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public entityData entityData
        {
            get
            {
                return this.entityDataField;
            }
            set
            {
                this.entityDataField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="countElements", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class countElementsRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.CountElementsRequest request;
        
        public countElementsRequest1()
        {
        }
        
        public countElementsRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.CountElementsRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="countElementsResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class countElementsResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.CountElementsResponse response;
        
        public countElementsResponse1()
        {
        }
        
        public countElementsResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.CountElementsResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="updateEntity", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class updateEntityRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.UpdateEntityRequest request;
        
        public updateEntityRequest1()
        {
        }
        
        public updateEntityRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.UpdateEntityRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="updateEntityResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class updateEntityResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.UpdateEntityResponse response;
        
        public updateEntityResponse1()
        {
        }
        
        public updateEntityResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.UpdateEntityResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="uploadFile", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class uploadFileRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.UploadFileRequest request;
        
        public uploadFileRequest1()
        {
        }
        
        public uploadFileRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.UploadFileRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="uploadFileResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class uploadFileResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.UploadFileResponse response;
        
        public uploadFileResponse1()
        {
        }
        
        public uploadFileResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.UploadFileResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="rejectEntity", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class rejectEntityRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.RejectEntityRequest request;
        
        public rejectEntityRequest1()
        {
        }
        
        public rejectEntityRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.RejectEntityRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="rejectEntityResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class rejectEntityResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.RejectEntityResponse response;
        
        public rejectEntityResponse1()
        {
        }
        
        public rejectEntityResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.RejectEntityResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="createEntity", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class createEntityRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.CreateEntityRequest request;
        
        public createEntityRequest1()
        {
        }
        
        public createEntityRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.CreateEntityRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="createEntityResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class createEntityResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.CreateEntityResponse response;
        
        public createEntityResponse1()
        {
        }
        
        public createEntityResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.CreateEntityResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="linkRecord", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class linkRecordRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.LinkRecordRequest request;
        
        public linkRecordRequest1()
        {
        }
        
        public linkRecordRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.LinkRecordRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="linkRecordResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class linkRecordResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.LinkRecordResponse response;
        
        public linkRecordResponse1()
        {
        }
        
        public linkRecordResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.LinkRecordResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="sendNotification", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class sendNotificationRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.SendNotificationRequest request;
        
        public sendNotificationRequest1()
        {
        }
        
        public sendNotificationRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.SendNotificationRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="sendNotificationResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class sendNotificationResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.SendNotificationResponse response;
        
        public sendNotificationResponse1()
        {
        }
        
        public sendNotificationResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.SendNotificationResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ping", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class pingRequest
    {
        
        public pingRequest()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="pingResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class pingResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string @return;
        
        public pingResponse()
        {
        }
        
        public pingResponse(string @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="startWorkflow", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class startWorkflowRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.StartWorkflowRequest request;
        
        public startWorkflowRequest1()
        {
        }
        
        public startWorkflowRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.StartWorkflowRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="startWorkflowResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class startWorkflowResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.StartWorkflowResponse response;
        
        public startWorkflowResponse1()
        {
        }
        
        public startWorkflowResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.StartWorkflowResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="createDataBackup", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class createDataBackupRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.CreateDataBackupRequest request;
        
        public createDataBackupRequest1()
        {
        }
        
        public createDataBackupRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.CreateDataBackupRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="createDataBackupResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class createDataBackupResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.CreateDataBackupResponse response;
        
        public createDataBackupResponse1()
        {
        }
        
        public createDataBackupResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.CreateDataBackupResponse response)
        {
            this.response = response;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="searchEntity", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class searchEntityRequest1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.SearchEntityRequest request;
        
        public searchEntityRequest1()
        {
        }
        
        public searchEntityRequest1(PartnerPortal.Backend.Shared.MarketEloApiClient.SearchEntityRequest request)
        {
            this.request = request;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="searchEntityResponse", WrapperNamespace="http://webservice.market.module.eloex.ovitas.hu/", IsWrapped=true)]
    public partial class searchEntityResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://webservice.market.module.eloex.ovitas.hu/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public PartnerPortal.Backend.Shared.MarketEloApiClient.SearchEntityResponse response;
        
        public searchEntityResponse1()
        {
        }
        
        public searchEntityResponse1(PartnerPortal.Backend.Shared.MarketEloApiClient.SearchEntityResponse response)
        {
            this.response = response;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public interface ELOWebServiceImplChannel : PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public partial class ELOWebServiceImplClient : System.ServiceModel.ClientBase<PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl>, PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl
    {
        
        public ELOWebServiceImplClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.countElementsResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.countElementsAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.countElementsRequest1 request)
        {
            return base.Channel.countElementsAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.countElementsResponse1> countElementsAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.CountElementsRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.countElementsRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.countElementsRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).countElementsAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.updateEntityResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.updateEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.updateEntityRequest1 request)
        {
            return base.Channel.updateEntityAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.updateEntityResponse1> updateEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.UpdateEntityRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.updateEntityRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.updateEntityRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).updateEntityAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.uploadFileResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.uploadFileAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.uploadFileRequest1 request)
        {
            return base.Channel.uploadFileAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.uploadFileResponse1> uploadFileAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.UploadFileRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.uploadFileRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.uploadFileRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).uploadFileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.rejectEntityResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.rejectEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.rejectEntityRequest1 request)
        {
            return base.Channel.rejectEntityAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.rejectEntityResponse1> rejectEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.RejectEntityRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.rejectEntityRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.rejectEntityRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).rejectEntityAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.createEntityResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.createEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.createEntityRequest1 request)
        {
            return base.Channel.createEntityAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.createEntityResponse1> createEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.CreateEntityRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.createEntityRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.createEntityRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).createEntityAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.linkRecordResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.linkRecordAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.linkRecordRequest1 request)
        {
            return base.Channel.linkRecordAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.linkRecordResponse1> linkRecordAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.LinkRecordRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.linkRecordRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.linkRecordRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).linkRecordAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.sendNotificationResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.sendNotificationAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.sendNotificationRequest1 request)
        {
            return base.Channel.sendNotificationAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.sendNotificationResponse1> sendNotificationAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.SendNotificationRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.sendNotificationRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.sendNotificationRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).sendNotificationAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.pingResponse> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.pingAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.pingRequest request)
        {
            return base.Channel.pingAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.pingResponse> pingAsync()
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.pingRequest inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.pingRequest();
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).pingAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.startWorkflowResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.startWorkflowAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.startWorkflowRequest1 request)
        {
            return base.Channel.startWorkflowAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.startWorkflowResponse1> startWorkflowAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.StartWorkflowRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.startWorkflowRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.startWorkflowRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).startWorkflowAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.createDataBackupResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.createDataBackupAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.createDataBackupRequest1 request)
        {
            return base.Channel.createDataBackupAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.createDataBackupResponse1> createDataBackupAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.CreateDataBackupRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.createDataBackupRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.createDataBackupRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).createDataBackupAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.searchEntityResponse1> PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl.searchEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.searchEntityRequest1 request)
        {
            return base.Channel.searchEntityAsync(request);
        }
        
        public System.Threading.Tasks.Task<PartnerPortal.Backend.Shared.MarketEloApiClient.searchEntityResponse1> searchEntityAsync(PartnerPortal.Backend.Shared.MarketEloApiClient.SearchEntityRequest request)
        {
            PartnerPortal.Backend.Shared.MarketEloApiClient.searchEntityRequest1 inValue = new PartnerPortal.Backend.Shared.MarketEloApiClient.searchEntityRequest1();
            inValue.request = request;
            return ((PartnerPortal.Backend.Shared.MarketEloApiClient.ELOWebServiceImpl)(this)).searchEntityAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        #if !NET6_0_OR_GREATER
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        #endif
    }
}

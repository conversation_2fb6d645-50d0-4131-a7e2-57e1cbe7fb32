{"name": "Backend.Shared.MarketEloApiClient", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/backend/shared/PartnerPortal.Backend.Shared.MarketEloApiClient", "targets": {"build": {"executor": "@nx-dotnet/core:build", "outputs": ["{workspaceRoot}/dist/libs/backend/shared/PartnerPortal.Backend.Shared.MarketEloApiClient", "{workspaceRoot}/dist/intermediates/libs/backend/shared/PartnerPortal.Backend.Shared.MarketEloApiClient"], "options": {"configuration": "Debug", "noDependencies": true}, "configurations": {"production": {"configuration": "Release"}}}, "lint": {"executor": "@nx-dotnet/core:format"}, "generate-wcf": {"executor": "nx:run-commands", "options": {"cwd": "libs/backend/shared/PartnerPortal.Backend.Shared.MarketEloApiClient", "commands": [{"command": "dotnet svcutil --update ./ServiceReference --projectFile ./PartnerPortal.Backend.Shared.MarketEloApiClient.csproj", "color": "yellow", "bgColor": "bgBlack", "description": "Generating WCF client"}]}}}, "tags": []}
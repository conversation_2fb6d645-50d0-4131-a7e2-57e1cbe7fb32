using System.ServiceModel;
using System.ServiceModel.Channels;
using System.ServiceModel.Description;
using System.ServiceModel.Dispatcher;

namespace PartnerPortal.Backend.Shared.MarketEloApiClient;

public class RemoveMustUnderstandBehavior : IEndpointBehavior, IClientMessageInspector
{
    public void AddBindingParameters(ServiceEndpoint endpoint, BindingParameterCollection bp) { }

    public void ApplyDispatchBehavior(ServiceEndpoint endpoint, EndpointDispatcher disp) { }

    public void Validate(ServiceEndpoint endpoint) { }

    public void ApplyClientBehavior(ServiceEndpoint endpoint, ClientRuntime clientRuntime)
    {
        clientRuntime.ClientMessageInspectors.Add(this);
    }

    public object? BeforeSendRequest(ref Message request, IClientChannel channel)
    {
        for (int i = 0; i < request.Headers.Count; i++)
        {
            var header = request.Headers[i];

            if (header.MustUnderstand)
            {
                request.Headers.RemoveAt(i);
            }
        }

        return null;
    }

    public void AfterReceiveReply(ref Message reply, object correlationState) { }
}

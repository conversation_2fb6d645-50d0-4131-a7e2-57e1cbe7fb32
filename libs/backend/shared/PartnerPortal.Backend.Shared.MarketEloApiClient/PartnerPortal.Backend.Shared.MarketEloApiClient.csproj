﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.ServiceModel.Duplex" />
    <PackageReference Include="System.ServiceModel.Http" />
    <PackageReference Include="System.ServiceModel.NetTcp" />
    <PackageReference Include="System.ServiceModel.Security" />
    <PackageReference Include="System.ServiceModel.Federation" />
    <PackageReference Include="System.ServiceModel.Primitives" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\shared\Common\PartnerPortal.Backend.Shared.Common.csproj" />
  </ItemGroup>
</Project>

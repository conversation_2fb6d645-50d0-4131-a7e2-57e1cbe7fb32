using PartnerPortal.Backend.Shared.Common.Utils;

namespace PartnerPortal.Backend.Shared.Common.CommonExtensions;

using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.Commons;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities;
using static PartnerPortal.Backend.Shared.Common.MarketAuth;

public static class MarketIdentityServiceExtension
{
    public const string AuthSchemeBasicForBc = "BasicAuth-BC";

    public static IServiceCollection ConfigureMarketIdentityService(this IServiceCollection services)
    {
        IConfiguration? configuration = services.BuildServiceProvider().GetService<IConfiguration>();

        services
            .AddIdentity<AppUser, IdentityRole<int>>(options =>
            {
                options.Password.RequireDigit = true;
                options.Password.RequireNonAlphanumeric = false;
                options.Password.RequireLowercase = true;
                options.Password.RequireUppercase = true;
                options.Password.RequiredUniqueChars = 0;
                options.Password.RequiredLength = 8;
                options.User.AllowedUserNameCharacters =
                    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+ ";
                options.User.RequireUniqueEmail = true;
            })
            .AddEntityFrameworkStores<UserDbContext>()
            .AddDefaultTokenProviders();

        string issuerServer = configuration.GetConfigValue<string>("Jwt:IssuerServer");
        string audienceClient = configuration.GetConfigValue<string>("Jwt:AudienceClient");
        services
            .AddAuthentication(opt =>
            {
                opt.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                opt.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = issuerServer,
                    ValidAudience = audienceClient,

                    // TODO take this from config["TokenKey"]
                    IssuerSigningKey = new SymmetricSecurityKey(
                        Encoding.UTF8.GetBytes("MFswDQYJKoZIhvcNAQEBBQADSgAwRwJAYgQjtJxNN0XMHtqK3fjUQcxfxQxHw8G9")
                    ),
                };
            });

        services
            .AddAuthorizationBuilder()
            .AddPolicy(
                "RequireAdminRole",
                policy =>
                {
                    policy.RequireRole(UserRole.Administrator.ToString());
                }
            )
            .AddPolicy(
                "RequirePartnerRole",
                policy =>
                {
                    policy.RequireRole(UserRole.Partner.ToString());
                }
            )
            .AddPolicy(
                "RequireEngineerRole",
                policy =>
                {
                    policy.RequireRole(UserRole.Engineer.ToString());
                }
            )
            .AddPolicy(
                "RequireHeadOfEngineerRole",
                policy =>
                {
                    policy.RequireRole(UserRole.HeadOfEngineer.ToString());
                }
            )
            .AddPolicy(
                "RequireEngineerOrHeadOfEngineerRole",
                policy =>
                {
                    policy.RequireRole(UserRole.Engineer.ToString(), UserRole.HeadOfEngineer.ToString());
                }
            )
            .AddPolicy(
                "RequireSecretaryRole",
                policy =>
                {
                    policy.RequireRole(UserRole.Secretary.ToString());
                }
            );

        // authenticate system REST endpoints via another provider: basic authentication with User/Pass configured in appsettings
        services
            .AddAuthentication()
            .AddScheme<AuthenticationSchemeOptions, MarketBasicAuthenticationHandler>(
                AuthSchemeBasicForBc,
                options => { }
            );

        return services;
    }
}

using PartnerPortal.Backend.Document.Utils;

namespace PartnerPortal.Backend.Document.Test.Utils;

public class PaymentMethodUtilityTests
{
    [Theory]
    [InlineData("átutalás", true)]
    [InlineData("utalás", true)]
    [InlineData("átut.", true)]
    [InlineData("utalással", true)]
    [InlineData("Átutalással", true)]
    [InlineData("banki átutalás", true)]
    [InlineData("Wire transfer", true)]
    [InlineData("Bank transfer", true)]
    [InlineData("SEPA transfer", true)]
    [InlineData("Überweisung", true)]
    [InlineData("Banküberweisung", true)]
    [InlineData("Cash", false)]
    [InlineData("Készpénz", false)]
    [InlineData("Bankkártya", false)]
    [InlineData("Card", false)]
    [InlineData("Voucher", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    [InlineData("   Wire transfer   ", true)] // Whitespace handling
    [InlineData("WIRE TRANSFER", true)] // All caps
    public void IsWireTransfer_ReturnsExpected(string? input, bool expected)
    {
        var result = PaymentMethodUtility.IsWireTransfer(input);
        Assert.Equal(expected, result);
    }
}

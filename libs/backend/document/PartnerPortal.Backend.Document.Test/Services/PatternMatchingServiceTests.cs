using Microsoft.Extensions.Logging;
using Moq;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services;

namespace PartnerPortal.Backend.Document.Test.Services;

public class PatternMatchingServiceTests
{
    private readonly Mock<ILogger<PatternMatchingService>> _loggerMock;
    private readonly PatternMatchingService _service;

    public PatternMatchingServiceTests()
    {
        _loggerMock = new Mock<ILogger<PatternMatchingService>>();
        _service = new PatternMatchingService(_loggerMock.Object);
    }

    [Fact]
    public void AnalyzeContent_FirstPatternGroup_ShouldDetectSoleEvAndRegistrationNumber()
    {
        // Arrange
        const string TestContent =
            "online\nszámlázó\nSZÁMLA\nprogram\nSorszám: EMIL-2025-29\nEladó: TÓTH EMIL\nVevő: MRKT Padló Kft\nMagyarország 1224 BUDAPEST, MINTAKERT UTCA 21\nMagyarország 1037 Budapest, Bojtár utca 51\nMagyar adószám: ********-2-43\nMagyar adószám: ********-2-41\nNyilvántartási szám: ********\nBankszámlaszám: ********-********-********\nIBAN: HU09 1040 2166 5052 7168 7848 1004\nEgyéni vállalkozó\nFizetési mód: Átutalás\nTeljesítés: 2025.07.10.\nKeltezés: 2025.06.30.\n";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "e.v. jelölés és nyilvántartási szám");
        Assert.NotNull(firstGroupResult);

        Assert.Equal(2, firstGroupResult.TotalMatchCount);

        var matches = firstGroupResult.Matches.ToList();
        Assert.Equal(2, matches.Count);

        var soleProprietorshipMatch = matches.FirstOrDefault(m => m.PatternName == "egyéni vállalkozó");
        var registrationNumberMatch = matches.FirstOrDefault(m => m.PatternName == "nyilvántartási szám variations");

        Assert.NotNull(soleProprietorshipMatch);
        Assert.NotNull(registrationNumberMatch);

        Assert.Contains("Egyéni vállalkozó", soleProprietorshipMatch.MatchedText);
        Assert.Contains("********", registrationNumberMatch.MatchedText);
    }

    [Fact]
    public void AnalyzeContent_RegistrationNumberOnly_ShouldDetectRegistrationNumber()
    {
        // Arrange - Test with only the registration number pattern
        const string TestContent = "Nyilvántartási szám: ********";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "e.v. jelölés és nyilvántartási szám");
        Assert.NotNull(firstGroupResult);

        Assert.Equal(1, firstGroupResult.TotalMatchCount);

        var match = firstGroupResult.Matches.First();
        Assert.Equal("nyilvántartási szám variations", match.PatternName);
        Assert.Equal("Nyilvántartási szám: ********", match.MatchedText);
    }

    [Fact]
    public void AnalyzeContent_EvaAbbreviation_ShouldDetectEvaAbbreviationAndWordsAroundIt()
    {
        // Arrange
        const string TestContent =
            "Számla\n2025-000038\nELADÓ\nVEVŐ\nSin Olivér Gábor ev.\nMarket Építő Zrt.\nSzentpéterfa\nBudapest\nKossuth Lajos\nBojtár utca 51.\n9799\n1037\nMagyarország\nMagyarország\nADÓSZÁM: 69040102-2-38\nADÓSZÁM: 14776355-2-44\nNYILVÁNTARTÁSI SZÁM: 52611412\n";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "e.v. jelölés és nyilvántartási szám");
        Assert.NotNull(firstGroupResult);

        Assert.Equal(2, firstGroupResult.TotalMatchCount);

        var matches = firstGroupResult.Matches.ToList();
        Assert.Equal(2, matches.Count);

        var evaMatch = matches.FirstOrDefault(m => m.PatternName == "e.v. abbreviations");
        var registrationNumberMatch = matches.FirstOrDefault(m => m.PatternName == "nyilvántartási szám variations");

        Assert.NotNull(evaMatch);
        Assert.NotNull(registrationNumberMatch);

        Assert.Equal("ev.", evaMatch.MatchedText, ignoreCase: true);
        Assert.Contains("NYILVÁNTARTÁSI SZÁM: 52611412", registrationNumberMatch.MatchedText);
        Assert.NotNull(evaMatch.ExtractedContext);
        Assert.DoesNotContain("\n", evaMatch.ExtractedContext);
        Assert.Contains("Sin Olivér Gábor ev.", evaMatch.ExtractedContext);
    }

    [Fact]
    public void AnalyzeContent_CashFlowSettlement_ShouldDetectCashFlowSettlement()
    {
        // Arrange
        const string TestContent =
            "munkaszám: KZP_FENNTART_2025\nÖsszesen:\n750 000\n202 500\n952 500\náfa 27 %: 202 500\nÖsszesen:\n952 500 Ft\nA számla kibocsátója pénzforgalmi elszámolásos.\nszámlázz.hu\nA bizonylatot a Számlázz.hu készítette.\nOldal 1/1";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "pénzforgalmi elszámolás");
        Assert.NotNull(firstGroupResult);

        Assert.Equal(1, firstGroupResult.TotalMatchCount);

        var match = firstGroupResult.Matches.First();
        Assert.Equal("pénzforgalmi elszámolásos", match.MatchedText);
        Assert.Equal("A számla kibocsátója pénzforgalmi elszámolásos.", match.ExtractedContext);
    }

    [Fact]
    public void AnalyzeContent_CashFlowSettlement_ShouldDetectCashFlowSettlement2()
    {
        // Arrange
        const string TestContent =
            ".\nÁtutalás\nÁFA törvény XIII/A Fejezete alapján Pénzforgalmi elszámolás alá tartozó bizonylat\nMEGNEVEZÉS\nMENNYISÉG\nEGYSÉGÁR\nÁFA %\nNETTÓ\nÁFA\nBRUTTÓ\n1\nBetontechnológiai műszaki szaktanácsadás\n1 db\n150 000,00 Ft\n27%\n150 000 Ft\n40 500 Ft\n190 500 Ft\nÁFA ÖSSZESÍTŐ\nNETTÓ\nÁFA\nBRUTTÓ\n27%\n150 000 Ft\n40 50";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "pénzforgalmi elszámolás");
        Assert.NotNull(firstGroupResult);

        Assert.Equal(1, firstGroupResult.TotalMatchCount);

        var match = firstGroupResult.Matches.First();
        Assert.Equal("Pénzforgalmi elszámolás", match.MatchedText);
        Assert.Equal(
            "ÁFA törvény XIII/A Fejezete alapján Pénzforgalmi elszámolás alá tartozó bizonylat",
            match.ExtractedContext
        );
    }

    [Fact]
    public void AnalyzeContent_DevizasSzamlakovetelmenyek_ShouldDetectDevizasSzamlakovetelmenyek()
    {
        // Arrange
        const string TestContent = "Árfolyam\n1 EUR = 399,30 HUF\nSzámla nettó értéke";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "Devizás számla követelmények");
        Assert.NotNull(firstGroupResult);

        var matches = firstGroupResult.Matches.ToList();
        Assert.Equal(2, matches.Count);

        var match = matches.FirstOrDefault();
        Assert.NotNull(match);
        Assert.Equal("Árfolyam 1 EUR = 399,30 HUF Számla nettó értéke", match.ExtractedContext);

        var match2 = matches.LastOrDefault();
        Assert.NotNull(match2);
        Assert.Equal("1 EUR = 399,30 HUF", match2.ExtractedContext);
    }

    [Fact]
    public void AnalyzeContent_DevizasSzamlakovetelmenyek_ShouldDetectDevizasSzamlakovetelmenyek2()
    {
        // Arrange
        const string TestContent =
            "ÁFA nélküli összesen:\n154 832,31 EUR\nÁFA árfolyama : 404,36 Ft/EUR\nÁFA összesen:\n0,00 EUR\nSzámlaérték összesen:\n154 832,31 EUR\nÁfa alap összesen:\n62 607 993,00 Ft\nÁfa érték összesen:\n0,00 Ft";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "Devizás számla követelmények");
        Assert.NotNull(firstGroupResult);

        var match = firstGroupResult.Matches.FirstOrDefault();
        Assert.NotNull(match);
        Assert.Equal("ÁFA árfolyama : 404,36 Ft/EUR", match.ExtractedContext);
    }

    [Fact]
    public void AnalyzeContent_Adomentes_ShouldDetectAdomentes()
    {
        // Arrange
        const string TestContent =
            "Az értékesítés adómentes (a HÉA-ról szóló 222/2004 sz. Törvény 43. §-ának (1) bekezdése\nszerint).";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "Adómentességi jogszabályi hivatkozás");
        Assert.NotNull(firstGroupResult);

        var match = firstGroupResult.Matches.FirstOrDefault();
        Assert.NotNull(match);
        Assert.Equal("adómentes", match.MatchedText);
        Assert.Equal("Az értékesítés adómentes (a HÉA-ról szóló 222/2004 sz.", match.ExtractedContext);
    }

    [Fact]
    public void AnalyzeContent_Adomentes_ShouldDetectAlanyiAdomentes()
    {
        // Arrange
        const string TestContent =
            "n\nAlanyi adómentes, Adómentesség leírása: Alanyi adómentes\nSzámla összesítő:\nSzámla nettó értéke\n120 000,00 HUF\nÁfa százaléka és értéke\nAAM\n0,00 HUF\nAlanyi adómentes\nÁthárított áfa összege\n0,00";

        // Act
        var results = _service.AnalyzeContent(TestContent);

        // Assert
        Assert.NotNull(results);
        Assert.NotEmpty(results);

        var firstGroupResult = results.FirstOrDefault(r => r.GroupName == "Adómentességi jogszabályi hivatkozás");
        Assert.NotNull(firstGroupResult);

        var match = firstGroupResult.Matches.FirstOrDefault();
        Assert.NotNull(match);
        Assert.Equal("adómentes", match.MatchedText);
        Assert.Equal("Alanyi adómentes, Adómentesség leírása: Alanyi adómentes", match.ExtractedContext);
    }
}

using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.DocumentProcessing.Validation;

namespace PartnerPortal.Backend.Document.Test.Services;

public class InvoiceValidatorTests
{
    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldReturnMissing_WhenBothServiceDateAndNavServiceDateAreNull(string fieldName)
    {
        // Arrange
        DateTime? serviceDate = null;
        NavInvoiceData? navValidationInvoiceData = new NavInvoiceData
        {
            InvoiceId = "TEST123",
            ServiceStartDate = null,
            ServiceEndDate = null,
        };

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(
            fieldName,
            serviceDate,
            fieldName == ELOFieldNames.ServiceStartDate
                ? navValidationInvoiceData?.ServiceStartDate
                : navValidationInvoiceData?.ServiceEndDate
        );

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.Missing, result.Status);
        Assert.Null(result.DocumentAiColumnValue);
        Assert.Null(result.NavColumnValue);
    }

    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldReturnNavdiscrepancy_WhenServiceDateAndNavServiceDateAreNotEqual(
        string fieldName
    )
    {
        // Arrange
        var serviceDate = new DateTime(2024, 1, 15);
        var navServiceDate = new DateTime(2024, 1, 20);
        NavInvoiceData navValidationInvoiceData = new NavInvoiceData
        {
            InvoiceId = "TEST123",
            ServiceStartDate = fieldName == ELOFieldNames.ServiceStartDate ? navServiceDate : null,
            ServiceEndDate = fieldName == ELOFieldNames.ServiceEndDate ? navServiceDate : null,
        };

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(
            fieldName,
            serviceDate,
            fieldName == ELOFieldNames.ServiceStartDate
                ? navValidationInvoiceData?.ServiceStartDate
                : navValidationInvoiceData?.ServiceEndDate
        );

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.NavDiscrepancy, result.Status);
        Assert.Equal("20240115", result.DocumentAiColumnValue);
        Assert.Equal("20240120", result.NavColumnValue);
    }

    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldReturnOk_WhenServiceDateAndNavServiceDateAreEqual(string fieldName)
    {
        // Arrange
        var serviceDate = new DateTime(2024, 1, 15);
        var navServiceDate = new DateTime(2024, 1, 15);
        NavInvoiceData navValidationInvoiceData = new NavInvoiceData
        {
            InvoiceId = "TEST123",
            ServiceStartDate = fieldName == ELOFieldNames.ServiceStartDate ? navServiceDate : null,
            ServiceEndDate = fieldName == ELOFieldNames.ServiceEndDate ? navServiceDate : null,
        };

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(
            fieldName,
            serviceDate,
            fieldName == ELOFieldNames.ServiceStartDate
                ? navValidationInvoiceData?.ServiceStartDate
                : navValidationInvoiceData?.ServiceEndDate
        );

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.Ok, result.Status);
        Assert.Equal("20240115", result.DocumentAiColumnValue);
        Assert.Equal("20240115", result.NavColumnValue);
    }

    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldReturnNavsource_WhenServiceDateIsNullAndNavServiceDateIsNotNull(
        string fieldName
    )
    {
        // Arrange
        DateTime? serviceDate = null;
        var navServiceDate = new DateTime(2024, 1, 15);
        NavInvoiceData navValidationInvoiceData = new NavInvoiceData
        {
            InvoiceId = "TEST123",
            ServiceStartDate = fieldName == ELOFieldNames.ServiceStartDate ? navServiceDate : null,
            ServiceEndDate = fieldName == ELOFieldNames.ServiceEndDate ? navServiceDate : null,
        };

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(
            fieldName,
            serviceDate,
            fieldName == ELOFieldNames.ServiceStartDate
                ? navValidationInvoiceData?.ServiceStartDate
                : navValidationInvoiceData?.ServiceEndDate
        );

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.Navsource, result.Status);
        Assert.Null(result.DocumentAiColumnValue);
        Assert.Equal("20240115", result.NavColumnValue);
    }

    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldReturnOk_WhenServiceDateIsNotNullAndNavValidationInvoiceDataIsNull(
        string fieldName
    )
    {
        // Arrange
        var serviceDate = new DateTime(2024, 1, 15);

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(fieldName, serviceDate, null);

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.Ok, result.Status);
        Assert.Equal("20240115", result.DocumentAiColumnValue);
        Assert.Null(result.NavColumnValue);
    }

    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldReturnMissing_WhenBothServiceDateAndNavValidationInvoiceDataAreNull(
        string fieldName
    )
    {
        // Arrange
        DateTime? serviceDate = null;
        NavInvoiceData? navValidationInvoiceData = null;

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(
            fieldName,
            serviceDate,
            fieldName == ELOFieldNames.ServiceStartDate
                ? navValidationInvoiceData?.ServiceStartDate
                : navValidationInvoiceData?.ServiceEndDate
        );

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.Missing, result.Status);
        Assert.Null(result.DocumentAiColumnValue);
        Assert.Null(result.NavColumnValue);
    }

    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldReturnOk_WhenServiceDateIsNotNullAndNavServiceDateIsNull(string fieldName)
    {
        // Arrange
        var serviceDate = new DateTime(2024, 1, 15);
        NavInvoiceData navValidationInvoiceData = new NavInvoiceData
        {
            InvoiceId = "TEST123",
            ServiceStartDate = fieldName == ELOFieldNames.ServiceStartDate ? null : new DateTime(2024, 1, 15),
            ServiceEndDate = fieldName == ELOFieldNames.ServiceEndDate ? null : new DateTime(2024, 1, 15),
        };

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(
            fieldName,
            serviceDate,
            fieldName == ELOFieldNames.ServiceStartDate
                ? navValidationInvoiceData?.ServiceStartDate
                : navValidationInvoiceData?.ServiceEndDate
        );

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.Ok, result.Status);
        Assert.Equal("20240115", result.DocumentAiColumnValue);
        Assert.Null(result.NavColumnValue);
    }

    [Theory]
    [InlineData(ELOFieldNames.ServiceStartDate)]
    [InlineData(ELOFieldNames.ServiceEndDate)]
    public void ValidateServiceDate_ShouldCompareOnlyDate_WhenDatesHaveDifferentTimes(string fieldName)
    {
        // Arrange
        var serviceDate = new DateTime(2024, 1, 15, 10, 30, 0);
        var navServiceDate = new DateTime(2024, 1, 15, 14, 45, 30);
        NavInvoiceData navValidationInvoiceData = new NavInvoiceData
        {
            InvoiceId = "TEST123",
            ServiceStartDate = fieldName == ELOFieldNames.ServiceStartDate ? navServiceDate : null,
            ServiceEndDate = fieldName == ELOFieldNames.ServiceEndDate ? navServiceDate : null,
        };

        // Act
        var result = InvoiceValidator.ValidateInvoiceDateToNavDate(
            fieldName,
            serviceDate,
            fieldName == ELOFieldNames.ServiceStartDate
                ? navValidationInvoiceData?.ServiceStartDate
                : navValidationInvoiceData?.ServiceEndDate
        );

        // Assert
        Assert.Equal(fieldName, result.EloColumnName);
        Assert.Equal(ColumnStatus.Ok, result.Status);
        Assert.Equal("20240115", result.DocumentAiColumnValue);
        Assert.Equal("20240115", result.NavColumnValue);
    }
}

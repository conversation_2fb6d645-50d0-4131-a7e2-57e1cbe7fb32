using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Test.Services;

public class DocumentProcessingStatusServiceTests
{
    private readonly DocumentProcessingStatusService _service;

    public DocumentProcessingStatusServiceTests()
    {
        _service = new DocumentProcessingStatusService();
    }

    [Fact]
    public void CalculatePerItemProcessingStatus_ReturnsSuccess_WhenBothEloAndValidationSucceed()
    {
        // Arrange
        var eloResult = new DocumentEloResult { Status = DocumentEloStatusEnum.Success };
        var validationResult = new DocumentValidationResult { Status = DocumentValidationStatusEnum.Success };
        var analysisResult = new DocumentAnalysisResult { Status = DocumentAnalysisStatusEnum.Success };
        var classificationResult = new DocumentClassificationResult
        {
            Status = DocumentClassificationStatusEnum.Success,
        };

        // Act
        var (status, failureReason) = _service.CalculatePerItemProcessingStatus(
            eloResult,
            validationResult,
            analysisResult,
            classificationResult
        );

        // Assert
        Assert.Equal(EloProcessingStatusEnum.Success, status);
        Assert.Equal(string.Empty, failureReason);
    }

    [Fact]
    public void CalculatePerItemProcessingStatus_ReturnsFailed_WhenEloFails()
    {
        // Arrange
        var eloResult = new DocumentEloResult
        {
            Status = DocumentEloStatusEnum.Failed,
            CreateResponseXml = @"<response><message>Test error message</message></response>",
        };
        var validationResult = new DocumentValidationResult { Status = DocumentValidationStatusEnum.Success };
        var analysisResult = new DocumentAnalysisResult { Status = DocumentAnalysisStatusEnum.Success };
        var classificationResult = new DocumentClassificationResult
        {
            Status = DocumentClassificationStatusEnum.Success,
        };

        // Act
        var (status, failureReason) = _service.CalculatePerItemProcessingStatus(
            eloResult,
            validationResult,
            analysisResult,
            classificationResult
        );

        // Assert
        Assert.Equal(EloProcessingStatusEnum.Failed, status);
        Assert.Equal("Test error message", failureReason);
    }

    [Theory]
    [InlineData(
        DocumentClassificationStatusEnum.Failed,
        "Hiba történt a feldolgozás közben. A hiba naplózásra került."
    )]
    [InlineData(
        DocumentClassificationStatusEnum.Timeout,
        "A feldolgozás Azure AI szolgáltatás időtúllépés miatt meghiúsult. Kérjük, próbálja újra."
    )]
    public void CalculatePerItemProcessingStatus_ReturnsFailed_WhenClassificationFails(
        DocumentClassificationStatusEnum classificationStatus,
        string expectedFailureReason
    )
    {
        // Arrange
        var eloResult = new DocumentEloResult { Status = DocumentEloStatusEnum.Pending };
        var validationResult = new DocumentValidationResult { Status = DocumentValidationStatusEnum.Pending };
        var analysisResult = new DocumentAnalysisResult { Status = DocumentAnalysisStatusEnum.Pending };
        var classificationResult = new DocumentClassificationResult { Status = classificationStatus };

        // Act
        var (status, failureReason) = _service.CalculatePerItemProcessingStatus(
            eloResult,
            validationResult,
            analysisResult,
            classificationResult
        );

        // Assert
        Assert.Equal(EloProcessingStatusEnum.Failed, status);
        Assert.Equal(expectedFailureReason, failureReason);
    }

    [Fact]
    public void CalculatePerItemProcessingStatus_ReturnsDefault_WhenNoResultsProvided()
    {
        // Arrange & Act
        var (status, failureReason) = _service.CalculatePerItemProcessingStatus(null, null, null, null);

        // Assert
        Assert.Equal(EloProcessingStatusEnum.Default, status);
        Assert.Equal(string.Empty, failureReason);
    }

    [Fact]
    public void CalculatePerItemProcessingStatus_ReturnsDefault_WhenResultsInProgress()
    {
        // Arrange
        var eloResult = new DocumentEloResult { Status = DocumentEloStatusEnum.Processing };
        var validationResult = new DocumentValidationResult { Status = DocumentValidationStatusEnum.Processing };
        var analysisResult = new DocumentAnalysisResult { Status = DocumentAnalysisStatusEnum.Processing };
        var classificationResult = new DocumentClassificationResult
        {
            Status = DocumentClassificationStatusEnum.Processing,
        };

        // Act
        var (status, failureReason) = _service.CalculatePerItemProcessingStatus(
            eloResult,
            validationResult,
            analysisResult,
            classificationResult
        );

        // Assert
        Assert.Equal(EloProcessingStatusEnum.Default, status);
        Assert.Equal(string.Empty, failureReason);
    }

    [Fact]
    public void CalculatePerItemProcessingStatus_ReturnsDefault_WhenValidationSuccessButEloPending()
    {
        // Arrange
        var eloResult = new DocumentEloResult { Status = DocumentEloStatusEnum.Pending };
        var validationResult = new DocumentValidationResult { Status = DocumentValidationStatusEnum.Success };
        var analysisResult = new DocumentAnalysisResult { Status = DocumentAnalysisStatusEnum.Success };
        var classificationResult = new DocumentClassificationResult
        {
            Status = DocumentClassificationStatusEnum.Success,
        };

        // Act
        var (status, failureReason) = _service.CalculatePerItemProcessingStatus(
            eloResult,
            validationResult,
            analysisResult,
            classificationResult
        );

        // Assert
        Assert.Equal(EloProcessingStatusEnum.Default, status);
        Assert.Equal(string.Empty, failureReason);
    }

    [Fact]
    public void CalculateOverallStatusForSecretary_ReturnsSuccess_WhenOnlySuccessfulClassificationExists_ForPerCase()
    {
        // Arrange
        var analysisResults = new List<DocumentAnalysisResult>();
        var validationResults = new List<DocumentValidationResult>();
        var eloResults = new List<DocumentEloResult>();
        var classificationResults = new List<DocumentClassificationResult>
        {
            new() { Status = DocumentClassificationStatusEnum.Success },
        };
        var uploadType = SecretaryUploadTypeEnum.PerCase;

        // Act
        var result = _service.CalculateOverallStatusForSecretary(
            analysisResults,
            validationResults,
            eloResults,
            classificationResults,
            uploadType
        );

        // Assert
        Assert.Equal(ProcessingStatusEnum.Processing, result);
    }

    [Fact]
    public void CalculateOverallStatusForSecretary_ReturnsProcessing_WhenNoResultsExist_ForPerCase()
    {
        // Arrange
        var analysisResults = new List<DocumentAnalysisResult>();
        var validationResults = new List<DocumentValidationResult>();
        var eloResults = new List<DocumentEloResult>();
        var classificationResults = new List<DocumentClassificationResult>();
        var uploadType = SecretaryUploadTypeEnum.PerCase;

        // Act
        var result = _service.CalculateOverallStatusForSecretary(
            analysisResults,
            validationResults,
            eloResults,
            classificationResults,
            uploadType
        );

        // Assert
        Assert.Equal(ProcessingStatusEnum.Processing, result);
    }

    private static SecretaryDocumentUpload CreateSecretaryUpload(
        SecretaryUploadTypeEnum uploadType = SecretaryUploadTypeEnum.Manual,
        List<DocumentAnalysisResult>? analysisResults = null,
        List<DocumentValidationResult>? validationResults = null,
        List<DocumentEloResult>? eloResults = null,
        List<DocumentClassificationResult>? classificationResults = null
    )
    {
        return new SecretaryDocumentUpload
        {
            Id = Guid.NewGuid(),
            UploadType = uploadType,
            DocumentAnalysisResults = analysisResults ?? [],
            DocumentValidationResults = validationResults ?? [],
            DocumentEloResults = eloResults ?? [],
            DocumentClassificationResults = classificationResults ?? [],
        };
    }

    private static DocumentAnalysisResult CreateAnalysisResult(DocumentAnalysisStatusEnum status)
    {
        return new DocumentAnalysisResult { Status = status };
    }

    private static DocumentValidationResult CreateValidationResult(DocumentValidationStatusEnum status)
    {
        return new DocumentValidationResult { Status = status };
    }

    private static DocumentEloResult CreateEloResult(DocumentEloStatusEnum status)
    {
        return new DocumentEloResult { Status = status };
    }

    private static DocumentClassificationResult CreateClassificationResult(DocumentClassificationStatusEnum status)
    {
        return new DocumentClassificationResult { Status = status };
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_FailedStatus_IncludesDocumentsWithFailedAnalysisResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Failed)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Failed);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(DocumentAnalysisStatusEnum.Failed, filteredResults[0].DocumentAnalysisResults.First().Status);
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_FailedStatus_IncludesDocumentsWithFailedValidationResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                validationResults: [CreateValidationResult(DocumentValidationStatusEnum.BcFailed)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                validationResults: [CreateValidationResult(DocumentValidationStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Failed);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(
            DocumentValidationStatusEnum.BcFailed,
            filteredResults[0].DocumentValidationResults.First().Status
        );
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_FailedStatus_IncludesDocumentsWithFailedEloResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                eloResults: [CreateEloResult(DocumentEloStatusEnum.Failed)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                eloResults: [CreateEloResult(DocumentEloStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Failed);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(DocumentEloStatusEnum.Failed, filteredResults[0].DocumentEloResults.First().Status);
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_ProcessingStatus_IncludesDocumentsWithProcessingAnalysisResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Processing)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Processing);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(DocumentAnalysisStatusEnum.Processing, filteredResults[0].DocumentAnalysisResults.First().Status);
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_ProcessingStatus_IncludesDocumentsWithNoResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(uploadType: SecretaryUploadTypeEnum.Manual),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Processing);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Empty(filteredResults[0].DocumentAnalysisResults);
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_SuccessStatus_IncludesDocumentsWithSuccessfulAnalysisResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Success)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Processing)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Success);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(DocumentAnalysisStatusEnum.Success, filteredResults[0].DocumentAnalysisResults.First().Status);
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_SuccessStatus_ExcludesDocumentsWithFailedResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Failed)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.Manual,
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Success);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(DocumentAnalysisStatusEnum.Success, filteredResults[0].DocumentAnalysisResults.First().Status);
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_SuccessStatus_ExcludesDocumentsWithFailedClassificationResults()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.PerCase,
                classificationResults: [CreateClassificationResult(DocumentClassificationStatusEnum.Failed)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.PerCase,
                classificationResults: [CreateClassificationResult(DocumentClassificationStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Failed);

        // Assert
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(
            DocumentClassificationStatusEnum.Failed,
            filteredResults[0].DocumentClassificationResults.First().Status
        );
    }

    [Fact]
    public void ApplyProcessingStatusFilterForSecretary_SuccessStatus_ExcludesDocumentsWithFailedClassificationResults2()
    {
        // Arrange
        var uploads = new List<SecretaryDocumentUpload>
        {
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.PerCase,
                classificationResults: [CreateClassificationResult(DocumentClassificationStatusEnum.Failed)]
            ),
            CreateSecretaryUpload(
                uploadType: SecretaryUploadTypeEnum.PerCase,
                classificationResults: [CreateClassificationResult(DocumentClassificationStatusEnum.Success)],
                analysisResults: [CreateAnalysisResult(DocumentAnalysisStatusEnum.Success)],
                validationResults: [CreateValidationResult(DocumentValidationStatusEnum.Success)],
                eloResults: [CreateEloResult(DocumentEloStatusEnum.Success)]
            ),
        }.AsQueryable();

        // Act
        var result = _service.ApplyProcessingStatusFilterForSecretary(uploads, ProcessingStatusEnum.Processing);

        // Assert
        var filteredResults = result.ToList();
        Assert.Empty(filteredResults);
    }
}

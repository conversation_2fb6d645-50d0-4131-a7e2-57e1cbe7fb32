using System.IO.Compression;
using System.Text.Json;
using Azure.AI.DocumentIntelligence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.DocumentProcessing.Jobs;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Attachment;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using Quartz;

namespace PartnerPortal.Backend.Document.Services;

public class DocumentStatusReportService
{
    private readonly UserDbContext _dbContext;
    private readonly string _responseFilePath;
    private readonly string _mergedDocumentsPath;
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly ILogger<DocumentStatusReportService> _logger;
    private readonly IAttachmentService _attachmentService;

    public DocumentStatusReportService(
        UserDbContext dbContext,
        IConfiguration config,
        ISchedulerFactory schedulerFactory,
        ILogger<DocumentStatusReportService> logger,
        IAttachmentService attachmentService
    )
    {
        _dbContext = dbContext;
        _responseFilePath = config.GetConfigValue<string>("AzureAI:ResponsesPath");
        _mergedDocumentsPath = config.GetConfigValue<string>("EloConfiguration:MergedDocumentsPath");
        _schedulerFactory = schedulerFactory;
        _logger = logger;
        _attachmentService = attachmentService;
        string documentAttachmentPath = config.GetConfigValue<string>("DocumentModule:FilesPath");
        long fileSizeLimit = config.GetConfigValue<long>("DocumentModule:FileSizeLimit");
        int fileCountLimit = config.GetConfigValue<int>("DocumentModule:FileCountLimit");
        string[] permittedExtensions = config.GetConfigValue<string[]>("DocumentModule:PermittedExtensions");

        (_attachmentService as AttachmentService)?.SetConfiguration(
            documentAttachmentPath,
            fileSizeLimit,
            fileCountLimit,
            permittedExtensions
        );
    }

    public async Task<GetDocumentStatusReports200Response> GetAllDocumentStatusesAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int? page = null,
        int? pageSize = null,
        string? spIdentifier = null
    )
    {
        int actualPage = page ?? 1;
        int actualPageSize = pageSize ?? 20;
        int skip = (actualPage - 1) * actualPageSize;

        var query = _dbContext.DocumentUploads.AsQueryable();

        if (!string.IsNullOrEmpty(spIdentifier))
        {
            query = query.Where(d =>
                (
                    d is SecretaryDocumentUpload
                    && ((SecretaryDocumentUpload)d).Classifications.Any(c => c.SpIdentifier == spIdentifier)
                ) || (d is PartnerDocumentUpload && d.AttachmentDocumentTypes.Any(a => a.SpIdentifier == spIdentifier))
            );
        }

        if (fromDate.HasValue)
        {
            query = query.Where(d => d.CreatedDate >= fromDate.Value.Date);
        }

        if (toDate.HasValue)
        {
            // Include the entire day by setting time to end of day
            var endOfDay = toDate.Value.Date.AddDays(1).AddTicks(-1);
            query = query.Where(d => d.CreatedDate <= endOfDay);
        }

        var totalRecords = await query.CountAsync();
        var documents = await query.OrderByDescending(d => d.CreatedDate).Skip(skip).Take(actualPageSize).ToListAsync();

        var result = new List<SimpleDocumentStatusReportDto>();

        foreach (var doc in documents)
        {
            var dto = CreateSimpleDocumentStatusReportDto(doc);
            result.Add(dto);
        }

        return new GetDocumentStatusReports200Response { Data = result, TotalRecords = totalRecords };
    }

    private static SimpleDocumentStatusReportDto CreateSimpleDocumentStatusReportDto(DocumentUpload document)
    {
        return new SimpleDocumentStatusReportDto
        {
            Id = document.Id,
            CreatedDate = document.CreatedDate,
            DocumentType =
                document is SecretaryDocumentUpload
                    ? SimpleDocumentStatusReportDto.DocumentTypeEnum.Secretary
                    : SimpleDocumentStatusReportDto.DocumentTypeEnum.Partner,
        };
    }

    public async Task<DocumentStatusReportDto> GetDocumentStatusAsync(Guid documentId)
    {
        var document = await _dbContext
            .DocumentUploads.Include(d => d.AttachmentDocumentTypes)
            .FirstOrDefaultAsync(d => d.Id == documentId);

        return await CreateDocumentStatusReportDto(
            document ?? throw new ArgumentException($"Document with ID {documentId} not found"),
            true
        );
    }

    private async Task<DocumentStatusReportDto> CreateDocumentStatusReportDto(
        DocumentUpload document,
        bool includeDetails = false
    )
    {
        // Get validation results
        var validationResults = await _dbContext
            .DocumentValidationResults.Where(v => v.DocumentUploadId == document.Id)
            .Include(v => v.ProcessingSource)
            .ThenInclude(v => v.Attachment)
            .AsSplitQuery()
            .ToListAsync();

        var eloResults = await _dbContext
            .DocumentEloResults.Where(e => e.DocumentUploadId == document.Id)
            .Include(e => e.ProcessingSource)
            .ThenInclude(e => e.Attachment)
            .AsSplitQuery()
            .ToListAsync();

        List<DocumentClassificationResult>? classificationResults = [];
        List<DocumentAnalysisResult>? analysisResults = [];

        string? contactNo = null;
        bool isSecretaryDocument = false;
        DateTime? secretaryDateOfArrival = null;
        string? secretaryUploadType = null;
        if (document is SecretaryDocumentUpload secretaryDocument && includeDetails)
        {
            classificationResults = await _dbContext
                .DocumentClassificationResults.Where(c => c.DocumentUploadId == document.Id)
                .Include(c => c.Attachment)
                .AsSplitQuery()
                .ToListAsync();

            contactNo = secretaryDocument.ContactNo;
            isSecretaryDocument = true;
            secretaryDateOfArrival = secretaryDocument.SecretaryDateOfArrival;
            secretaryUploadType = secretaryDocument.UploadType.ToString();
        }

        analysisResults = await _dbContext
            .DocumentAnalysisResults.Where(a => a.DocumentUploadId == document.Id)
            .Include(a => a.ProcessingSource)
            .ThenInclude(a => a.Attachment)
            .AsSplitQuery()
            .ToListAsync();

        var dto = new DocumentStatusReportDto
        {
            Id = document.Id,
            IsSecretaryDocument = isSecretaryDocument,
            SecretaryUploadType = secretaryUploadType,
            ContactNo = contactNo,
            ProjectNo = document.ProjectNo,
            ProjectDescription = document.ProjectDescription,
            ContractNo = document.ContractNo,
            CreatedDate = document.CreatedDate,
            DateOfArrival = document.DateOfArrival,
            SecretaryDateOfArrival = secretaryDateOfArrival,
            UploadStatus = document.Status,
            TotalAttachments = document.AttachmentDocumentTypes.Count,

            // Analysis counts
            AnalyzedDocuments = analysisResults.Count(a => a.Status == DocumentAnalysisStatusEnum.Success),
            PendingAnalysisDocuments = analysisResults.Count(a =>
                a.Status == DocumentAnalysisStatusEnum.Pending || a.Status == DocumentAnalysisStatusEnum.Processing
            ),
            FailedAnalysisDocuments = analysisResults.Count(a => a.Status == DocumentAnalysisStatusEnum.Failed),

            // Validation counts
            ValidatedDocuments = validationResults.Count(v => v.Status == DocumentValidationStatusEnum.Success),
            PendingValidationDocuments = validationResults.Count(v =>
                v.Status == DocumentValidationStatusEnum.Pending || v.Status == DocumentValidationStatusEnum.Processing
            ),
            FailedValidationDocuments = validationResults.Count(v =>
                v.Status != DocumentValidationStatusEnum.Success
                && v.Status != DocumentValidationStatusEnum.Pending
                && v.Status != DocumentValidationStatusEnum.Processing
            ),

            // ELO counts
            SuccessEloDocuments = eloResults.Count(e => e.Status == DocumentEloStatusEnum.Success),
            PendingEloDocuments = eloResults.Count(e =>
                e.Status == DocumentEloStatusEnum.Pending || e.Status == DocumentEloStatusEnum.Processing
            ),
            FailedEloDocuments = eloResults.Count(e => e.Status == DocumentEloStatusEnum.Failed),
        };

        foreach (var classification in classificationResults)
        {
            dto.ClassificationDetails ??= [];

            var filePath = FileUtility.GetFilePath(
                _responseFilePath,
                classification.Attachment.Context,
                classification.Attachment.StoredName
            );

            string? classificationResultJson = null;

            try
            {
                if (includeDetails)
                {
                    AnalyzeResult analysisResult = await FileUtility.GetAnalyzeResult(filePath);
                    classificationResultJson = JsonSerializer.Serialize(analysisResult);
                }
            }
            catch (Exception)
            {
                classificationResultJson = null;
            }

            dto.ClassificationDetails.Add(
                new DocumentClassificationDetailDto
                {
                    Id = classification.Id,
                    DocumentId = document.Id,
                    ProcessingSourceId = classification.Attachment.Id,
                    AttachmentName = classification.Attachment.StoredName,
                    AttachmentId = classification.Attachment.Id,
                    Status = classification.Status,
                    ErrorMessage = classification.ErrorMessage,
                    RetryCount = classification.RetryCount,
                    ClassificationResultJson = classificationResultJson,
                }
            );
        }

        // Add analysis details
        foreach (var analysis in analysisResults)
        {
            dto.AnalysisDetails ??= [];

            string? filePath = null;
            string? attachmentName = null;

            switch (analysis.ProcessingSource)
            {
                case DocumentClassification classification when isSecretaryDocument:
                    filePath = FileUtility.GetFilePath(
                        _responseFilePath,
                        classification.Attachment.Context,
                        FileUtility.GetClassificationStoredName(
                            classification.Attachment.StoredName,
                            classification.StartPage,
                            classification.EndPage
                        )
                    );
                    attachmentName =
                        $"{classification.Attachment.StoredName}- from {classification.StartPage} to {classification.EndPage}";
                    break;

                case AttachmentDocumentType attachmentDocumentType:
                    filePath = FileUtility.GetFilePath(
                        _responseFilePath,
                        attachmentDocumentType.Attachment.Context,
                        attachmentDocumentType.Attachment.StoredName
                    );
                    attachmentName = attachmentDocumentType.Attachment.StoredName;
                    break;

                default:
                    throw new InvalidOperationException(
                        "Processing source is not a document classification or attachment document type"
                    );
            }

            string? analysisResultJson = null;

            try
            {
                if (includeDetails)
                {
                    AnalyzeResult analysisResult = await FileUtility.GetAnalyzeResult(filePath);
                    analysisResultJson = JsonSerializer.Serialize(analysisResult);
                }
            }
            catch (Exception)
            {
                analysisResultJson = null;
            }

            dto.AnalysisDetails.Add(
                new DocumentAnalysisDetailDto
                {
                    Id = analysis.Id,
                    ProcessingSourceId = analysis.ProcessingSourceId,
                    DocumentType = analysis.ProcessingSource.DocumentType,
                    AttachmentName = AddSpToAttachmentName(attachmentName, analysis.ProcessingSource.SpIdentifier),
                    AttachmentId = analysis.ProcessingSource.Attachment.Id,
                    Status = analysis.Status,
                    ErrorMessage = analysis.ErrorMessage,
                    RetryCount = analysis.RetryCount,
                    AzureDocumentAiResultJson = analysisResultJson,
                }
            );
        }

        // Add validation details
        foreach (var validation in validationResults)
        {
            dto.ValidationDetails ??= [];

            var validationResultJson = new List<ValidationJsonDto>();

            try
            {
                validationResultJson =
                    JsonSerializer.Deserialize<List<ValidationJsonDto>>(validation.ValidationResultJson ?? "[]") ?? [];
            }
            catch (Exception)
            {
                validationResultJson = [];
            }

            dto.ValidationDetails.Add(
                new DocumentValidationDetailDto
                {
                    Id = validation.Id,
                    ProcessingSourceId = validation.ProcessingSourceId,
                    DocumentType = validation.ProcessingSource.DocumentType,
                    AttachmentName = AddSpToAttachmentName(
                        validation.ProcessingSource.Attachment.StoredName,
                        validation.ProcessingSource.SpIdentifier
                    ),
                    AttachmentId = validation.ProcessingSource.Attachment.Id,
                    Status = validation.Status,
                    ErrorMessage = validation.ErrorMessage,
                    RetryCount = validation.RetryCount,
                    ValidationResultJson = validationResultJson,
                }
            );
        }

        // Add ELO details
        foreach (var elo in eloResults)
        {
            dto.EloDetails ??= [];

            dto.EloDetails.Add(
                new DocumentEloDetailDto
                {
                    Id = elo.Id,
                    DocumentId = document.Id,
                    ProcessingSourceId = elo.ProcessingSourceId,
                    AttachmentName = AddSpToAttachmentName(
                        elo.ProcessingSource.Attachment.StoredName,
                        elo.ProcessingSource.SpIdentifier
                    ),
                    AttachmentId = elo.ProcessingSource.Attachment.Id,
                    Status = elo.Status,
                    ErrorMessage = elo.ErrorMessage,
                    RetryCount = elo.RetryCount,
                    CreateRequestXml = elo.CreateRequestXml,
                    CreateResponseXml = elo.CreateResponseXml,
                    UploadResponseXml = elo.UploadResponseXml,
                    UploadRequestXml = elo.UploadRequestXml,
                    WorkflowRequestXml = elo.StartWorkflowRequestXml,
                    WorkflowResponseXml = elo.StartWorkflowResponseXml,
                }
            );
        }

        return dto;
    }

    private static string? AddSpToAttachmentName(string? attachmentName, string? spIdentifier)
    {
        if (attachmentName == null || spIdentifier == null)
        {
            return attachmentName;
        }

        return attachmentName + " (" + spIdentifier + ")";
    }

    public async Task<(FileStream FileStream, string FileName)?> DownloadMergedDocument(
        Guid documentId,
        int attachmentDocumentTypeId
    )
    {
        var attachment = await _dbContext
            .DocumentEloResults.Where(a =>
                a.DocumentUploadId == documentId && a.ProcessingSourceId == attachmentDocumentTypeId
            )
            .Include(a => a.DocumentUpload)
            .Include(a => a.ProcessingSource)
            .ThenInclude(a => a.Attachment)
            .FirstOrDefaultAsync();

        if (attachment == null)
        {
            return null;
        }

        var name = Path.GetFileNameWithoutExtension(attachment.ProcessingSource.Attachment.StoredName);
        var spIdentifier = attachment.ProcessingSource.SpIdentifier!.Replace("/", "");
        var mergedFilePath = FileUtility.GetFilePath(
            _mergedDocumentsPath,
            documentId.ToString(),
            $"{spIdentifier}.pdf"
        );

        FileStream fileStream = new FileStream(mergedFilePath, FileMode.Open, FileAccess.Read);
        return (fileStream, $"{spIdentifier}.pdf");
    }

    public async Task RetryAnalysis(int id)
    {
        var analysisResult =
            await _dbContext.DocumentAnalysisResults.FindAsync(id)
            ?? throw new NotFoundException($"Analysis result with ID {id} not found");
        var validationResults = await _dbContext
            .DocumentValidationResults.Where(v => v.DocumentUploadId == analysisResult.DocumentUploadId)
            .ToListAsync();

        if (validationResults.Count == 0)
        {
            throw new NotFoundException($"Validation result with ID {id} not found");
        }

        foreach (var validation in validationResults)
        {
            validation.Status = DocumentValidationStatusEnum.Pending;
            validation.RetryCount++;
        }

        var eloResults = await _dbContext
            .DocumentEloResults.Where(e => e.DocumentUploadId == analysisResult.DocumentUploadId)
            .ToListAsync();

        if (eloResults.Count == 0)
        {
            throw new NotFoundException($"ELO result with ID {id} not found");
        }

        foreach (var elo in eloResults)
        {
            elo.Status = DocumentEloStatusEnum.Pending;
            elo.RetryCount++;
        }

        analysisResult.Status = DocumentAnalysisStatusEnum.Processing;
        analysisResult.RetryCount++;
        await _dbContext.SaveChangesAsync();

        var jobData = JsonSerializer.Serialize(
            new DocumentJobInputData
            {
                ItemId = analysisResult.ProcessingSourceId,
                DocumentUploadId = analysisResult.DocumentUploadId,
                RetryCount = 0,
            }
        );

        var trigger = TriggerBuilder
            .Create()
            .ForJob(DocumentAnalyzerJob.Key)
            .UsingJobData("jobData", jobData)
            .StartNow()
            .Build();

        var scheduler = await _schedulerFactory.GetScheduler();
        await scheduler.ScheduleJob(trigger);
    }

    public async Task RetryElo(int id)
    {
        var eloResult =
            await _dbContext.DocumentEloResults.FindAsync(id)
            ?? throw new NotFoundException($"ELO result with ID {id} not found");
        eloResult.Status = DocumentEloStatusEnum.Processing;
        eloResult.RetryCount++;
        await _dbContext.SaveChangesAsync();

        var jobData = JsonSerializer.Serialize(
            new DocumentJobInputData
            {
                ItemId = eloResult.ProcessingSourceId,
                DocumentUploadId = eloResult.DocumentUploadId,
                RetryCount = 0,
            }
        );

        var trigger = TriggerBuilder.Create().ForJob(EloJob.Key).UsingJobData("jobData", jobData).StartNow().Build();
        var scheduler = await _schedulerFactory.GetScheduler();
        await scheduler.ScheduleJob(trigger);
    }

    public async Task RetryValidation(int id)
    {
        var validationResult =
            await _dbContext.DocumentValidationResults.FindAsync(id)
            ?? throw new NotFoundException($"Validation result with ID {id} not found");
        var eloResults = await _dbContext
            .DocumentEloResults.Where(e => e.DocumentUploadId == validationResult.DocumentUploadId)
            .ToListAsync();

        if (eloResults.Count == 0)
        {
            throw new NotFoundException($"ELO result with ID {id} not found");
        }

        foreach (var elo in eloResults)
        {
            elo.Status = DocumentEloStatusEnum.Pending;
            elo.RetryCount++;
        }

        validationResult.Status = DocumentValidationStatusEnum.Processing;
        validationResult.RetryCount++;
        await _dbContext.SaveChangesAsync();

        var jobData = JsonSerializer.Serialize(
            new DocumentJobInputData
            {
                ItemId = validationResult.ProcessingSourceId,
                DocumentUploadId = validationResult.DocumentUploadId,
                RetryCount = 0,
            }
        );

        var trigger = TriggerBuilder
            .Create()
            .ForJob(DocumentValidationJob.Key)
            .UsingJobData("jobData", jobData)
            .StartNow()
            .Build();
        var scheduler = await _schedulerFactory.GetScheduler();
        await scheduler.ScheduleJob(trigger);
    }

    public async Task RetryAllValidation()
    {
        var validationResults = await _dbContext.DocumentValidationResults.ToListAsync();
        foreach (var validation in validationResults)
        {
            await RetryValidation(validation.Id);
        }
    }

    public async Task<(MemoryStream MemoryStream, string FileName)?> DownloadBasedOnSp(
        DownloadBasedOnSpRequest downloadBasedOnSpRequest
    )
    {
        var documentUploads = await _dbContext
            .DocumentUploads.Include(d => d.AttachmentDocumentTypes)
            .Where(d =>
                (
                    d is SecretaryDocumentUpload
                    && ((SecretaryDocumentUpload)d).Classifications.Any(c =>
                        downloadBasedOnSpRequest.SpIdentifiers.Contains(c.SpIdentifier)
                    )
                )
                || (
                    d is PartnerDocumentUpload
                    && d.AttachmentDocumentTypes.Any(a =>
                        downloadBasedOnSpRequest.SpIdentifiers.Contains(a.SpIdentifier)
                    )
                )
            )
            .ToListAsync();

        var ms = new MemoryStream();

        using (var zip = new ZipArchive(ms, ZipArchiveMode.Create, true))
        {
            foreach (var documentUpload in documentUploads.DistinctBy(d => d.Id))
            {
                if (documentUpload is SecretaryDocumentUpload secretaryDocumentUpload)
                {
                    var invoiceClassifications = await _dbContext
                        .DocumentClassifications.Where(c =>
                            c.DocumentType == DocumentType.Invoice
                            && !string.IsNullOrEmpty(c.SpIdentifier)
                            && downloadBasedOnSpRequest.SpIdentifiers.Contains(c.SpIdentifier)
                            && c.SecretaryDocumentUploadId == documentUpload.Id
                        )
                        .Include(c => c.Attachment)
                        .ToListAsync();

                    var completionCertClassifications = await _dbContext
                        .DocumentClassifications.Where(c =>
                            c.DocumentType == DocumentType.CompletionCert
                            && c.SecretaryDocumentUploadId == documentUpload.Id
                        )
                        .Include(c => c.Attachment)
                        .ToListAsync();

                    foreach (var invoiceClassification in invoiceClassifications)
                    {
                        var folderName = invoiceClassification.SpIdentifier!.Replace("/", "");

                        var entryPath = $"{folderName}/{invoiceClassification.Attachment.UploadName}";
                        var entry = zip.CreateEntry(entryPath);
                        var storedFilePath = _attachmentService.StoredFilePath(
                            documentUpload.Id.ToString(),
                            invoiceClassification.Attachment.StoredName
                        );
                        using var fileStream = new FileStream(storedFilePath, FileMode.Open, FileAccess.Read);
                        using var entryStream = entry.Open();
                        fileStream.CopyTo(entryStream);
                    }

                    if (invoiceClassifications.Count != 0 && completionCertClassifications.Count != 0)
                    {
                        foreach (var invoiceClassification in invoiceClassifications)
                        {
                            var folderName = invoiceClassification.SpIdentifier!.Replace("/", "");

                            foreach (var completionCert in completionCertClassifications)
                            {
                                var entryPath = $"{folderName}/{completionCert.Attachment.UploadName}";
                                var entry = zip.CreateEntry(entryPath);
                                var storedFilePath = _attachmentService.StoredFilePath(
                                    documentUpload.Id.ToString(),
                                    completionCert.Attachment.StoredName
                                );
                                using var fileStream = new FileStream(storedFilePath, FileMode.Open, FileAccess.Read);
                                using var entryStream = entry.Open();
                                fileStream.CopyTo(entryStream);
                            }
                        }
                    }
                }
                else if (documentUpload is PartnerDocumentUpload partnerDocumentUpload)
                {
                    var invoiceAttachments = partnerDocumentUpload.AttachmentDocumentTypes.Where(a =>
                        a.IsInvoiceType
                        && !string.IsNullOrEmpty(a.SpIdentifier)
                        && downloadBasedOnSpRequest.SpIdentifiers.Contains(a.SpIdentifier)
                    );

                    var completionCertAttachments = partnerDocumentUpload.AttachmentDocumentTypes.Where(a =>
                        a.DocumentType == DocumentType.CompletionCert
                    );

                    foreach (var invoiceAttachment in invoiceAttachments)
                    {
                        var folderName = invoiceAttachment.SpIdentifier!.Replace("/", "");
                        var entryPath = $"{folderName}/{invoiceAttachment.Attachment.UploadName}";

                        var entry = zip.CreateEntry(entryPath);
                        var storedFilePath = _attachmentService.StoredFilePath(
                            documentUpload.Id.ToString(),
                            invoiceAttachment.Attachment.StoredName
                        );
                        using var fileStream = new FileStream(storedFilePath, FileMode.Open, FileAccess.Read);
                        using var entryStream = entry.Open();
                        fileStream.CopyTo(entryStream);
                    }

                    if (invoiceAttachments.Any() && completionCertAttachments.Any())
                    {
                        foreach (var invoiceAttachment in invoiceAttachments)
                        {
                            var folderName = invoiceAttachment.SpIdentifier!.Replace("/", "");

                            foreach (var completionCert in completionCertAttachments)
                            {
                                var entryPath = $"{folderName}/{completionCert.Attachment.UploadName}";
                                var entry = zip.CreateEntry(entryPath);
                                var storedFilePath = _attachmentService.StoredFilePath(
                                    documentUpload.Id.ToString(),
                                    completionCert.Attachment.StoredName
                                );
                                using var fileStream = new FileStream(storedFilePath, FileMode.Open, FileAccess.Read);
                                using var entryStream = entry.Open();
                                fileStream.CopyTo(entryStream);
                            }
                        }
                    }
                }
            }
        }
        ms.Position = 0;
        return (ms, "sp-documents.zip");
    }
}

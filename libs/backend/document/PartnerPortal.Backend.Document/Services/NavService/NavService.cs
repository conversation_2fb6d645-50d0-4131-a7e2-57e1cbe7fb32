using System.Threading.RateLimiting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nav.Invoice.Client;
using Nav.Invoice.Client.Configuration;
using Nav.Invoice.Client.Exceptions;
using Nav.Invoice.Client.Models;
using PartnerPortal.Backend.Document.Models;
using Polly;

namespace PartnerPortal.Backend.Document.Services.NavService;

public class NavService : INavService
{
    private readonly ILogger<NavService> _logger;
    private readonly IOptions<NavConfig> _navModeConfig;

    /// <summary>
    /// Maximum requests per second for NAV API - strict limit of 1 request per second
    /// </summary>
    private const int MaxRequestsPerSecond = 1;

    /// <summary>
    /// Shared static resilience pipeline for global rate limiting across all NAV service instances
    /// </summary>
    private static readonly ResiliencePipeline SharedResiliencePipeline = new ResiliencePipelineBuilder()
        .AddRateLimiter(
            new TokenBucketRateLimiter(
                new TokenBucketRateLimiterOptions
                {
                    TokensPerPeriod = MaxRequestsPerSecond,
                    ReplenishmentPeriod = TimeSpan.FromSeconds(2), // 2 seconds to stay within the rate limit safely
                    TokenLimit = MaxRequestsPerSecond,
                    QueueLimit = int.MaxValue,
                }
            )
        )
        .AddRetry(
            new Polly.Retry.RetryStrategyOptions
            {
                ShouldHandle = new PredicateBuilder()
                    .Handle<NavHttpException>(ex =>
                        ex.StatusCode.HasValue
                        && (
                            ex.StatusCode.Value == 429
                            || // Too Many Requests
                            ex.StatusCode.Value == 503
                            || // Service Unavailable
                            ex.StatusCode.Value >= 500 // Server errors
                        )
                    )
                    .Handle<NavTimeoutException>()
                    .Handle<HttpRequestException>()
                    .Handle<TaskCanceledException>(ex => !ex.CancellationToken.IsCancellationRequested),
                MaxRetryAttempts = 3,
                Delay = TimeSpan.FromSeconds(2),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
            }
        )
        .AddTimeout(TimeSpan.FromMinutes(5))
        .Build();

    public NavService(ILogger<NavService> logger, IOptions<NavConfig> navModeConfig)
    {
        _logger = logger;
        _navModeConfig = navModeConfig;
    }

    /// <inheritdoc />
    public async Task<InvoiceData?> QueryInvoiceDataAsync(
        InvoiceNumberQueryType invoiceNumberQuery,
        CustomerDetails? customerDetails
    )
    {
        ArgumentNullException.ThrowIfNull(invoiceNumberQuery);

        _logger.LogDebug(
            "Starting NAV query for invoice {InvoiceNumber} with supplier tax number {SupplierTaxNumber}",
            invoiceNumberQuery.InvoiceNumber,
            invoiceNumberQuery.SupplierTaxNumber
        );

        return await SharedResiliencePipeline.ExecuteAsync(async token =>
            await ExecuteNavQuery(invoiceNumberQuery, customerDetails, token)
        );
    }

    /// <summary>
    /// Executes the actual NAV query with proper configuration
    /// </summary>
    private async Task<InvoiceData?> ExecuteNavQuery(
        InvoiceNumberQueryType invoiceNumberQuery,
        CustomerDetails? customerDetails,
        CancellationToken cancellationToken
    )
    {
        var navConfiguration = CreateNavConfiguration(customerDetails);
        if (navConfiguration == null)
        {
            _logger.LogWarning("NAV configuration could not be created - missing customer details");
            return null;
        }

        using var client = new NavClient(navConfiguration);

        try
        {
            _logger.LogInformation(
                "Executing NAV query for invoice {InvoiceNumber} with supplier tax number {SupplierTaxNumber}",
                invoiceNumberQuery.InvoiceNumber,
                invoiceNumberQuery.SupplierTaxNumber
            );

            var result = await client.QueryInvoiceDataAsync(invoiceNumberQuery, cancellationToken);

            _logger.LogInformation(
                "NAV query completed successfully for invoice {InvoiceNumber}",
                invoiceNumberQuery.InvoiceNumber
            );

            return result.invoiceData;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "NAV query failed for invoice {InvoiceNumber} with supplier tax number {SupplierTaxNumber}",
                invoiceNumberQuery.InvoiceNumber,
                invoiceNumberQuery.SupplierTaxNumber
            );
            throw;
        }
    }

    /// <summary>
    /// Creates NAV configuration from customer details and configuration
    /// </summary>
    private NavConfiguration? CreateNavConfiguration(CustomerDetails? customerDetails)
    {
        if (
            customerDetails == null
            || string.IsNullOrEmpty(customerDetails.Login)
            || string.IsNullOrEmpty(customerDetails.Password)
            || string.IsNullOrEmpty(customerDetails.TaxNumber)
            || string.IsNullOrEmpty(customerDetails.SignKey)
            || string.IsNullOrEmpty(customerDetails.ExchangeKey)
        )
        {
            return null;
        }

        var userData = new UserData
        {
            Login = customerDetails.Login,
            Password = customerDetails.Password,
            TaxNumber = customerDetails.TaxNumber,
            SignKey = customerDetails.SignKey,
            ExchangeKey = customerDetails.ExchangeKey,
        };

        var softwareData = new Nav.Invoice.Client.Models.SoftwareType
        {
            SoftwareId = _navModeConfig.Value.SoftwareType.SoftwareId,
            SoftwareName = _navModeConfig.Value.SoftwareType.SoftwareName,
            SoftwareOperation = _navModeConfig.Value.SoftwareType.SoftwareOperation,
            SoftwareMainVersion = _navModeConfig.Value.SoftwareType.SoftwareMainVersion,
            SoftwareDevName = _navModeConfig.Value.SoftwareType.SoftwareDevName,
            SoftwareDevContact = _navModeConfig.Value.SoftwareType.SoftwareDevContact,
        };

        return new NavConfiguration(
            _navModeConfig.Value.NavMode == NavMode.Test ? NavConfiguration.TestUrl : NavConfiguration.ProductionUrl,
            userData,
            softwareData,
            logger: _logger
        ).EnableLogging(
            _navModeConfig.Value.NavMode == NavMode.Test
                ? LoggingOptions.ForDevelopment()
                : LoggingOptions.ForProduction()
        );
    }
}

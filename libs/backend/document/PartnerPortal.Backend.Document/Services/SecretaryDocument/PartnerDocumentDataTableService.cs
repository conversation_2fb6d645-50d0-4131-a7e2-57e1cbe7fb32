using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Services.Base;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.SecretaryDocument;

public interface IPartnerDocumentDataTableService
    : IBaseDataTableService<PartnerDocumentUpload, SecretaryPartnerDocumentListDto, ListPartnerDocuments200Response> { }

public class PartnerDocumentDataTableService
    : BaseDataTableService<PartnerDocumentUpload, SecretaryPartnerDocumentListDto, ListPartnerDocuments200Response>,
        IPartnerDocumentDataTableService
{
    private readonly UserDbContext _context;
    private readonly IDocumentProcessingStatusService _statusService;

    public PartnerDocumentDataTableService(
        UserDbContext context,
        IMapper mapper,
        IDocumentProcessingStatusService statusService
    )
        : base(mapper)
    {
        _context = context;
        _statusService = statusService;
    }

    protected override IQueryable<PartnerDocumentUpload> BuildBaseQuery()
    {
        var query = _context
            .DocumentUploads.Include(i => i.AttachmentDocumentTypes)
            .ThenInclude(x => x.Attachment)
            .Include(i => i.User)
            .Include(i => i.DocumentAnalysisResults)
            .Include(i => i.DocumentValidationResults)
            .Include(i => i.DocumentEloResults)
            .OfType<PartnerDocumentUpload>();

        return query;
    }

    protected override ListPartnerDocuments200Response BuildResponse(
        List<SecretaryPartnerDocumentListDto> results,
        int totalCount,
        TableRequest request
    )
    {
        return new ListPartnerDocuments200Response
        {
            Data = results,
            TotalRecords = totalCount,
            Page = request.Page,
            PageSize = request.PageSize,
        };
    }

    protected override HashSet<string> GetStandardDatabaseFields()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Id",
            "CreatedDate",
            "DateOfArrival",
            "PartnerContactNo",
            "Status",
        };
    }

    protected override HashSet<string> GetDatabaseComputedFields()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase) { "Partner", "spIdentifiers", "ProcessingStatus" };
    }

    protected override Dictionary<string, string> GetFieldMappings()
    {
        return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "PartnerContactNo", "User.BcUserId" },
        };
    }

    protected override Task<IQueryable<PartnerDocumentUpload>> ApplyDatabaseComputedFiltersImplementation(
        IQueryable<PartnerDocumentUpload> query,
        List<ColumnFilter> filters,
        CancellationToken cancellationToken
    )
    {
        foreach (var filter in filters)
        {
            switch (filter.Field.ToLower())
            {
                case "partner":
                    query = ApplyPartnerFilterAtDatabase(query, filter);
                    break;

                case "spidentifiers":
                    query = ApplySpIdentifiersFilterAtDatabase(query, filter);
                    break;

                case "processingstatus":
                    if (
                        filter.Value != null
                        && Enum.TryParse<ProcessingStatusEnum>(filter.Value.ToString(), true, out var targetStatus)
                    )
                    {
                        query = _statusService.ApplyProcessingStatusFilterForPartner(query, targetStatus);
                    }
                    break;
            }
        }

        return Task.FromResult(query);
    }

    protected override Task<IQueryable<PartnerDocumentUpload>> ApplyDatabaseComputedSortsImplementation(
        IQueryable<PartnerDocumentUpload> query,
        List<SortField> sorts,
        CancellationToken cancellationToken
    )
    {
        foreach (var sort in sorts)
        {
            switch (sort.Field.ToLower())
            {
                case "partner":
                    query = ApplyPartnerSortAtDatabase(query, sort);
                    break;

                case "spidentifiers":
                    query = ApplySpIdentifiersSortAtDatabase(query, sort);
                    break;
            }
        }

        return Task.FromResult(query);
    }

    private static IQueryable<PartnerDocumentUpload> ApplyPartnerFilterAtDatabase(
        IQueryable<PartnerDocumentUpload> query,
        ColumnFilter filter
    )
    {
        if (filter.Value == null)
            return query;

        var searchValue = filter.Value.ToString()!;

        return filter.MatchMode switch
        {
            ColumnFilter.MatchModeEnum.Contains => query.Where(d =>
                (d.User.ProfileName != null && d.User.ProfileName.Contains(searchValue))
                || (d.User.VatRegistrationNo != null && d.User.VatRegistrationNo.Contains(searchValue))
            ),

            ColumnFilter.MatchModeEnum.StartsWith => query.Where(d =>
                (d.User.ProfileName != null && d.User.ProfileName.StartsWith(searchValue))
                || (d.User.VatRegistrationNo != null && d.User.VatRegistrationNo.StartsWith(searchValue))
            ),

            ColumnFilter.MatchModeEnum.Equals => query.Where(d =>
                EF.Functions.Like(d.User.ProfileName + " (" + d.User.VatRegistrationNo + ")", searchValue)
            ),

            _ => query,
        };
    }

    private static IQueryable<PartnerDocumentUpload> ApplyPartnerSortAtDatabase(
        IQueryable<PartnerDocumentUpload> query,
        SortField sort
    )
    {
        if (sort.Order == SortField.OrderEnum.Desc)
        {
            return query.OrderByDescending(d => d.User.ProfileName).ThenByDescending(d => d.User.VatRegistrationNo);
        }
        else
        {
            return query.OrderBy(d => d.User.ProfileName).ThenBy(d => d.User.VatRegistrationNo);
        }
    }

    private static IQueryable<PartnerDocumentUpload> ApplySpIdentifiersFilterAtDatabase(
        IQueryable<PartnerDocumentUpload> query,
        ColumnFilter filter
    )
    {
        if (filter.Value == null)
            return query;

        var searchValue = filter.Value.ToString()!;

        return filter.MatchMode switch
        {
            ColumnFilter.MatchModeEnum.Contains => query.Where(d =>
                d.AttachmentDocumentTypes.Any(a => a.SpIdentifier != null && a.SpIdentifier.Contains(searchValue))
            ),

            ColumnFilter.MatchModeEnum.StartsWith => query.Where(d =>
                d.AttachmentDocumentTypes.Any(a => a.SpIdentifier != null && a.SpIdentifier.StartsWith(searchValue))
            ),

            ColumnFilter.MatchModeEnum.Equals => query.Where(d =>
                d.AttachmentDocumentTypes.Any(a => a.SpIdentifier == searchValue)
            ),

            _ => query,
        };
    }

    private static IQueryable<PartnerDocumentUpload> ApplySpIdentifiersSortAtDatabase(
        IQueryable<PartnerDocumentUpload> query,
        SortField sort
    )
    {
        // Sort by the first (alphabetically) SP identifier
        if (sort.Order == SortField.OrderEnum.Desc)
        {
            return query.OrderByDescending(d =>
                d.AttachmentDocumentTypes.Where(a => a.SpIdentifier != null)
                    .Select(a => a.SpIdentifier)
                    .OrderBy(sp => sp)
                    .FirstOrDefault()
            );
        }
        else
        {
            return query.OrderBy(d =>
                d.AttachmentDocumentTypes.Where(a => a.SpIdentifier != null)
                    .Select(a => a.SpIdentifier)
                    .OrderBy(sp => sp)
                    .FirstOrDefault()
            );
        }
    }

    protected override async Task<List<SecretaryPartnerDocumentListDto>> ApplyPostQueryOperations(
        List<SecretaryPartnerDocumentListDto> results,
        TableRequest request,
        CancellationToken cancellationToken
    )
    {
        if (results.Count == 0)
            return results;

        var documentIds = results.Select(r => r.Id).ToList();

        var documentsWithResults = await _context
            .DocumentUploads.Include(d => d.DocumentAnalysisResults)
            .Include(d => d.DocumentValidationResults)
            .Include(d => d.DocumentEloResults)
            .Where(d => documentIds.Contains(d.Id))
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        var documentLookup = documentsWithResults.ToDictionary(d => d.Id);

        foreach (var result in results)
        {
            if (documentLookup.TryGetValue(result.Id, out var document))
            {
                var status = _statusService.CalculateOverallStatusForPartner(
                    [.. document.DocumentAnalysisResults],
                    [.. document.DocumentValidationResults],
                    [.. document.DocumentEloResults]
                );

                result.ProcessingStatus = status;
            }
            else
            {
                result.ProcessingStatus = ProcessingStatusEnum.Default;
            }
        }

        return results;
    }
}

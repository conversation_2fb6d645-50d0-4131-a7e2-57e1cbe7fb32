using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Services.ArrivalDateCalculation;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Document.Services.SpIdentifier;
using PartnerPortal.Backend.Shared.Attachment;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using static PartnerPortal.Backend.Document.DocumentApiStub.Models.SimpleDocumentStatusReportDto;
using ContractsFileInfo = PartnerPortal.Backend.Contracts.ContractsApiStub.Models.FileInfo;

namespace PartnerPortal.Backend.Document.Services.SecretaryDocument;

public class SecretaryDocumentUploadService : ISecretaryDocumentUploadService
{
    private readonly UserDbContext _context;

    private readonly ILogger<SecretaryDocumentUploadService> _logger;

    private readonly IAttachmentService _attachmentService;

    private readonly IArrivalDateCalculationService _arrivalDateCalculationService;

    private readonly IProcessingManagerService _processingManagerService;
    private readonly IDocumentProcessingStatusService _statusService;

    private readonly IMapper _mapper;

    private readonly IPartnerDocumentDataTableService _partnerDocumentDataTableService;

    private readonly ISecretaryDocumentDataTableService _secretaryDocumentDataTableService;
    private readonly ISpIdentifierService _spIdentifierService;

    public SecretaryDocumentUploadService(
        UserDbContext context,
        ILogger<SecretaryDocumentUploadService> logger,
        IAttachmentService attachmentService,
        IArrivalDateCalculationService arrivalDateCalculationService,
        IProcessingManagerService processingManagerService,
        IDocumentProcessingStatusService statusService,
        IMapper mapper,
        IPartnerDocumentDataTableService partnerDocumentDataTableService,
        ISecretaryDocumentDataTableService secretaryDocumentDataTableService,
        ISpIdentifierService spIdentifierService,
        IConfiguration config
    )
    {
        _context = context;
        _logger = logger;
        _attachmentService = attachmentService;
        _arrivalDateCalculationService = arrivalDateCalculationService;
        _processingManagerService = processingManagerService;
        _statusService = statusService;
        _mapper = mapper;
        _partnerDocumentDataTableService = partnerDocumentDataTableService;
        _secretaryDocumentDataTableService = secretaryDocumentDataTableService;
        _spIdentifierService = spIdentifierService;
        // Override the target file path for document attachments
        string documentAttachmentPath = config.GetConfigValue<string>("DocumentModule:FilesPath");
        long fileSizeLimit = config.GetConfigValue<long>("DocumentModule:FileSizeLimit");
        int fileCountLimit = config.GetConfigValue<int>("DocumentModule:FileCountLimit");
        string[] permittedExtensions = config.GetConfigValue<string[]>("DocumentModule:PermittedExtensions");

        (_attachmentService as AttachmentService)?.SetConfiguration(
            documentAttachmentPath,
            fileSizeLimit,
            fileCountLimit,
            permittedExtensions
        );
    }

    public async Task<SecretaryDocumentUploadDto> CreateSecretaryDocumentUploadDraft(
        int userId,
        CreateSecretaryDocumentUploadDraftRequest request
    )
    {
        _logger.LogInformation(
            "Creating secretary document upload draft with context id {ContextId}",
            request.ContextId
        );

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var documentUpload = new SecretaryDocumentUpload
            {
                Id = request.ContextId,
                UserId = userId,
                ContactNo = request.ContactNo,
                ContactName = request.ContactName,
                ProjectNo = request.ProjectNo,
                ProjectDescription = request.ProjectDescription,
                ContractNo = request.ContractNo,
                AttachmentDocumentTypes =
                [
                    .. request.AttachmentDocumentTypes.Select(x => new AttachmentDocumentType
                    {
                        DocumentUploadId = request.ContextId,
                        AttachmentId = x.Attachment.FileId,
                    }),
                ],
                Status = UploadStatusEnum.Draft,
                Comment = request.Comment,
                DateOfArrival = request.DateOfArrival,
                SecretaryDateOfArrival = request.SecretaryDateOfArrival,
                UploadType = request.SecretaryUploadType,
            };

            await _context.DocumentUploads.AddAsync(documentUpload);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Secretary document upload created with id {DocumentUploadId}", documentUpload.Id);

            await CommitFiles(request.AttachmentDocumentTypes, userId, documentUpload.Id);

            await transaction.CommitAsync();

            return _mapper.Map<SecretaryDocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(
                ex,
                "Error creating secretary document upload draft with context id {ContextId}",
                request.ContextId
            );
            throw;
        }
    }

    public async Task SubmitSecretaryDocumentUpload(
        Guid documentUploadId,
        UpdateSecretaryDocumentUploadDraftRequest request
    )
    {
        _logger.LogInformation("Submitting secretary document upload with id {DocumentUploadId}", documentUploadId);

        var documentUpload = await _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .FirstOrDefaultAsync(x => x.Id == documentUploadId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Secretary document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        if (documentUpload.Status != UploadStatusEnum.Draft)
        {
            _logger.LogWarning(
                "Cannot submit secretary document upload with id {DocumentUploadId} because it is not in draft status",
                documentUploadId
            );
            throw new ValidationException("Only draft documents can be submitted");
        }

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            UpdateDocumentUpload(documentUpload, request);
            documentUpload.Status = UploadStatusEnum.Submitted;
            await _context.SaveChangesAsync();

            await CommitFiles(request.AttachmentDocumentTypes, documentUpload.UserId, documentUpload.Id);

            // Handle special case for batch uploads with multiple attachments
            List<(int AttachmentId, Guid DocumentUploadId)> batchJobsFromSplit = [];
            if (
                documentUpload.UploadType == SecretaryUploadTypeEnum.Batch
                && documentUpload.AttachmentDocumentTypes.Count > 1
            )
            {
                batchJobsFromSplit = await SplitBatchUploadIntoIndividualUploads(documentUpload);
                _context.DocumentUploads.Remove(documentUpload);
                await _context.SaveChangesAsync();
            }

            var (analysisJobs, batchJobs, perCaseClassificationUploadId) =
                batchJobsFromSplit.Count > 0
                    ? (new List<(int, Guid)>(), batchJobsFromSplit, (Guid?)null)
                    : await ScheduleProcessingJobsAsync(documentUpload);

            await transaction.CommitAsync();

            await TriggerScheduledJobsAsync(analysisJobs, batchJobs, perCaseClassificationUploadId);

            _logger.LogInformation(
                "Secretary document upload with id {DocumentUploadId} successfully submitted",
                documentUploadId
            );
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(
                ex,
                "Error submitting secretary document upload with id {DocumentUploadId}",
                documentUploadId
            );
            throw;
        }
    }

    public async Task<SecretaryDocumentUploadDto> UpdateSecretaryDocumentUploadDraft(
        Guid documentUploadId,
        UpdateSecretaryDocumentUploadDraftRequest request
    )
    {
        var documentUpload = await _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .FirstOrDefaultAsync(x => x.Id == documentUploadId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Secretary document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        _logger.LogInformation("Updating secretary document upload with id {DocumentUploadId}", documentUploadId);

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            UpdateDocumentUpload(documentUpload, request);

            await _context.SaveChangesAsync();

            await CommitFiles(request.AttachmentDocumentTypes, documentUpload.UserId, documentUpload.Id);

            await transaction.CommitAsync();

            _logger.LogInformation(
                "Secretary document upload updated successfully with id {DocumentUploadId}",
                documentUploadId
            );

            return _mapper.Map<SecretaryDocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(
                ex,
                "Error updating secretary document upload with id {DocumentUploadId}",
                documentUploadId
            );
            throw;
        }
    }

    public async Task<GetSecretaryDocumentUploadTotalCountsResponse> GetSecretaryDocumentUploadTotalCounts(
        string? projectNo
    )
    {
        var query = _context.DocumentUploads.OfType<SecretaryDocumentUpload>();

        if (!string.IsNullOrWhiteSpace(projectNo))
        {
            query = query.Where(x => x.ProjectNo == projectNo);
        }

        var counts = await query
            .GroupBy(x => x.Status)
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Status, x => x.Count);

        int draftCount = counts.GetValueOrDefault(UploadStatusEnum.Draft, 0);
        int submittedCount = counts.GetValueOrDefault(UploadStatusEnum.Submitted, 0);
        int totalCount = draftCount + submittedCount;

        return new GetSecretaryDocumentUploadTotalCountsResponse
        {
            Total = totalCount,
            Draft = draftCount,
            Submitted = submittedCount,
        };
    }

    public async Task<SecretaryDocumentUploadDto> GetSecretaryDocumentUpload(Guid documentUploadId)
    {
        var documentUpload = await _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .ThenInclude(x => x.Attachment)
            .Include(i => i.Classifications)
            .Include(i => i.User)
            .AsNoTracking()
            .AsSplitQuery()
            .FirstOrDefaultAsync(x => x.Id == documentUploadId);

        var result =
            documentUpload == null
                ? throw new NotFoundException()
                : _mapper.Map<SecretaryDocumentUploadDto>(documentUpload);

        foreach (var attachmentDocumentType in result.AttachmentDocumentTypes)
        {
            List<SecretaryClassificationDto> classifications = [];
            foreach (
                var classification in documentUpload.Classifications.Where(x =>
                    x.AttachmentId == attachmentDocumentType.Attachment.FileId
                )
            )
            {
                var eloResults = await _context
                    .DocumentEloResults.Where(x =>
                        x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == classification.Id
                    )
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var analysisResult = await _context
                    .DocumentAnalysisResults.Where(x =>
                        x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == classification.Id
                    )
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var validationResult = await _context
                    .DocumentValidationResults.Where(x =>
                        x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == classification.Id
                    )
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var classificationResult = await _context
                    .DocumentClassificationResults.Where(x =>
                        x.DocumentUploadId == documentUpload.Id
                        && x.AttachmentId == attachmentDocumentType.Attachment.FileId
                    )
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var (processingStatus, failureReason) = _statusService.CalculatePerItemProcessingStatus(
                    eloResults,
                    validationResult,
                    analysisResult,
                    classificationResult
                );

                classifications.Add(
                    new SecretaryClassificationDto
                    {
                        SpIdentifier = classification.SpIdentifier,
                        DocumentType = classification.DocumentType,
                        ProcessingStatus = processingStatus,
                        FailureReason = failureReason,
                    }
                );
            }

            attachmentDocumentType.Classifications = classifications;
        }

        return result;
    }

    public async Task RemoveSecretaryDocumentUpload(Guid documentUploadId)
    {
        var documentUpload =
            await _context
                .DocumentUploads.OfType<SecretaryDocumentUpload>()
                .FirstOrDefaultAsync(x => x.Id == documentUploadId) ?? throw new NotFoundException();

        if (documentUpload.Status != UploadStatusEnum.Draft)
        {
            throw new ValidationException("Only draft documents can be removed");
        }

        _context.DocumentUploads.Remove(documentUpload);
        await _context.SaveChangesAsync();

        await _attachmentService.DeleteFiles(documentUpload.Id.ToString(), documentUpload.UserId.ToString());
    }

    public async Task CreateAndSubmitSecretaryDocument(int userId, CreateSecretaryDocumentUploadDraftRequest request)
    {
        _logger.LogInformation(
            "Creating and submitting secretary document upload with context id {ContextId}",
            request.ContextId
        );

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            if (
                request.SecretaryUploadType == SecretaryUploadTypeEnum.Batch
                && request.AttachmentDocumentTypes.Count > 1
            )
            {
                var batchJobsToSchedule = await HandleBatchUploadWithMultipleAttachments(userId, request);
                await transaction.CommitAsync();
                await TriggerScheduledJobsAsync([], batchJobsToSchedule, null);
                return;
            }

            var documentUpload = CreateSecretaryDocumentUpload(
                request.ContextId,
                userId,
                request,
                UploadStatusEnum.Submitted
            );
            await _context.DocumentUploads.AddAsync(documentUpload);
            await _context.SaveChangesAsync();

            _logger.LogInformation(
                "Secretary document upload created and submitted with id {DocumentUploadId}",
                documentUpload.Id
            );

            await CommitFiles(request.AttachmentDocumentTypes, userId, documentUpload.Id);

            var (analysisJobs, batchJobs, perCaseClassificationUploadId) = await ScheduleProcessingJobsAsync(
                documentUpload
            );

            await transaction.CommitAsync();

            await TriggerScheduledJobsAsync(analysisJobs, batchJobs, perCaseClassificationUploadId);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(
                ex,
                "Error creating and submitting secretary document upload with context id {ContextId}",
                request.ContextId
            );
            throw;
        }
    }

    private static void UpdateDocumentUpload(
        SecretaryDocumentUpload documentUpload,
        UpdateSecretaryDocumentUploadDraftRequest request
    )
    {
        documentUpload.ContactNo = request.ContactNo;
        documentUpload.ProjectNo = request.ProjectNo;
        documentUpload.ProjectDescription = request.ProjectDescription;
        documentUpload.ContractNo = request.ContractNo;
        documentUpload.Comment = request.Comment;
        documentUpload.SecretaryDateOfArrival = request.SecretaryDateOfArrival;
        documentUpload.ContactName = request.ContactName;
        documentUpload.RowVersion = request.RowVersion;
        documentUpload.UpdateAttachments(request.AttachmentDocumentTypes);
    }

    private async Task CommitFiles(
        List<SecretaryAttachmentDocumentTypeDto> attachmentDocumentTypes,
        int userId,
        Guid documentUploadId
    )
    {
        var fileInfos = attachmentDocumentTypes
            .Select(x => new ContractsFileInfo { FileId = x.Attachment.FileId })
            .ToList();
        await _attachmentService.CommitFiles(documentUploadId.ToString(), userId.ToString(), fileInfos);

        _logger.LogInformation("Document upload files committed with id {DocumentUploadId}", documentUploadId);
    }

    private async Task<
        List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)>
    > AutoCreateInvoiceClassificationForSingleAttachment(SecretaryDocumentUpload documentUpload)
    {
        var attachment = documentUpload.AttachmentDocumentTypes.First();
        var classification = new DocumentClassification
        {
            SecretaryDocumentUploadId = documentUpload.Id,
            AttachmentId = attachment.AttachmentId,
            DocumentType = DocumentType.Invoice,
            StartPage = 1,
            EndPage = 1,
            SpIdentifier = await _spIdentifierService.GetNewSpIdentifierAsync(),
        };

        _context.DocumentClassifications.Add(classification);
        await _context.SaveChangesAsync();

        var documentAnalyses = await _processingManagerService.PrepareSecretaryDocuments([classification]);

        _logger.LogInformation(
            "Auto-created invoice classification for single attachment PerCase upload {DocumentUploadId}",
            documentUpload.Id
        );

        return [.. documentAnalyses.Select(da => (da.ProcessingSourceId, da.DocumentUploadId))];
    }

    private async Task<List<(int AttachmentId, Guid DocumentUploadId)>> SplitBatchUploadIntoIndividualUploads(
        SecretaryDocumentUpload originalUpload
    )
    {
        var newUploads = new List<SecretaryDocumentUpload>();
        var jobsToSchedule = new List<(int AttachmentId, Guid DocumentUploadId)>();

        foreach (var attachmentDocumentType in originalUpload.AttachmentDocumentTypes)
        {
            var newUpload = new SecretaryDocumentUpload
            {
                Id = Guid.NewGuid(),
                UserId = originalUpload.UserId,
                ContactNo = originalUpload.ContactNo,
                ContactName = originalUpload.ContactName,
                ProjectNo = originalUpload.ProjectNo,
                ProjectDescription = originalUpload.ProjectDescription,
                ContractNo = originalUpload.ContractNo,
                Comment = originalUpload.Comment,
                DateOfArrival = originalUpload.DateOfArrival,
                SecretaryDateOfArrival = originalUpload.SecretaryDateOfArrival,
                UploadType = SecretaryUploadTypeEnum.Batch,
                Status = UploadStatusEnum.Submitted,
                Type = originalUpload.Type,
                RowVersion = originalUpload.RowVersion,
                AttachmentDocumentTypes = [],
                DocumentEloResults = [],
                DocumentValidationResults = [],
                DocumentAnalysisResults = [],
                DocumentClassificationResults = [],
                Classifications = [],
            };

            await _attachmentService.CopyAttachmentToNewContext(
                attachmentDocumentType.AttachmentId,
                originalUpload.Id.ToString(),
                newUpload.Id.ToString(),
                originalUpload.UserId.ToString()
            );

            newUpload.AttachmentDocumentTypes.Add(
                new AttachmentDocumentType
                {
                    DocumentUploadId = newUpload.Id,
                    AttachmentId = attachmentDocumentType.AttachmentId,
                    DocumentType = attachmentDocumentType.DocumentType,
                }
            );

            _context.DocumentUploads.Add(newUpload);
            newUploads.Add(newUpload);

            _logger.LogInformation(
                "Created new batch upload {NewUploadId} for attachment {AttachmentId} from original upload {OriginalUploadId}",
                newUpload.Id,
                attachmentDocumentType.AttachmentId,
                originalUpload.Id
            );
        }

        await _context.SaveChangesAsync();

        foreach (var newUpload in newUploads)
        {
            _logger.LogInformation("Classifying new upload {NewUploadId}", newUpload.Id);
            var documents = await _processingManagerService.ClassifySecretaryDocuments(newUpload);
            jobsToSchedule.AddRange(documents);
        }

        _logger.LogInformation(
            "Split original batch upload {OriginalUploadId} into {Count} individual uploads",
            originalUpload.Id,
            newUploads.Count
        );

        return jobsToSchedule;
    }

    private static SecretaryDocumentUpload CreateSecretaryDocumentUpload(
        Guid contextId,
        int userId,
        CreateSecretaryDocumentUploadDraftRequest request,
        UploadStatusEnum status
    )
    {
        return new SecretaryDocumentUpload
        {
            Id = contextId,
            UserId = userId,
            ContactNo = request.ContactNo,
            ContactName = request.ContactName,
            ProjectNo = request.ProjectNo,
            ProjectDescription = request.ProjectDescription,
            ContractNo = request.ContractNo,
            AttachmentDocumentTypes =
            [
                .. request.AttachmentDocumentTypes.Select(x => new AttachmentDocumentType
                {
                    DocumentUploadId = contextId,
                    AttachmentId = x.Attachment.FileId,
                }),
            ],
            Status = status,
            Comment = request.Comment,
            DateOfArrival = request.DateOfArrival,
            SecretaryDateOfArrival = request.SecretaryDateOfArrival,
            UploadType = request.SecretaryUploadType,
        };
    }

    private async Task<List<(int AttachmentId, Guid DocumentUploadId)>> HandleBatchUploadWithMultipleAttachments(
        int userId,
        CreateSecretaryDocumentUploadDraftRequest request
    )
    {
        await CommitFiles(request.AttachmentDocumentTypes, userId, request.ContextId);

        var batchJobsToSchedule = new List<(int AttachmentId, Guid DocumentUploadId)>();
        var newUploads = new List<SecretaryDocumentUpload>();

        foreach (var attachmentDocumentType in request.AttachmentDocumentTypes)
        {
            var newUpload = new SecretaryDocumentUpload
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                ContactNo = request.ContactNo,
                ContactName = request.ContactName,
                ProjectNo = request.ProjectNo,
                ProjectDescription = request.ProjectDescription,
                ContractNo = request.ContractNo,
                Comment = request.Comment,
                DateOfArrival = request.DateOfArrival,
                SecretaryDateOfArrival = request.SecretaryDateOfArrival,
                UploadType = SecretaryUploadTypeEnum.Batch,
                Status = UploadStatusEnum.Submitted,
                AttachmentDocumentTypes = [],
            };

            await _attachmentService.CopyAttachmentToNewContext(
                attachmentDocumentType.Attachment.FileId,
                request.ContextId.ToString(),
                newUpload.Id.ToString(),
                userId.ToString()
            );

            newUpload.AttachmentDocumentTypes.Add(
                new AttachmentDocumentType
                {
                    DocumentUploadId = newUpload.Id,
                    AttachmentId = attachmentDocumentType.Attachment.FileId,
                }
            );

            _context.DocumentUploads.Add(newUpload);
            newUploads.Add(newUpload);
        }

        await _context.SaveChangesAsync();

        foreach (var newUpload in newUploads)
        {
            var documents = await _processingManagerService.ClassifySecretaryDocuments(newUpload);
            batchJobsToSchedule.AddRange(documents);
        }

        return batchJobsToSchedule;
    }

    private async Task<(
        List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)> analysisJobs,
        List<(int AttachmentId, Guid DocumentUploadId)> batchJobs,
        Guid? perCaseClassificationUploadId
    )> ScheduleProcessingJobsAsync(SecretaryDocumentUpload documentUpload)
    {
        List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)> analysisJobsToSchedule = [];
        List<(int AttachmentId, Guid DocumentUploadId)> batchJobsToSchedule = [];
        Guid? perCaseClassificationToScheduleForUploadId = null;

        if (documentUpload.UploadType == SecretaryUploadTypeEnum.PerCase)
        {
            if (documentUpload.AttachmentDocumentTypes.Count == 1)
            {
                analysisJobsToSchedule = await AutoCreateInvoiceClassificationForSingleAttachment(documentUpload);
            }
            else
            {
                await _processingManagerService.ClassifySecretaryDocuments(documentUpload);
                perCaseClassificationToScheduleForUploadId = documentUpload.Id;
            }
        }
        else if (documentUpload.UploadType == SecretaryUploadTypeEnum.Batch)
        {
            batchJobsToSchedule = await _processingManagerService.ClassifySecretaryDocuments(documentUpload);
        }
        else
        {
            throw new ValidationException("Invalid upload type");
        }

        return (analysisJobsToSchedule, batchJobsToSchedule, perCaseClassificationToScheduleForUploadId);
    }

    private async Task TriggerScheduledJobsAsync(
        List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)> analysisJobs,
        List<(int AttachmentId, Guid DocumentUploadId)> batchJobs,
        Guid? perCaseClassificationUploadId
    )
    {
        if (analysisJobs.Count > 0)
        {
            await _processingManagerService.TriggerDocumentAnalysisJobs(analysisJobs);
        }
        if (perCaseClassificationUploadId.HasValue)
        {
            await _processingManagerService.TriggerPerCaseClassificationJobs(perCaseClassificationUploadId.Value);
        }
        if (batchJobs.Count > 0)
        {
            await _processingManagerService.TriggerBatchClassificationJobs(batchJobs);
        }
    }

    public async Task<SecretaryDateOfArrivalDto> GetSecretaryDocumentDateOfArrival()
    {
        // mindig az előző munkanap kerüljön betöltésre a naptár kalkuláció szerint
        var dateOfArrival = await _arrivalDateCalculationService.CalculateLastDateOfArrival(DateTime.Now);
        return new SecretaryDateOfArrivalDto { DateOfArrival = dateOfArrival };
    }

    public async Task<ListPartnerDocuments200Response> ListPartnerDocuments(TableRequest request)
    {
        return await _partnerDocumentDataTableService.ExecuteDataTableQuery(request);
    }

    public async Task<SecretaryPartnerDocumentDetailDto> GetPartnerDocumentDetails(Guid documentUploadId)
    {
        var documentUpload = await _context
            .DocumentUploads.OfType<PartnerDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .ThenInclude(x => x.Attachment)
            .Include(i => i.User)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == documentUploadId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Partner document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        var result = _mapper.Map<SecretaryPartnerDocumentDetailDto>(documentUpload);

        foreach (
            var attachmentDocumentType in documentUpload.AttachmentDocumentTypes.Where(x =>
                x.IsInvoiceType || x.DocumentType == DocumentType.CompletionCert
            )
        )
        {
            DocumentEloResult? eloResults = null;
            if (attachmentDocumentType.IsInvoiceType)
            {
                eloResults = await _context
                    .DocumentEloResults.Where(x =>
                        x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                    )
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
            }

            var analysisResult = await _context
                .DocumentAnalysisResults.Where(x =>
                    x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                )
                .AsNoTracking()
                .FirstOrDefaultAsync();

            var validationResult = await _context
                .DocumentValidationResults.Where(x =>
                    x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                )
                .AsNoTracking()
                .FirstOrDefaultAsync();

            var (processingStatus, failureReason) = _statusService.CalculatePerItemProcessingStatus(
                eloResults,
                validationResult,
                analysisResult,
                null
            );

            var resultAttachment = result.AttachmentDocumentTypes.First(x =>
                x.Attachment.FileId == attachmentDocumentType.AttachmentId
            );
            resultAttachment.ProcessingStatus = processingStatus;
            resultAttachment.FailureReason = failureReason;
        }

        return result;
    }

    public async Task<ListSecretaryDocuments200Response> ListSecretaryDocuments(TableRequest request)
    {
        return await _secretaryDocumentDataTableService.ExecuteDataTableQuery(request);
    }
}

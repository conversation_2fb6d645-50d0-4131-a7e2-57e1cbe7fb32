using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Services.Base;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.SecretaryDocument;

public interface ISecretaryDocumentDataTableService
    : IBaseDataTableService<SecretaryDocumentUpload, SecretaryDocumentListDto, ListSecretaryDocuments200Response> { }

public class SecretaryDocumentDataTableService
    : BaseDataTableService<SecretaryDocumentUpload, SecretaryDocumentListDto, ListSecretaryDocuments200Response>,
        ISecretaryDocumentDataTableService
{
    private readonly UserDbContext _context;
    private readonly IDocumentProcessingStatusService _statusService;

    public SecretaryDocumentDataTableService(
        UserDbContext context,
        IMapper mapper,
        IDocumentProcessingStatusService statusService
    )
        : base(mapper)
    {
        _context = context;
        _statusService = statusService;
    }

    protected override IQueryable<SecretaryDocumentUpload> BuildBaseQuery()
    {
        var query = _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .Include(i => i.Classifications)
            .Include(i => i.DocumentClassificationResults)
            .Include(i => i.DocumentAnalysisResults)
            .Include(i => i.DocumentValidationResults)
            .Include(i => i.DocumentEloResults);

        return query;
    }

    protected override ListSecretaryDocuments200Response BuildResponse(
        List<SecretaryDocumentListDto> results,
        int totalCount,
        TableRequest request
    )
    {
        return new ListSecretaryDocuments200Response
        {
            Data = results,
            TotalRecords = totalCount,
            Page = request.Page,
            PageSize = request.PageSize,
        };
    }

    protected override HashSet<string> GetStandardDatabaseFields()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Id",
            "CreatedDate",
            "Status",
            "Uploader",
            "UploadType",
            "ContactName",
        };
    }

    protected override HashSet<string> GetDatabaseComputedFields()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "SpIdentifiers",
            "ProcessingStatus",
            "DateOfArrival",
        };
    }

    protected override Dictionary<string, string> GetFieldMappings()
    {
        return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase) { { "Uploader", "User.ProfileName" } };
    }

    protected override Task<IQueryable<SecretaryDocumentUpload>> ApplyDatabaseComputedFiltersImplementation(
        IQueryable<SecretaryDocumentUpload> query,
        List<ColumnFilter> filters,
        CancellationToken cancellationToken
    )
    {
        foreach (var filter in filters)
        {
            switch (filter.Field.ToLower())
            {
                case "spidentifiers":
                    query = ApplySpIdentifiersFilterAtDatabase(query, filter);
                    break;

                case "processingstatus":
                    if (
                        filter.Value != null
                        && Enum.TryParse<ProcessingStatusEnum>(filter.Value.ToString(), true, out var targetStatus)
                    )
                    {
                        query = _statusService.ApplyProcessingStatusFilterForSecretary(query, targetStatus);
                    }
                    break;

                case "dateofarrival":
                    query = ApplyDateOfArrivalFilterAtDatabase(query, filter);
                    break;
            }
        }

        return Task.FromResult(query);
    }

    protected override Task<IQueryable<SecretaryDocumentUpload>> ApplyDatabaseComputedSortsImplementation(
        IQueryable<SecretaryDocumentUpload> query,
        List<SortField> sorts,
        CancellationToken cancellationToken
    )
    {
        foreach (var sort in sorts)
        {
            switch (sort.Field.ToLower())
            {
                case "spidentifiers":
                    query = ApplySpIdentifiersSortAtDatabase(query, sort);
                    break;

                case "dateofarrival":
                    query = ApplyDateOfArrivalSortAtDatabase(query, sort);
                    break;
            }
        }

        return Task.FromResult(query);
    }

    private static IQueryable<SecretaryDocumentUpload> ApplySpIdentifiersFilterAtDatabase(
        IQueryable<SecretaryDocumentUpload> query,
        ColumnFilter filter
    )
    {
        if (filter.Value == null)
            return query;

        var searchValue = filter.Value.ToString()!;

        return filter.MatchMode switch
        {
            // Manual uploads: SP comes from AttachmentDocumentTypes; Automated uploads: SP comes from Classifications
            ColumnFilter.MatchModeEnum.Contains => query.Where(d =>
                (
                    d.UploadType == SecretaryUploadTypeEnum.Manual
                    && d.AttachmentDocumentTypes.Any(a =>
                        a.SpIdentifier != null && a.SpIdentifier.Contains(searchValue)
                    )
                )
                || (
                    d.UploadType != SecretaryUploadTypeEnum.Manual
                    && d.Classifications.Any(c => c.SpIdentifier != null && c.SpIdentifier.Contains(searchValue))
                )
            ),

            ColumnFilter.MatchModeEnum.StartsWith => query.Where(d =>
                (
                    d.UploadType == SecretaryUploadTypeEnum.Manual
                    && d.AttachmentDocumentTypes.Any(a =>
                        a.SpIdentifier != null && a.SpIdentifier.StartsWith(searchValue)
                    )
                )
                || (
                    d.UploadType != SecretaryUploadTypeEnum.Manual
                    && d.Classifications.Any(c => c.SpIdentifier != null && c.SpIdentifier.StartsWith(searchValue))
                )
            ),

            ColumnFilter.MatchModeEnum.Equals => query.Where(d =>
                (
                    d.UploadType == SecretaryUploadTypeEnum.Manual
                    && d.AttachmentDocumentTypes.Any(a => a.SpIdentifier == searchValue)
                )
                || (
                    d.UploadType != SecretaryUploadTypeEnum.Manual
                    && d.Classifications.Any(c => c.SpIdentifier == searchValue)
                )
            ),

            _ => query,
        };
    }

    private static IQueryable<SecretaryDocumentUpload> ApplySpIdentifiersSortAtDatabase(
        IQueryable<SecretaryDocumentUpload> query,
        SortField sort
    )
    {
        if (sort.Order == SortField.OrderEnum.Desc)
        {
            return query.OrderByDescending(d =>
                (
                    d.UploadType == SecretaryUploadTypeEnum.Manual
                        ? d
                            .AttachmentDocumentTypes.Where(a => a.SpIdentifier != null)
                            .Select(a => a.SpIdentifier!)
                            .Min()
                        : d.Classifications.Where(c => c.SpIdentifier != null).Select(c => c.SpIdentifier!).Min()
                ) ?? string.Empty
            );
        }
        else
        {
            return query.OrderBy(d =>
                (
                    d.UploadType == SecretaryUploadTypeEnum.Manual
                        ? d
                            .AttachmentDocumentTypes.Where(a => a.SpIdentifier != null)
                            .Select(a => a.SpIdentifier!)
                            .Min()
                        : d.Classifications.Where(c => c.SpIdentifier != null).Select(c => c.SpIdentifier!).Min()
                ) ?? string.Empty
            );
        }
    }

    protected override async Task<List<SecretaryDocumentListDto>> ApplyPostQueryOperations(
        List<SecretaryDocumentListDto> results,
        TableRequest request,
        CancellationToken cancellationToken
    )
    {
        if (results.Count == 0)
            return results;

        var documentIds = results.Select(r => r.Id).ToList();

        var documentsWithResults = await _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(d => d.DocumentClassificationResults)
            .Include(d => d.Classifications)
            .Include(d => d.DocumentAnalysisResults)
            .Include(d => d.DocumentValidationResults)
            .Include(d => d.DocumentEloResults)
            .Include(d => d.AttachmentDocumentTypes)
            .Where(d => documentIds.Contains(d.Id))
            .AsNoTracking()
            .AsSplitQuery()
            .ToListAsync(cancellationToken);

        var documentLookup = documentsWithResults.ToDictionary(d => d.Id);

        foreach (var result in results)
        {
            if (documentLookup.TryGetValue(result.Id, out var document))
            {
                if (document.UploadType == SecretaryUploadTypeEnum.Manual)
                {
                    result.SpIdentifiers =
                    [
                        .. document
                            .AttachmentDocumentTypes.Where(x => x.SpIdentifier != null)
                            .Select(x => x.SpIdentifier!)
                            .Distinct()
                            .OrderBy(x => x),
                    ];
                }
                else
                {
                    result.SpIdentifiers =
                    [
                        .. document
                            .Classifications.Where(c => !string.IsNullOrWhiteSpace(c.SpIdentifier))
                            .Select(c => c.SpIdentifier!)
                            .Distinct()
                            .OrderBy(x => x),
                    ];
                }

                var status = _statusService.CalculateOverallStatusForSecretary(
                    [.. document.DocumentAnalysisResults],
                    [.. document.DocumentValidationResults],
                    [.. document.DocumentEloResults],
                    [.. document.DocumentClassificationResults],
                    document.UploadType
                );

                result.ProcessingStatus = status;
            }
            else
            {
                result.ProcessingStatus = ProcessingStatusEnum.Processing;
                result.SpIdentifiers = [];
            }
        }

        return results;
    }

    private static IQueryable<SecretaryDocumentUpload> ApplyDateOfArrivalFilterAtDatabase(
        IQueryable<SecretaryDocumentUpload> query,
        ColumnFilter filter
    )
    {
        if (filter.Value == null)
            return query;

        if (!DateTime.TryParse(filter.Value.ToString(), out var dateValue))
            return query;

        return filter.MatchMode switch
        {
            ColumnFilter.MatchModeEnum.Equals => query.Where(d =>
                (d.SecretaryDateOfArrival ?? d.DateOfArrival).HasValue
                && (d.SecretaryDateOfArrival ?? d.DateOfArrival)!.Value.Date == dateValue.Date
            ),

            ColumnFilter.MatchModeEnum.DateAfter => query.Where(d =>
                (d.SecretaryDateOfArrival ?? d.DateOfArrival).HasValue
                && (d.SecretaryDateOfArrival ?? d.DateOfArrival)!.Value.Date > dateValue.Date
            ),

            ColumnFilter.MatchModeEnum.DateBefore => query.Where(d =>
                (d.SecretaryDateOfArrival ?? d.DateOfArrival).HasValue
                && (d.SecretaryDateOfArrival ?? d.DateOfArrival)!.Value.Date < dateValue.Date
            ),

            ColumnFilter.MatchModeEnum.DateIs => query.Where(d =>
                (d.SecretaryDateOfArrival ?? d.DateOfArrival).HasValue
                && (d.SecretaryDateOfArrival ?? d.DateOfArrival)!.Value.Date == dateValue.Date
            ),

            ColumnFilter.MatchModeEnum.DateIsNot => query.Where(d =>
                (d.SecretaryDateOfArrival ?? d.DateOfArrival).HasValue
                && (d.SecretaryDateOfArrival ?? d.DateOfArrival)!.Value.Date != dateValue.Date
            ),

            _ => query,
        };
    }

    private static IQueryable<SecretaryDocumentUpload> ApplyDateOfArrivalSortAtDatabase(
        IQueryable<SecretaryDocumentUpload> query,
        SortField sort
    )
    {
        if (sort.Order == SortField.OrderEnum.Desc)
        {
            return query.OrderByDescending(d => d.SecretaryDateOfArrival ?? d.DateOfArrival);
        }
        else
        {
            return query.OrderBy(d => d.SecretaryDateOfArrival ?? d.DateOfArrival);
        }
    }
}

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Extensions;
using PartnerPortal.Backend.Document.Services.ArrivalDateCalculation;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Document.Services.SpIdentifier;
using PartnerPortal.Backend.Shared.Attachment;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using ContractsFileInfo = PartnerPortal.Backend.Contracts.ContractsApiStub.Models.FileInfo;

namespace PartnerPortal.Backend.Document.Services.SecretaryDocument;

public interface ISecretaryManualDocumentUploadService
{
    Task<SecretaryManualDocumentUploadDto> CreateAndSubmitDocument(
        int userId,
        CreateSecretaryManualDocumentUploadDraftRequest request
    );

    Task<SecretaryManualDocumentUploadDto> CreateDocumentUploadDraft(
        int userId,
        CreateSecretaryManualDocumentUploadDraftRequest request
    );

    Task<SecretaryManualDocumentUploadDto> SubmitDocumentUpload(
        int userId,
        Guid documentUploadId,
        UpdateSecretaryManualDocumentUploadDraftRequest request
    );

    Task<SecretaryManualDocumentUploadDto> UpdateDocumentUploadDraft(
        int userId,
        Guid documentUploadId,
        UpdateSecretaryManualDocumentUploadDraftRequest request
    );

    Task<SecretaryManualDocumentUploadDto> GetDocumentUpload(int userId, Guid documentUploadId);
}

public class SecretaryManualDocumentUploadService : ISecretaryManualDocumentUploadService
{
    private readonly UserDbContext _context;

    private readonly ILogger<SecretaryManualDocumentUploadService> _logger;

    private readonly IAttachmentService _attachmentService;

    private readonly ISpIdentifierService _spIdentifierService;

    private readonly IArrivalDateCalculationService _arrivalDateCalculationService;
    private readonly IProcessingManagerService _processingManagerService;
    private readonly IDocumentProcessingStatusService _statusService;

    private readonly IMapper _mapper;

    public SecretaryManualDocumentUploadService(
        UserDbContext context,
        ILogger<SecretaryManualDocumentUploadService> logger,
        IAttachmentService attachmentService,
        ISpIdentifierService spIdentifierService,
        IArrivalDateCalculationService arrivalDateCalculationService,
        IProcessingManagerService processingManagerService,
        IDocumentProcessingStatusService statusService,
        IMapper mapper
    )
    {
        _context = context;
        _logger = logger;
        _attachmentService = attachmentService;
        _spIdentifierService = spIdentifierService;
        _arrivalDateCalculationService = arrivalDateCalculationService;
        _processingManagerService = processingManagerService;
        _statusService = statusService;
        _mapper = mapper;
    }

    public async Task<SecretaryManualDocumentUploadDto> CreateDocumentUploadDraft(
        int userId,
        CreateSecretaryManualDocumentUploadDraftRequest request
    )
    {
        _logger.LogInformation("Creating document upload draft with context id {ContextId}", request.ContextId);
        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var documentUpload = new SecretaryDocumentUpload
            {
                Id = request.ContextId,
                UserId = userId,
                ContactNo = request.ContactNo,
                ContactName = request.ContactName,
                ProjectNo = request.ProjectNo,
                ProjectDescription = request.ProjectDescription,
                ContractNo = request.ContractNo,
                Type = request.Type,
                Status = UploadStatusEnum.Draft,
                Comment = request.Comment,
                DateOfArrival = request.DateOfArrival,
                SecretaryDateOfArrival = request.SecretaryDateOfArrival,
                UploadType = SecretaryUploadTypeEnum.Manual,
                AttachmentDocumentTypes = [],
            };
            await documentUpload.AddAttachmentDocumentTypes(_spIdentifierService, request.AttachmentDocumentTypes);

            await _context.DocumentUploads.AddAsync(documentUpload);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Document upload created with id {DocumentUploadId}", documentUpload.Id);

            await CommitFiles(request.AttachmentDocumentTypes, userId, documentUpload.Id);

            await transaction.CommitAsync();

            return _mapper.Map<SecretaryManualDocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error creating document upload draft with context id {ContextId}", request.ContextId);
            throw;
        }
    }

    public async Task<SecretaryManualDocumentUploadDto> SubmitDocumentUpload(
        int userId,
        Guid documentUploadId,
        UpdateSecretaryManualDocumentUploadDraftRequest request
    )
    {
        _logger.LogInformation("Submitting document upload with id {DocumentUploadId}", documentUploadId);
        var documentUpload = await _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .FirstOrDefaultAsync(x => x.Id == documentUploadId && x.UserId == userId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        // Validate that the document is in draft status
        if (documentUpload.Status != UploadStatusEnum.Draft)
        {
            _logger.LogWarning(
                "Cannot submit document upload with id {DocumentUploadId} because it is not in draft status",
                documentUploadId
            );
            throw new ValidationException("Only draft documents can be submitted");
        }

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            await UpdateDocumentUpload(documentUpload, request);

            documentUpload.ValidateSubmit(out var validationError);
            if (validationError != null)
            {
                throw new ValidationException(validationError);
            }

            documentUpload.Status = UploadStatusEnum.Submitted;
            documentUpload.DateOfArrival = await _arrivalDateCalculationService.CalculateDateOfArrival(DateTime.Now);

            await _context.SaveChangesAsync();

            await CommitFiles(request.AttachmentDocumentTypes, documentUpload.UserId, documentUpload.Id);

            var documents = await _processingManagerService.PrepareManualDocuments(documentUpload);

            await transaction.CommitAsync();

            await _processingManagerService.TriggerDocumentAnalysisJobs(documents);

            _logger.LogInformation(
                "Document upload with id {DocumentUploadId} successfully submitted",
                documentUploadId
            );

            return _mapper.Map<SecretaryManualDocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error submitting document upload with id {DocumentUploadId}", documentUploadId);
            throw;
        }
    }

    private async Task UpdateDocumentUpload(
        SecretaryDocumentUpload documentUpload,
        UpdateSecretaryManualDocumentUploadDraftRequest request
    )
    {
        documentUpload.ProjectNo = request.ProjectNo;
        documentUpload.ProjectDescription = request.ProjectDescription;
        documentUpload.ContactNo = request.ContactNo;
        documentUpload.ContactName = request.ContactName;
        documentUpload.ContractNo = request.ContractNo;
        documentUpload.Type = request.Type;
        documentUpload.Comment = request.Comment;
        documentUpload.RowVersion = request.RowVersion;
        documentUpload.SecretaryDateOfArrival = request.SecretaryDateOfArrival;
        await documentUpload.UpdateManualDocumentAttachments(_spIdentifierService, request.AttachmentDocumentTypes);
    }

    public async Task<SecretaryManualDocumentUploadDto> UpdateDocumentUploadDraft(
        int userId,
        Guid documentUploadId,
        UpdateSecretaryManualDocumentUploadDraftRequest request
    )
    {
        var documentUpload = await _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .FirstOrDefaultAsync(x => x.Id == documentUploadId && x.UserId == userId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        _logger.LogInformation("Updating document upload with id {DocumentUploadId}", documentUploadId);

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            await UpdateDocumentUpload(documentUpload, request);

            await _context.SaveChangesAsync();

            await CommitFiles(request.AttachmentDocumentTypes, documentUpload.UserId, documentUpload.Id);

            await transaction.CommitAsync();

            _logger.LogInformation("Document upload updated successfully with id {DocumentUploadId}", documentUploadId);

            return _mapper.Map<SecretaryManualDocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error updating document upload with id {DocumentUploadId}", documentUploadId);
            throw;
        }
    }

    public async Task<SecretaryManualDocumentUploadDto> CreateAndSubmitDocument(
        int userId,
        CreateSecretaryManualDocumentUploadDraftRequest request
    )
    {
        _logger.LogInformation(
            "Creating and submitting document upload with context id {ContextId}",
            request.ContextId
        );

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var documentUpload = new SecretaryDocumentUpload
            {
                Id = request.ContextId,
                UserId = userId,
                ContactNo = request.ContactNo,
                ContactName = request.ContactName,
                ProjectNo = request.ProjectNo,
                ProjectDescription = request.ProjectDescription,
                ContractNo = request.ContractNo,
                Status = UploadStatusEnum.Submitted,
                Comment = request.Comment,
                DateOfArrival = request.DateOfArrival,
                SecretaryDateOfArrival = request.SecretaryDateOfArrival,
                Type = request.Type,
                UploadType = SecretaryUploadTypeEnum.Manual,
                AttachmentDocumentTypes = [],
            };

            await documentUpload.AddAttachmentDocumentTypes(_spIdentifierService, request.AttachmentDocumentTypes);

            documentUpload.ValidateSubmit(out var validationError);
            if (validationError != null)
            {
                throw new ValidationException(validationError);
            }

            documentUpload.DateOfArrival = await _arrivalDateCalculationService.CalculateDateOfArrival(DateTime.Now);

            await _context.DocumentUploads.AddAsync(documentUpload);
            await _context.SaveChangesAsync();

            _logger.LogInformation(
                "Document upload created and submitted with id {DocumentUploadId}",
                documentUpload.Id
            );

            await CommitFiles(request.AttachmentDocumentTypes, userId, documentUpload.Id);

            var documents = await _processingManagerService.PrepareManualDocuments(documentUpload);

            await transaction.CommitAsync();

            await _processingManagerService.TriggerDocumentAnalysisJobs(documents);

            return _mapper.Map<SecretaryManualDocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(
                ex,
                "Error creating and submitting document upload with context id {ContextId}",
                request.ContextId
            );
            throw;
        }
    }

    public async Task<SecretaryManualDocumentUploadDto> GetDocumentUpload(int userId, Guid documentUploadId)
    {
        var documentUpload = await _context
            .DocumentUploads.OfType<SecretaryDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .ThenInclude(x => x.Attachment)
            .AsNoTracking()
            .AsSplitQuery()
            .FirstOrDefaultAsync(x => x.Id == documentUploadId && x.UserId == userId);

        if (documentUpload == null)
        {
            _logger.LogWarning(
                "Secretary manual document upload with id {DocumentUploadId} not found",
                documentUploadId
            );
            throw new NotFoundException();
        }

        var result = _mapper.Map<SecretaryManualDocumentUploadDto>(documentUpload);

        foreach (
            var attachmentDocumentType in documentUpload.AttachmentDocumentTypes.Where(x =>
                x.IsInvoiceType || x.DocumentType == DocumentType.CompletionCert
            )
        )
        {
            var eloResults = await _context
                .DocumentEloResults.Where(x =>
                    x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                )
                .AsNoTracking()
                .FirstOrDefaultAsync();

            var analysisResult = await _context
                .DocumentAnalysisResults.Where(x =>
                    x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                )
                .AsNoTracking()
                .FirstOrDefaultAsync();

            var validationResult = await _context
                .DocumentValidationResults.Where(x =>
                    x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                )
                .AsNoTracking()
                .FirstOrDefaultAsync();

            var (processingStatus, failureReason) = _statusService.CalculatePerItemProcessingStatus(
                eloResults,
                validationResult,
                analysisResult,
                null
            );

            var resultAttachment = result.AttachmentDocumentTypes.First(x =>
                x.Attachment.FileId == attachmentDocumentType.AttachmentId
            );
            resultAttachment.ProcessingStatus = processingStatus;
            resultAttachment.FailureReason = failureReason;
        }

        return result;
    }

    private async Task CommitFiles(
        List<AttachmentDocumentTypeDto> attachmentDocumentTypes,
        int userId,
        Guid documentUploadId
    )
    {
        var fileInfos = attachmentDocumentTypes
            .Select(x => new ContractsFileInfo { FileId = x.Attachment.FileId })
            .ToList();
        await _attachmentService.CommitFiles(documentUploadId.ToString(), userId.ToString(), fileInfos);

        _logger.LogInformation("Document upload files committed with id {DocumentUploadId}", documentUploadId);
    }
}

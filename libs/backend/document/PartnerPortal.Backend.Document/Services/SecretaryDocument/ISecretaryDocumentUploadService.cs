using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Document.Services.SecretaryDocument;

public interface ISecretaryDocumentUploadService
{
    Task CreateAndSubmitSecretaryDocument(int userId, CreateSecretaryDocumentUploadDraftRequest request);

    Task<SecretaryDocumentUploadDto> CreateSecretaryDocumentUploadDraft(
        int userId,
        CreateSecretaryDocumentUploadDraftRequest request
    );

    Task SubmitSecretaryDocumentUpload(Guid documentUploadId, UpdateSecretaryDocumentUploadDraftRequest request);

    Task<SecretaryDocumentUploadDto> UpdateSecretaryDocumentUploadDraft(
        Guid documentUploadId,
        UpdateSecretaryDocumentUploadDraftRequest request
    );

    Task<GetSecretaryDocumentUploadTotalCountsResponse> GetSecretaryDocumentUploadTotalCounts(string? projectNo);

    // Task<GetPaginatedSecretaryDocumentsResponse> ListSecretaryDocumentUploads(
    //     UploadStatusEnum? status,
    //     string? projectNo,
    //     int? page,
    //     int? pageSize,
    //     string? searchTerm,
    //     DateTime? fromDate,
    //     DateTime? toDate,
    //     SortByEnum? sortBy,
    //     SortOrderEnum? sortOrder
    // );

    Task<ListSecretaryDocuments200Response> ListSecretaryDocuments(TableRequest request);

    Task<SecretaryDocumentUploadDto> GetSecretaryDocumentUpload(Guid documentUploadId);

    Task RemoveSecretaryDocumentUpload(Guid documentUploadId);

    Task<SecretaryDateOfArrivalDto> GetSecretaryDocumentDateOfArrival();

    Task<ListPartnerDocuments200Response> ListPartnerDocuments(TableRequest request);

    Task<SecretaryPartnerDocumentDetailDto> GetPartnerDocumentDetails(Guid documentUploadId);
}

using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Extensions;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Document.Services.Elo.Builders;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.Elo;

public interface IEloInvoicePreparationService
{
    Task<EloCreateEntityDataModel> PrepareAsync(DocumentJobInputData job);
}

public class EloInvoicePreparationService : IEloInvoicePreparationService
{
    private readonly ILogger<EloInvoicePreparationService> _logger;
    private readonly UserDbContext _context;
    private readonly IInScopeRetrieverService _inScopeRetrieverService;

    public EloInvoicePreparationService(
        ILogger<EloInvoicePreparationService> logger,
        UserDbContext context,
        IInScopeRetrieverService inScopeRetrieverService
    )
    {
        _logger = logger;
        _context = context;
        _inScopeRetrieverService = inScopeRetrieverService;
    }

    public async Task<EloCreateEntityDataModel> PrepareAsync(DocumentJobInputData job)
    {
        var validationResult = await LoadValidationResult(job);

        ValidateDocumentForEloProcessing(validationResult);

        _logger.LogInformation(
            "Starting ExtractDocumentData for document with ID {DocumentId} for attachment {AttachmentDocumentTypeId}",
            validationResult.DocumentUploadId,
            validationResult.ProcessingSourceId
        );

        var document = validationResult.DocumentUpload;
        var invoiceValidationJson = ExtractValidationData(validationResult.ValidationResultJson!);

        DateTime? arrivalDate;
        string source;
        if (document is SecretaryDocumentUpload secretaryInvoice)
        {
            arrivalDate = secretaryInvoice.GetEffectiveArrivalDate();
            source = EloConstants.InvSourceSecretary;
        }
        else
        {
            arrivalDate = document.DateOfArrival;
            source = EloConstants.InvSourcePartner;
        }

        string isAdvanceInvoice = "Nem";
        if (document is PartnerDocumentUpload partnerInvoice)
        {
            isAdvanceInvoice = partnerInvoice.Type == AccountTypeEnum.AdvanceInvoice ? "Igen" : "Nem";
        }

        var companyId = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.CompanyId);
        if (string.IsNullOrEmpty(companyId))
        {
            companyId = EloConstants.DefaultCompanyId;
        }

        var spIdentifier =
            validationResult.ProcessingSource.SpIdentifier
            ?? throw new EloProcessingException(validationResult.ProcessingSourceId.ToString(), "SpIdentifier is null");

        var documentData = new EloCreateEntityDataModel
        {
            // Primary invoice data
            CompanyId = companyId,
            BankAccountId = invoiceValidationJson.GetFieldValueOnly(ELOFieldNames.BankAccountId) ?? string.Empty,
            BankAccountNo = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.BankAccountNo) ?? string.Empty,
            PartnerId = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.PartnerId),
            InvoiceId = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.InvoiceId) ?? string.Empty,
            InvoiceDate = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.InvoiceDate) ?? string.Empty,
            FulfilmentDate = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.FulfilmentDate) ?? string.Empty,
            DueDate = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.DueDate) ?? string.Empty,
            ArrivalDate = arrivalDate?.ToString("yyyyMMdd") ?? string.Empty,
            Currency = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.Currency) ?? string.Empty,
            GrossAmount = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.InvoiceTotalAmount) ?? string.Empty,
            ServiceStartDate =
                invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.ServiceStartDate) ?? string.Empty,
            ServiceEndDate = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.ServiceEndDate) ?? string.Empty,
            IsAdvanceInvoice = isAdvanceInvoice,
            SpIdentifier = spIdentifier,
            Comment = document.Comment ?? string.Empty,
            RelatedCompletionCerts = await _inScopeRetrieverService.ExtractRelatedCompletionCerts(validationResult),
            ExtId = $"{document.Id}_{validationResult.ProcessingSourceId}",
            IsFoundInNav = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.FoundInNav) == "true",
            VendorTaxNumber = invoiceValidationJson.GetFieldOneOfValues(ELOFieldNames.VendorTaxId) ?? string.Empty,
            Source = source,
            ValidationFields = InvoiceEloFieldBuilder.Build(invoiceValidationJson),
            RelatedProjectId = "",
        };
        documentData.CompCertValidationFields = CompCertEloFieldBuilder.Build(documentData.RelatedCompletionCerts);
        SetRelatedProjectId(documentData, document);

        return documentData;
    }

    private static void ValidateDocumentForEloProcessing(DocumentValidationResult validationResult)
    {
        if (validationResult.Status != DocumentValidationStatusEnum.Success)
        {
            throw new EloProcessingException(
                validationResult.ProcessingSourceId.ToString(),
                $"Cannot create ELO entity because validation failed with status {validationResult.Status}"
            );
        }

        if (validationResult.ValidationResultJson == null)
        {
            throw new EloProcessingException(
                validationResult.ProcessingSourceId.ToString(),
                "Validation result JSON is null"
            );
        }

        var invoice = validationResult.DocumentUpload;
        if (invoice is SecretaryDocumentUpload secretaryInvoice)
        {
            if (secretaryInvoice.GetEffectiveArrivalDate() == null)
            {
                throw new EloProcessingException(
                    validationResult.ProcessingSourceId.ToString(),
                    "Arrival date is missing"
                );
            }
        }
        else
        {
            if (invoice.DateOfArrival == null)
            {
                throw new EloProcessingException(
                    validationResult.ProcessingSourceId.ToString(),
                    "Arrival date is missing"
                );
            }
        }
    }

    private static void SetRelatedProjectId(EloCreateEntityDataModel createEntityData, DocumentUpload? documentUpload)
    {
        string? workNumberFromTig = null;
        bool isRelatedProjectIdMatchingBc = false;
        if (createEntityData.RelatedCompletionCerts.Count > 0)
        {
            foreach (var completionCert in createEntityData.RelatedCompletionCerts)
            {
                var validationData = completionCert.ValidationData;
                var aiValue = validationData.GetFieldAiValue(CompletionCertAiFieldNames.WorkNumber);
                var status = validationData.GetFieldStatus(CompletionCertAiFieldNames.WorkNumber);
                var bcValue = validationData.GetFieldBcValue(CompletionCertAiFieldNames.WorkNumber);
                if (!string.IsNullOrEmpty(aiValue))
                {
                    workNumberFromTig = aiValue;
                    isRelatedProjectIdMatchingBc = (
                        status == ColumnStatus.Ok.ToString() && !string.IsNullOrEmpty(bcValue)
                    );
                    break;
                }
            }
        }

        if (workNumberFromTig != null)
        {
            createEntityData.RelatedProjectId = workNumberFromTig;
            createEntityData.IsRelatedProjectIdMatchingBc = isRelatedProjectIdMatchingBc;
        }
        else
        {
            createEntityData.RelatedProjectId = documentUpload?.ProjectNo ?? string.Empty;
        }
    }

    private static List<ValidationJson> ExtractValidationData(string validationResultJson)
    {
        return JsonSerializer.Deserialize<List<ValidationJson>>(validationResultJson)
            ?? throw new InvalidOperationException("Validation result JSON is null");
    }

    private async Task<DocumentValidationResult> LoadValidationResult(DocumentJobInputData jobInputData)
    {
        var validationResult = await _context
            .DocumentValidationResults.Include(x => x.DocumentUpload)
            .ThenInclude(x => x.AttachmentDocumentTypes)
            .ThenInclude(x => x.Attachment)
            .Include(x => x.ProcessingSource)
            .Include(x => x.ProcessingSource.Attachment)
            .AsSplitQuery()
            .FirstOrDefaultAsync(dv =>
                dv.DocumentUploadId == jobInputData.DocumentUploadId && dv.ProcessingSourceId == jobInputData.ItemId
            );

        if (validationResult == null)
        {
            _logger.LogWarning(
                "No document validation results found for document upload {ItemId}",
                jobInputData.ItemId!
            );
            throw new EloProcessingException(
                jobInputData.ItemId.ToString()!,
                "No document validation results found for document upload"
            );
        }

        return validationResult;
    }
}

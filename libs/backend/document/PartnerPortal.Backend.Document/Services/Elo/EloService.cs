using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.Elo.Builders;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.MarketEloApiClient;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.Elo;

public interface IEloService
{
    Task<JobOperationResult> ProcessForElo(DocumentJobInputData jobInputData);
}

public class EloService : IEloService
{
    private readonly MarketEloClient _marketEloClient;
    private readonly ILogger<EloService> _logger;
    private readonly UserDbContext _context;
    private readonly IEloInvoicePreparationService _eloInvoicePreparationService;
    private readonly IDocumentMergePlannerService _documentMergePlannerService;

    public EloService(
        MarketEloClient marketEloClient,
        ILogger<EloService> logger,
        UserDbContext dbContext,
        IEloInvoicePreparationService eloInvoicePreparationService,
        IDocumentMergePlannerService documentMergePlannerService
    )
    {
        _marketEloClient = marketEloClient;
        _logger = logger;
        _context = dbContext;
        _eloInvoicePreparationService = eloInvoicePreparationService;
        _documentMergePlannerService = documentMergePlannerService;
    }

    public async Task<JobOperationResult> ProcessForElo(DocumentJobInputData jobInputData)
    {
        DocumentEloResult? eloResult = null;
        try
        {
            // 1. Load ELO result
            eloResult = await LoadEloResult(jobInputData);

            // 2. Prepare document data
            var documentData = await _eloInvoicePreparationService.PrepareAsync(jobInputData);

            // 3. Duplication check in ELO
            var searchResult = await SearchEntity(documentData, eloResult);

            // 4. Build ELO request data
            var createEntityRequest = CreateEntityRequestBuilder.Build(documentData, searchResult);

            // 5. Create ELO entity
            var response = await CreateEntityAsync(createEntityRequest, eloResult);

            var mergedDocumentFilePath = await _documentMergePlannerService.MergeDocuments(eloResult);

            // 6. Upload file to ELO
            var uploadFileResponse = await UploadFileAsync(
                response,
                eloResult,
                mergedDocumentFilePath,
                documentData.CompanyId
            );

            // 6. Start workflow
            await StartWorkflow(response, eloResult, documentData.CompanyId);
            await SetDocumentEloStatus(eloResult, DocumentEloStatusEnum.Success);
            return new JobOperationResult { Status = JobOperationStatus.Success };
        }
        catch (EloApiException ex)
        {
            _logger.LogError(
                ex,
                "ELO API error for document upload {DocumentUploadId}: {Message}",
                jobInputData.ItemId,
                ex.Message
            );
            await SetDocumentEloStatus(eloResult!, DocumentEloStatusEnum.Failed, ex.ToString());
            return new JobOperationResult { Status = JobOperationStatus.Fatal };
        }
        catch (EloProcessingException ex)
        {
            _logger.LogError(
                ex,
                "ELO processing error for document upload {DocumentUploadId}: {Message}",
                jobInputData.ItemId,
                ex.Message
            );
            await SetDocumentEloStatus(eloResult!, DocumentEloStatusEnum.Failed, ex.ToString());
            return new JobOperationResult { Status = JobOperationStatus.Fatal };
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Fatal error processing document job {DocumentUploadId}: {Message}",
                jobInputData.ItemId,
                ex.Message
            );
            if (eloResult != null)
            {
                await SetDocumentEloStatus(eloResult, DocumentEloStatusEnum.Failed, ex.ToString());
            }
            return new JobOperationResult { Status = JobOperationStatus.Fatal };
        }
    }

    private async Task<DocumentEloResult> LoadEloResult(DocumentJobInputData jobInputData)
    {
        var eloResult = await _context
            .DocumentEloResults.Include(x => x.ProcessingSource)
            .ThenInclude(x => x.Attachment)
            .FirstOrDefaultAsync(elo =>
                elo.DocumentUploadId == jobInputData.DocumentUploadId && elo.ProcessingSourceId == jobInputData.ItemId
            );

        if (eloResult == null)
        {
            _logger.LogWarning(
                "No ELO results found for document upload {DocumentUploadId}",
                jobInputData.DocumentUploadId
            );
            throw new EloProcessingException(
                jobInputData.DocumentUploadId.ToString(),
                "No ELO results found for document upload"
            );
        }

        if (eloResult.ProcessingSource == null)
        {
            _logger.LogWarning(
                "ProcessingSource is null for document upload {DocumentUploadId}",
                jobInputData.DocumentUploadId
            );
            throw new EloProcessingException(
                jobInputData.DocumentUploadId.ToString(),
                "ProcessingSource is null for document upload"
            );
        }

        return eloResult;
    }

    public async Task<CreateEntityResponse> CreateEntityAsync(CreateEntityRequest request, DocumentEloResult eloResult)
    {
        var (response, requestXml, responseXml) = await _marketEloClient.CreateEntityAsync(request);
        eloResult.CreateRequestXml = requestXml;
        eloResult.CreateResponseXml = responseXml;
        eloResult.EloRegId = response.identifiers.eloRegId;
        eloResult.EloGuid = response.identifiers.eloGuid;
        await _context.SaveChangesAsync();

        _logger.LogInformation("ELO entity created with ID: {EloRegId}", response.identifiers.eloRegId);

        if (response.response.code != 0)
        {
            _logger.LogWarning("ELO creation failed with code: {Code}", response.response.code);
            throw new EloApiException($"ELO creation failed with code: {response.response.code}");
        }

        return response;
    }

    public async Task<UploadFileResponse> UploadFileAsync(
        CreateEntityResponse createEntityResponse,
        DocumentEloResult eloResult,
        string mergedDocumentFilePath,
        string? companyId
    )
    {
        _logger.LogDebug(
            "Uploading file to ELO for entity {EloRegId} for document upload {DocumentUploadId}",
            eloResult.EloRegId,
            eloResult.DocumentUploadId
        );

        var uploadFileRequest = new UploadFileRequest
        {
            companyId = companyId,
            entityType = EloConstants.CaseType,
            user = EloConstants.EloUser,
            systemId = EloConstants.SystemId,
            timeStamp = DateTime.Now.ToString("yyyyMMddHHmmss"),
            identifiers = new identifiers
            {
                eloGuid = createEntityResponse.identifiers.eloGuid,
                eloRegId = createEntityResponse.identifiers.eloRegId,
                bcId = createEntityResponse.identifiers.bcId,
                bcRegId = createEntityResponse.identifiers.bcRegId,
            },

            file = new fileData
            {
                path = mergedDocumentFilePath,
                mime = "application/pdf",
                type = EloConstants.IkDocType,
            },
        };

        var (response, requestXml, responseXml) = await _marketEloClient.UploadFileAsync(uploadFileRequest);

        eloResult.UploadRequestXml = requestXml;
        eloResult.UploadResponseXml = responseXml;
        eloResult.FileId = response.fileId;

        await _context.SaveChangesAsync();

        if (response.response.code != 0)
        {
            _logger.LogWarning("ELO upload failed with code: {Code}", response.response.code);
            throw new EloApiException($"ELO upload failed with code: {response.response.code}");
        }

        _logger.LogInformation("ELO upload successful with file ID: {FileId}", response.fileId);

        return response;
    }

    private async Task<entityData?> SearchEntity(EloCreateEntityDataModel documentData, DocumentEloResult eloResult)
    {
        if (
            string.IsNullOrEmpty(documentData.InvoiceId)
            || string.IsNullOrEmpty(documentData.InvoiceDate)
            || string.IsNullOrEmpty(documentData.PartnerId)
        )
        {
            _logger.LogInformation(
                "Skipping ELO search for document upload {DocumentUploadId}/{ProcessingSourceId} because invoice ID, invoice date, or partner ID is missing",
                eloResult.DocumentUploadId,
                eloResult.ProcessingSourceId
            );
            return null;
        }

        var searchRequest = new SearchEntityRequest
        {
            systemId = EloConstants.SystemId,
            user = EloConstants.EloUser,
            companyId = documentData.CompanyId,
            timeStamp = DateTime.Now.ToString("yyyyMMddHHmmss"),
            entityType = EloConstants.CaseType,
            filter = new searchFilter
            {
                fields =
                [
                    new searchEntityField { key = "INV_ID", value = documentData.InvoiceId },
                    new searchEntityField { key = "INV_DATE", value = documentData.InvoiceDate },
                    new searchEntityField { key = "PARTNER_ID", value = documentData.PartnerId },
                ],
            },
        };
        var (response, requestXml, responseXml) = await _marketEloClient.SearchEntityAsync(searchRequest);

        eloResult.SearchRequestXml = requestXml;
        eloResult.SearchResponseXml = responseXml;
        await _context.SaveChangesAsync();

        if (response.response.code != 0)
        {
            _logger.LogWarning("ELO search failed with code: {Code}", response.response.code);
            throw new EloApiException($"ELO search failed with code: {response.response.code}");
        }

        _logger.LogInformation("ELO search successful with {Count} results", response.resultList.Length);
        return response.resultList.FirstOrDefault()?.entityData;
    }

    private async Task StartWorkflow(
        CreateEntityResponse createEntityResponse,
        DocumentEloResult eloResult,
        string? companyId
    )
    {
        _logger.LogDebug("Starting workflow for entity {EloRegId}", createEntityResponse.identifiers.eloRegId);

        var workflowRequest = new StartWorkflowRequest
        {
            systemId = EloConstants.SystemId,
            user = EloConstants.EloUser,
            companyId = companyId,
            timeStamp = DateTime.Now.ToString("yyyyMMddHHmmss"),
            entityType = EloConstants.CaseType,
            workflowTemplate = EloConstants.WorkflowTemplate,
            identifiers = new identifiers
            {
                eloGuid = createEntityResponse.identifiers.eloGuid,
                eloRegId = createEntityResponse.identifiers.eloRegId,
                bcId = createEntityResponse.identifiers.bcId,
                bcRegId = createEntityResponse.identifiers.bcRegId,
            },
        };

        var (response, requestXml, responseXml) = await _marketEloClient.StartWorkflowAsync(workflowRequest);

        eloResult.StartWorkflowRequestXml = requestXml;
        eloResult.StartWorkflowResponseXml = responseXml;
        eloResult.WorkflowId = response.workflowId;
        await _context.SaveChangesAsync();

        if (response.response.code != 0)
        {
            _logger.LogWarning("ELO workflow start failed with code: {Code}", response.response.code);
            throw new EloApiException($"ELO workflow start failed with code: {response.response.code}");
        }

        _logger.LogInformation("ELO workflow started with ID: {WorkflowId}", response.workflowId);
    }

    private async Task SetDocumentEloStatus(
        DocumentEloResult documentEloResult,
        DocumentEloStatusEnum status,
        string? errorMessage = null,
        CancellationToken cancellationToken = default
    )
    {
        documentEloResult.Status = status;
        documentEloResult.SetErrorMessage(errorMessage);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Document ELO {DocumentEloResultId} status updated to {Status}",
            documentEloResult.Id,
            status
        );
    }
}

using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Shared.MarketEloApiClient;

namespace PartnerPortal.Backend.Document.Services.Elo.Builders;

public static class CreateEntityRequestBuilder
{
    static entityDataField F(string key, string value, string type = "string") =>
        new()
        {
            key = key,
            value = value ?? "",
            type = type,
            rowIndex = 0,
        };

    public static CreateEntityRequest Build(EloCreateEntityDataModel model, entityData? searchResult)
    {
        var request = new CreateEntityRequest
        {
            companyId = model.CompanyId,
            entityType = EloConstants.CaseType,
            user = EloConstants.EloUser,
            systemId = EloConstants.SystemId,
            timeStamp = DateTime.Now.ToString("yyyyMMddHHmmss"),

            // Create identifiers
            identifiers = new identifiers { extId = model.ExtId },

            // Create entity data with fields
            entityData = new entityData
            {
                partnerId = model.PartnerId,
                fields =
                [
                    F("INV_BARCODE", model.SpIdentifier),
                    F("IK_CASE_SUBJECT", model.SpIdentifier),
                    F("INV_ID", model.InvoiceId),
                    F("INV_DATE", model.InvoiceDate),
                    F("INV_DELIVDATE", model.FulfilmentDate),
                    F("INV_DUEDATE", model.DueDate),
                    F("INV_INDATE", model.ArrivalDate),
                    F("INV_CURRENCY", model.Currency),
                    F("INV_GROSSAMOUNT", model.GrossAmount, "number"),
                    F("INV_BANKACCID", model.BankAccountId),
                    F("INV_BANKACCNO", model.BankAccountNo),
                    F("INV_FORMAT", EloConstants.InvoiceFormat),
                    F("INV_DELIVSTARTDATE", model.ServiceStartDate),
                    F("INV_DELIVENDDATE", model.ServiceEndDate),
                    F("INV_ADV_INV", model.IsAdvanceInvoice),
                    F("INV_PARTNER_COMMENT", model.Comment),
                    F("INV_SOURCE", model.Source),
                ],
            },
        };

        if (model.IsFoundInNav)
        {
            request.entityData.fields =
            [
                .. request.entityData.fields,
                F("MON_NAV_INVNO", model.InvoiceId),
                F("MON_NAV_TAXNO", model.VendorTaxNumber),
                F("MON_NAV_INVDATE", model.InvoiceDate),
            ];
        }

        // TIG-es számla esetén töltjük
        if (model.RelatedCompletionCerts.Count > 0 && model.IsRelatedProjectIdMatchingBc)
        {
            request.entityData.projectId = model.RelatedProjectId;
        }
        else
        {
            request.entityData.fields = [.. request.entityData.fields, F("ERP_PRJ_DIM", model.RelatedProjectId)];
        }

        // Add search result fields
        if (searchResult != null)
        {
            request.entityData.fields = [.. request.entityData.fields, .. AddSearchResultFields(searchResult)];
        }
        // Add validation fields
        request.entityData.fields = [.. request.entityData.fields, .. model.ValidationFields];
        // Add related completion certificate fields
        request.entityData.fields = [.. request.entityData.fields, .. model.CompCertValidationFields];

        SetRowIndex(request.entityData.fields);

        return request;
    }

    private static void SetRowIndex(entityDataField[] fields)
    {
        int currentFieldIndex = 0;
        int currentMonRowIndex = 1; // start from 1 ELO requires this

        while (currentFieldIndex < fields.Length)
        {
            // Special handling for MON_FIELD_* fields which come in groups of 3
            if (
                currentFieldIndex + 2 < fields.Length
                && fields[currentFieldIndex].key == "MON_FIELD_ID"
                && fields[currentFieldIndex + 1].key == "MON_FIELD_COMMENT"
                && fields[currentFieldIndex + 2].key == "MON_FIELD_STATUS"
            )
            {
                // Set the same rowIndex for all three related fields
                fields[currentFieldIndex].rowIndex = currentMonRowIndex;
                fields[currentFieldIndex + 1].rowIndex = currentMonRowIndex;
                fields[currentFieldIndex + 2].rowIndex = currentMonRowIndex;

                // Move to the next group
                currentFieldIndex += 3;
                currentMonRowIndex++;
            }
            else
            {
                // For other fields (non-MON fields) do nothing
                currentFieldIndex++;
            }
        }
    }

    private static entityDataField[] AddSearchResultFields(entityData? searchResult)
    {
        if (searchResult == null)
        {
            return [];
        }

        var w = new MonFieldWriter();
        var iK_CASE_SUBJECT = searchResult.fields.FirstOrDefault(f => f.key == "IK_CASE_SUBJECT");
        if (iK_CASE_SUBJECT != null)
        {
            w.Add(
                "INV_ID",
                string.Format(
                    "Duplikáció észlelve: a három kulcsmező alapján a rendszer korábban már rögzített üggyel egyezést talált: {0}",
                    iK_CASE_SUBJECT.value
                ),
                ColumnStatus.Info.ToString()
            );
        }

        var iNV_WFSTATUS = searchResult.fields.FirstOrDefault(f => f.key == "INV_WFSTATUS");
        if (
            iNV_WFSTATUS != null
            && iK_CASE_SUBJECT != null
            && (iNV_WFSTATUS.value == EloConstants.InvWfStatus68 || iNV_WFSTATUS.value == EloConstants.InvWfStatus70)
        )
        {
            w.Add(
                "INV_ID",
                string.Format(
                    "A korábbi ügy azonosítója: {0}. Ügy aktuális státusza: {1}",
                    iK_CASE_SUBJECT.value,
                    iNV_WFSTATUS.value
                ),
                ColumnStatus.Warning.ToString()
            );
        }

        return w.Build();
    }
}

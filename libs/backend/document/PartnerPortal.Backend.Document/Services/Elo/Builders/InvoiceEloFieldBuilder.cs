using System.Text.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Extensions;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Shared.MarketEloApiClient;

namespace PartnerPortal.Backend.Document.Services.Elo.Builders;

public static class InvoiceEloFieldBuilder
{
    public static entityDataField[] Build(List<ValidationJson> validationData)
    {
        var w = new MonFieldWriter();

        // Pre-generated warnings/info
        w.AddRange(GenerateCustomerNotFoundInCustomerCodesFields(validationData));
        w.AddRange(NotWireTransferWarningFields(validationData));
        // Vevő adószáma - ORG_COMPANY
        w.Add(
            "ORG_COMPANY",
            string.Format(
                "Vevő adószáma: számlaképen: '{0}', BC-ben: '{1}'",
                validationData.GetFieldAiValue(ELOFieldNames.CustomerTaxId),
                validationData.GetFieldBcValue(ELOFieldNames.CustomerTaxId)
            ),
            validationData.GetFieldStatus(ELOFieldNames.CustomerTaxId) ?? ""
        );

        // Vevő neve - ORG_COMPANY
        w.Add(
            "ORG_COMPANY",
            string.Format(
                "Vevő neve: számlaképen: '{0}', BC-ben: '{1}'",
                validationData.GetFieldAiValue(ELOFieldNames.CustomerName),
                validationData.GetFieldBcValue(ELOFieldNames.CustomerName)
            ),
            validationData.GetFieldStatus(ELOFieldNames.CustomerName) ?? ""
        );

        // Irányítószám - ORG_COMPANY
        w.Add(
            "ORG_COMPANY",
            string.Format(
                "Irányítószám: számlaképen: '{0}', BC-ben: '{1}'",
                validationData.GetFieldAiValue(ELOFieldNames.PostalCode),
                validationData.GetFieldBcValue(ELOFieldNames.PostalCode)
            ),
            validationData.GetFieldStatus(ELOFieldNames.PostalCode) ?? ""
        );

        // Város - ORG_COMPANY
        w.Add(
            "ORG_COMPANY",
            string.Format(
                "Város: számlaképen: '{0}', BC-ben: '{1}'",
                validationData.GetFieldAiValue(ELOFieldNames.City),
                validationData.GetFieldBcValue(ELOFieldNames.City)
            ),
            validationData.GetFieldStatus(ELOFieldNames.City) ?? ""
        );

        // Cím - ORG_COMPANY
        w.Add(
            "ORG_COMPANY",
            string.Format(
                "Cím: számlaképen: '{0}', BC-ben: '{1}'",
                validationData.GetFieldAiValue(ELOFieldNames.StreetAddress),
                validationData.GetFieldBcValue(ELOFieldNames.StreetAddress)
            ),
            validationData.GetFieldStatus(ELOFieldNames.StreetAddress) ?? ""
        );

        // Partner azonosító - PARTNER_ID
        w.Add(
            "PARTNER_ID",
            string.Format("Partner azonosító: '{0}'", validationData.GetFieldBcValue(ELOFieldNames.PartnerId)),
            validationData.GetFieldStatus(ELOFieldNames.PartnerId) ?? ""
        );

        // Partner adószáma
        w.Add(
            "PARTNER_ID",
            string.Format(
                "Partner adószáma: számlaképen: '{0}', BC-ben: '{1}'",
                validationData.GetFieldAiValue(ELOFieldNames.VendorTaxId),
                validationData.GetFieldBcValue(ELOFieldNames.VendorTaxId)
            ),
            validationData.GetFieldStatus(ELOFieldNames.VendorTaxId) ?? ""
        );

        // Számla sorszám - INV_ID
        w.Add(
            "INV_ID",
            string.Format("Számla sorszám: '{0}'", validationData.GetFieldAiValue(ELOFieldNames.InvoiceId)),
            validationData.GetFieldStatus(ELOFieldNames.InvoiceId) ?? ""
        );

        // Gyűjtőszámla - INV_ID (Collection Invoice)
        w.AddRange(GenerateCollectionInvoiceFields(validationData));

        // Számla kelte - INV_DATE
        w.Add(
            "INV_DATE",
            string.Format("Számla kelte: '{0}'", validationData.GetFieldAiValue(ELOFieldNames.InvoiceDate)),
            validationData.GetFieldStatus(ELOFieldNames.InvoiceDate) ?? ""
        );

        // Teljesítés dátuma - INV_DELIVDATE
        w.Add(
            "INV_DELIVDATE",
            string.Format("Teljesítés dátuma: '{0}'", validationData.GetFieldAiValue(ELOFieldNames.FulfilmentDate)),
            validationData.GetFieldStatus(ELOFieldNames.FulfilmentDate) ?? ""
        );

        // Calculated/derived validations around fulfilment date
        w.AddRange(GenerateFulfilmentDateAfterIssueDateFields(validationData));
        w.AddRange(GenerateCalculatedPerformanceDateMatchEntityDataFields(validationData));
        w.AddRange(GenerateInvoiceFulfilmentDateToCompletionCertExecutionDateFields(validationData));

        // Fizetési határidő - INV_DUEDATE
        w.Add(
            "INV_DUEDATE",
            string.Format("Fizetési határidő: '{0}'", validationData.GetFieldAiValue(ELOFieldNames.DueDate)),
            validationData.GetFieldStatus(ELOFieldNames.DueDate) ?? ""
        );

        // Pénznem - INV_CURRENCY
        w.Add(
            "INV_CURRENCY",
            string.Format("Pénznem: '{0}'", validationData.GetFieldAiValue(ELOFieldNames.Currency)),
            validationData.GetFieldStatus(ELOFieldNames.Currency) ?? ""
        );

        // Bruttó összeg - INV_GROSSAMOUNT
        w.Add(
            "INV_GROSSAMOUNT",
            string.Format("Bruttó összeg: '{0}'", validationData.GetFieldAiValue(ELOFieldNames.InvoiceTotalAmount)),
            validationData.GetFieldStatus(ELOFieldNames.InvoiceTotalAmount) ?? ""
        );

        w.AddRange(GenerateInvoiceGrossAmountToCompletionCertFields(validationData));

        // Bankszámlaszám - INV_BANKACCNO
        w.Add(
            "INV_BANKACCNO",
            string.Format(
                "Bankszámlaszám: számlaképen: '{0}', BC-ben: '{1}'",
                validationData.GetFieldAiValue(ELOFieldNames.BankAccountNo),
                validationData.GetFieldBcValue(ELOFieldNames.BankAccountNo)
            ),
            validationData.GetFieldStatus(ELOFieldNames.BankAccountNo) ?? ""
        );

        // Elszámolási időszak kezdete - INV_DELIVSTARTDATE
        w.Add(
            "INV_DELIVSTARTDATE",
            string.Format(
                "Elszámolási időszak kezdete: '{0}'",
                validationData.GetFieldOneOfValues(ELOFieldNames.ServiceStartDate)
            ),
            validationData.GetFieldStatus(ELOFieldNames.ServiceStartDate) ?? ""
        );

        // Elszámolási időszak vége - INV_DELIVENDDATE
        w.Add(
            "INV_DELIVENDDATE",
            string.Format(
                "Elszámolási időszak vége: '{0}'",
                validationData.GetFieldOneOfValues(ELOFieldNames.ServiceEndDate)
            ),
            validationData.GetFieldStatus(ELOFieldNames.ServiceEndDate) ?? ""
        );

        // Adókulcs - MON_NAV_INVNO
        w.Add(
            "MON_NAV_INVNO",
            string.Format("Adókulcs: '{0}'", validationData.GetFieldOneOfValues(ELOFieldNames.TaxRate)),
            validationData.GetFieldStatus(ELOFieldNames.TaxRate) ?? ""
        );

        w.AddRange(GenerateTigInvoiceVatRateAgainstContractNotMatchFields(validationData));

        // Legal requirements validation
        w.AddRange(GenerateLegalInfoFields(validationData));

        return w.Build();
    }

    private static entityDataField[] GenerateLegalInfoFields(List<ValidationJson> validationData)
    {
        var w = new MonFieldWriter();
        var validationResult = validationData.GetFieldValueOnly(ELOFieldNames.LegalInfo);
        var results = JsonSerializer.Deserialize<List<PatternMatchResult>>(validationResult ?? "[]");
        if (results != null && results.Count > 0)
        {
            foreach (var result in results)
            {
                foreach (var match in result.Matches)
                {
                    var foundText = !string.IsNullOrEmpty(match.ExtractedContext)
                        ? match.ExtractedContext
                        : match.MatchedText;

                    w.Add(
                        "MON_NAV_INVNO",
                        string.Format(
                            "Törvényi előírások ellenőrzése alapján a számlán az alábbi releváns szövegrészek megtalálhatók: {0}",
                            foundText
                        ),
                        ColumnStatus.Info.ToString()
                    );
                }
            }
        }
        return w.Build();
    }

    private static entityDataField[] GenerateTigInvoiceVatRateAgainstContractNotMatchFields(
        List<ValidationJson> validationData
    )
    {
        var w = new MonFieldWriter();
        var validationResult = validationData.GetFieldValueOnly(ELOFieldNames.TigInvoiceVatRateAgainstContractNotMatch);
        var results = JsonSerializer.Deserialize<List<TigInvoiceVatRateAgainstContractNotMatchResult>>(
            validationResult ?? "[]"
        );
        if (results != null)
        {
            foreach (var result in results)
            {
                w.Add(
                    "MON_NAV_INVNO",
                    string.Format(
                        "Számlán található '{0}' adókulcs eltér a BC-ben '{1}' rögzítettől a(z) {2} TIG-hez tartozó szerződésnél.",
                        result.InvoiceVatRate,
                        result.ContractVatRate,
                        result.TigId
                    ),
                    ColumnStatus.Discrepancy.ToString()
                );
            }
        }
        return w.Build();
    }

    private static entityDataField[] NotWireTransferWarningFields(List<ValidationJson> validationData)
    {
        var w = new MonFieldWriter();
        var notWireTransferWarning = validationData.GetFieldValueOnly(ELOFieldNames.NotWireTransferWarning);
        if (notWireTransferWarning != null)
        {
            w.Add("INV_PAYMODE_DESC", "A számlán szereplő fizetési mód nem átutalás.", ColumnStatus.Info.ToString());
        }
        return w.Build();
    }

    private static entityDataField[] GenerateCalculatedPerformanceDateMatchEntityDataFields(
        List<ValidationJson> validationData
    )
    {
        var calculatedPerformanceDateMatch = validationData.GetFieldValueOnly(
            ELOFieldNames.CalculatedPerformanceDateMatch
        );
        var w = new MonFieldWriter();
        if (calculatedPerformanceDateMatch != null && calculatedPerformanceDateMatch == "true")
        {
            w.Add(
                "INV_DELIVDATE",
                "Számla teljesítési dátuma megegyezik a teljesítési kalkulátor által számolt dátummal.",
                ColumnStatus.Info.ToString()
            );
        }
        else if (calculatedPerformanceDateMatch != null && calculatedPerformanceDateMatch == "false")
        {
            w.Add(
                "INV_DELIVDATE",
                "Számla teljesítési dátuma NEM egyezik a teljesítési kalkulátor által számolt dátummal.",
                ColumnStatus.Warning.ToString()
            );
        }

        return w.Build();
    }

    private static entityDataField[] GenerateCollectionInvoiceFields(List<ValidationJson> validationData)
    {
        var w = new MonFieldWriter();
        var collectionInvoiceValue = validationData.GetFieldValueOnly(ELOFieldNames.CollectionInvoice);
        if (collectionInvoiceValue == "true")
        {
            w.Add("INV_ID", "A számla gyűjtőszámla.", ColumnStatus.Info.ToString());
        }
        return w.Build();
    }

    private static entityDataField[] GenerateFulfilmentDateAfterIssueDateFields(List<ValidationJson> validationData)
    {
        var fulfilmentDateAfterIssueDate = validationData.GetFieldValueOnly(ELOFieldNames.FulfilmentDateAfterIssueDate);
        var w = new MonFieldWriter();
        if (fulfilmentDateAfterIssueDate != null && fulfilmentDateAfterIssueDate == "true")
        {
            w.Add(
                "INV_DELIVDATE",
                "Számla teljesítési dátuma későbbi, mint a számla kelte.",
                ColumnStatus.Info.ToString()
            );
        }
        else if (fulfilmentDateAfterIssueDate != null && fulfilmentDateAfterIssueDate == "false")
        {
            w.Add(
                "INV_DELIVDATE",
                "Számla teljesítési dátuma NEM későbbi, mint a számla kelte.",
                ColumnStatus.Warning.ToString()
            );
        }

        return w.Build();
    }

    private static entityDataField[] GenerateInvoiceFulfilmentDateToCompletionCertExecutionDateFields(
        List<ValidationJson> validationData
    )
    {
        var w = new MonFieldWriter();
        var validationResult = validationData.GetFieldValueOnly(
            ELOFieldNames.InvoiceFulfilmentDateToCompletionCertExecutionDate
        );
        var results = JsonSerializer.Deserialize<List<InvoiceFulfilmentDateToCompletionCertExecutionDateResult>>(
            validationResult ?? "[]"
        );

        if (results != null)
        {
            foreach (var result in results)
            {
                w.Add(
                    "INV_DELIVDATE",
                    result.IsMatch
                        ? string.Format(
                            "A(z) {0} TIG-en lévő teljesítési dátum megegyezik a számlán lévővel.",
                            result.TigId
                        )
                        : string.Format(
                            "A(z) {0} TIG-en lévő teljesítési dátum nem egyezik a számlán lévővel. Számlán: '{1}' TIG-en: '{2}'",
                            result.TigId,
                            result.InvoiceFulfilmentDate,
                            result.CompletionCertExecutionDate
                        ),
                    result.IsMatch ? ColumnStatus.Ok.ToString() : ColumnStatus.Discrepancy.ToString()
                );
            }
        }

        return w.Build();
    }

    private static entityDataField[] GenerateInvoiceGrossAmountToCompletionCertFields(
        List<ValidationJson> validationData
    )
    {
        var w = new MonFieldWriter();
        var validationResult = validationData.GetFieldValueOnly(
            ELOFieldNames.InvoiceGrossAmountToCompletionCertGrossAmount
        );
        var results = JsonSerializer.Deserialize<List<InvoiceGrossAmountToCompletionCertGrossAmountResult>>(
            validationResult ?? "[]"
        );

        if (results != null)
        {
            foreach (var result in results)
            {
                w.Add(
                    "INV_GROSSAMOUNT",
                    result.IsMatch
                        ? string.Format(
                            "A(z) {0} TIG-en lévő bruttó összeg megegyezik a számlán lévővel.",
                            result.TigId
                        )
                        : string.Format(
                            "A(z) {0} TIG-en lévő bruttó összeg nem egyezik a számlán lévővel. Számlán: '{1}' TIG-en: '{2}'",
                            result.TigId,
                            result.InvoiceGrossAmount,
                            result.CompletionCertGrossAmount
                        ),
                    result.IsMatch ? ColumnStatus.Ok.ToString() : ColumnStatus.Discrepancy.ToString()
                );
            }
        }

        return w.Build();
    }

    private static entityDataField[] GenerateCustomerNotFoundInCustomerCodesFields(List<ValidationJson> validationData)
    {
        var w = new MonFieldWriter();
        var validationResult = validationData.GetFieldStatus(ELOFieldNames.CustomerNotFoundInCustomerCodes);
        if (!string.IsNullOrEmpty(validationResult))
        {
            w.Add("ORG_COMPANY", "A megrendelő nincs benne a 7 céges listában!", validationResult);
        }
        return w.Build();
    }
}

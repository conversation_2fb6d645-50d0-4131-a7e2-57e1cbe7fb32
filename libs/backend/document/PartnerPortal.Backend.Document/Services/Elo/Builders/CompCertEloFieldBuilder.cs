using PartnerPortal.Backend.Document.Extensions;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Shared.MarketEloApiClient;

namespace PartnerPortal.Backend.Document.Services.Elo.Builders;

public static class CompCertEloFieldBuilder
{
    public static entityDataField[] Build(List<EloCompletionCertData> completionCertData)
    {
        var w = new MonFieldWriter();
        foreach (var cert in completionCertData)
        {
            // Special MON_INV_PFM_NO field with provided rowIndex
            w.AddRange(
                [
                    new entityDataField
                    {
                        key = "MON_INV_PFM_NO",
                        value = cert.CompletionCertId,
                        type = "string",
                        rowIndex = cert.RowIndex,
                    },
                ]
            );

            // TIG azonosító
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - BC-ben: {1}, TIG képen: {2}",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldBcValue(CompletionCertAiFieldNames.TigId),
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.TigId)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.TigId) ?? ""
            );

            // Teljesítési időpont
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Teljesítési időpont: BC-ben: {1}, TIG képen: {2}",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldBcValue(CompletionCertAiFieldNames.ExecutionDate),
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.ExecutionDate)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.ExecutionDate) ?? ""
            );

            // BC Rendszerben a TIG státusz
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - BC Rendszerben a TIG státusz: {1}",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldBcValue(CompletionCertAiFieldNames.TigStatus)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.TigStatus) ?? ""
            );

            // BC rendszerben lévő összegek
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - BC rendszerben lévő összegek: {1}, TIG képen: {2}",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldBcValue(CompletionCertAiFieldNames.CurrentGrossAmount),
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.CurrentGrossAmount)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.CurrentGrossAmount) ?? ""
            );

            // Projekt megnevezés
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Projekt megnevezés: BC-ben: {1}, TIG képen: {2}",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldBcValue(CompletionCertAiFieldNames.ProjectName),
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.ProjectName)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.ProjectName) ?? ""
            );

            // Projekt szám
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Projektszám: BC-ben: '{1}', TIG képen: '{2}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldBcValue(CompletionCertAiFieldNames.WorkNumber),
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.WorkNumber)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.WorkNumber) ?? ""
            );

            // Kivitelező Vállalkozó megnevezése
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Kivitelező Vállalkozó megnevezése: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.ContractorName)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.ContractorName) ?? ""
            );

            // Alvállalkozó megnevezése
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Alvállalkozó megnevezése: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.SubContractorName)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.SubContractorName) ?? ""
            );

            // Szerződés összege nettó
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Szerződés összege nettó: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.ContractTotalAmount)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.ContractTotalAmount) ?? ""
            );

            // Szerződés összege nettó deviza
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Szerződés összege nettó deviza: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.ContractTotalCurrencyCode)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.ContractTotalCurrencyCode) ?? ""
            );

            // Pót és elmaradó munkák összesen
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Pót és elmaradó munkák összesen: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.ExtraAndMissedWorksAmount)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.ExtraAndMissedWorksAmount) ?? ""
            );

            // Pót és elmaradó munkák összesen deviza
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Pót és elmaradó munkák összesen deviza: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.ExtraAndMissedWorksCurrencyCode)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.ExtraAndMissedWorksCurrencyCode) ?? ""
            );

            // Aktuális bruttó teljesítési összeg
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Aktuális bruttó teljesítési összeg: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.CurrentGrossAmount)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.CurrentGrossAmount) ?? ""
            );

            // Aktuális bruttó teljesítési összeg deviza
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Aktuális bruttó teljesítési összeg deviza: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.CurrentGrossAmountCurrCode)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.CurrentGrossAmountCurrCode) ?? ""
            );

            // Aktuális nettó teljesítési összeg
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Aktuális nettó teljesítési összeg: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.CurrentNetAmount)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.CurrentNetAmount) ?? ""
            );

            // Aktuális nettó teljesítési összeg deviza
            w.Add(
                "MON_INV_PFM_NO",
                string.Format(
                    "TIG azonosító: {0} - Aktuális nettó teljesítési összeg deviza: '{1}'",
                    cert.CompletionCertId,
                    cert.ValidationData.GetFieldAiValue(CompletionCertAiFieldNames.CurrentNetAmountCurrCode)
                ),
                cert.ValidationData.GetFieldStatus(CompletionCertAiFieldNames.CurrentNetAmountCurrCode) ?? ""
            );
        }

        return w.Build();
    }
}

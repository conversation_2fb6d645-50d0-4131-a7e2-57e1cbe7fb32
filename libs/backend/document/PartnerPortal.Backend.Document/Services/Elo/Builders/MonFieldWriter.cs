using PartnerPortal.Backend.Shared.MarketEloApiClient;

namespace PartnerPortal.Backend.Document.Services.Elo.Builders;

internal sealed class MonFieldWriter
{
    private readonly List<entityDataField> _fields = [];

    public void Add(string monFieldId, string comment, string status)
    {
        _fields.AddRange(
            [
                new entityDataField
                {
                    key = "MON_FIELD_ID",
                    value = monFieldId,
                    type = "string",
                    rowIndex = 0,
                },
                new entityDataField
                {
                    key = "MON_FIELD_COMMENT",
                    value = comment ?? "",
                    type = "string",
                    rowIndex = 0,
                },
                new entityDataField
                {
                    key = "MON_FIELD_STATUS",
                    value = status ?? "",
                    type = "string",
                    rowIndex = 0,
                },
            ]
        );
    }

    public void AddRange(IEnumerable<entityDataField> fields) => _fields.AddRange(fields);

    public entityDataField[] Build() => [.. _fields];
}

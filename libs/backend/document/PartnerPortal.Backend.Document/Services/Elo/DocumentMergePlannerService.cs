using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.Elo;

public sealed class DocumentMergePlan
{
    public sealed class Input
    {
        public required string Path { get; init; }
    }

    public required List<Input> Inputs { get; init; }
    public required string TargetFilePath { get; init; }
}

public interface IDocumentMergePlannerService
{
    Task<string> MergeDocuments(DocumentEloResult eloResult);
}

public class DocumentMergePlannerService : IDocumentMergePlannerService
{
    private readonly ILogger<DocumentMergePlannerService> _logger;
    private readonly UserDbContext _context;
    private readonly string _documentAttachmentPath;
    private readonly string _mergedDocumentsPath;

    public DocumentMergePlannerService(
        ILogger<DocumentMergePlannerService> logger,
        UserDbContext context,
        IConfiguration config
    )
    {
        _logger = logger;
        _context = context;
        _documentAttachmentPath = config.GetConfigValue<string>("DocumentModule:FilesPath");
        _mergedDocumentsPath = config.GetConfigValue<string>("EloConfiguration:MergedDocumentsPath");
    }

    public async Task<string> MergeDocuments(DocumentEloResult eloResult)
    {
        if (
            eloResult.DocumentUpload is SecretaryDocumentUpload secretaryDocumentUpload
            && eloResult.ProcessingSource is DocumentClassification currentClassification
        )
        {
            _logger.LogDebug(
                "Merging documents for secretary document upload {SecretaryDocumentUploadId} for attachment {AttachmentDocumentTypeId}",
                secretaryDocumentUpload.Id,
                currentClassification.AttachmentId
            );

            var classifications = await _context
                .DocumentClassifications.Include(x => x.Attachment)
                .Where(x => x.SecretaryDocumentUploadId == secretaryDocumentUpload.Id)
                .ToListAsync();

            var plan = BuildForSecretary(
                currentClassification,
                classifications,
                secretaryDocumentUpload.UploadType,
                eloResult.DocumentUpload.Id.ToString(),
                eloResult.ProcessingSource.SpIdentifier!
            );

            var inputs = plan.Inputs.Select(i => i.Path).ToList();
            var merged = await PdfHelper.MergeFilesAsync(inputs);
            await PdfHelper.SavePdfToFileAsync(merged, plan.TargetFilePath);

            return plan.TargetFilePath;
        }
        else
        {
            _logger.LogDebug(
                "Merging documents for partner document upload {PartnerDocumentUploadId} for attachment {AttachmentDocumentTypeId}",
                eloResult.DocumentUpload.Id,
                eloResult.ProcessingSource.AttachmentId
            );

            if (eloResult.ProcessingSource is AttachmentDocumentType currentAttachment)
            {
                var plan = BuildForManualMerge(
                    currentAttachment,
                    eloResult.DocumentUpload.AttachmentDocumentTypes,
                    eloResult.DocumentUpload.Id.ToString(),
                    eloResult.ProcessingSource.SpIdentifier!
                );

                var inputs = plan.Inputs.Select(i => i.Path).ToList();
                var merged = await PdfHelper.MergeFilesAsync(inputs);
                await PdfHelper.SavePdfToFileAsync(merged, plan.TargetFilePath);
                return plan.TargetFilePath;
            }

            throw new EloProcessingException(
                eloResult.ProcessingSourceId.ToString(),
                "Unexpected processing source type for document upload"
            );
        }
    }

    private DocumentMergePlan BuildForSecretary(
        DocumentClassification currentClassification,
        IEnumerable<DocumentClassification> allClassifications,
        SecretaryUploadTypeEnum uploadType,
        string documentUploadId,
        string spIdentifier
    )
    {
        // Batch: no merge/split needed, return the original file containing the current invoice
        if (uploadType == SecretaryUploadTypeEnum.Batch)
        {
            var inputs = new List<DocumentMergePlan.Input>
            {
                new()
                {
                    Path = FileUtility.GetFilePath(
                        _documentAttachmentPath,
                        currentClassification.Attachment.Context,
                        currentClassification.Attachment.StoredName
                    ),
                },
            };

            var sanitizedSpBatch = spIdentifier.Replace("/", "");
            var targetBatch = FileUtility.GetFilePath(
                _mergedDocumentsPath,
                documentUploadId,
                $"{sanitizedSpBatch}.pdf"
            );
            return new DocumentMergePlan { Inputs = inputs, TargetFilePath = targetBatch };
        }

        // PerCase: treat similar to Partner, merge whole files: current invoice + all non-invoices with completion certs first
        return BuildForManualMerge(currentClassification, allClassifications, documentUploadId, spIdentifier);
    }

    private DocumentMergePlan BuildForManualMerge(
        DocumentProcessingSource currentAttachment,
        IEnumerable<DocumentProcessingSource> allAttachments,
        string documentUploadId,
        string spIdentifier
    )
    {
        var inputs = new List<DocumentMergePlan.Input>
        {
            // current invoice first
            new()
            {
                Path = FileUtility.GetFilePath(
                    _documentAttachmentPath,
                    currentAttachment.Attachment.Context,
                    currentAttachment.Attachment.StoredName
                ),
            },
        };

        // then all non-invoice documents ordered with completion certs first
        inputs.AddRange(
            allAttachments
                .Where(d => !d.IsInvoiceType)
                .OrderByDescending(d => d.DocumentType == DocumentType.CompletionCert)
                .Select(x => new DocumentMergePlan.Input
                {
                    Path = FileUtility.GetFilePath(
                        _documentAttachmentPath,
                        x.Attachment.Context,
                        x.Attachment.StoredName
                    ),
                })
        );

        var sanitizedSp = spIdentifier.Replace("/", "");
        var target = FileUtility.GetFilePath(_mergedDocumentsPath, documentUploadId, $"{sanitizedSp}.pdf");

        return new DocumentMergePlan { Inputs = inputs, TargetFilePath = target };
    }
}

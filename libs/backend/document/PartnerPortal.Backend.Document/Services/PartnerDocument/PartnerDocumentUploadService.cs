using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Extensions;
using PartnerPortal.Backend.Document.Services.ArrivalDateCalculation;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Document.Services.SpIdentifier;
using PartnerPortal.Backend.Shared.Attachment;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using ContractsFileInfo = PartnerPortal.Backend.Contracts.ContractsApiStub.Models.FileInfo;

namespace PartnerPortal.Backend.Document.Services.PartnerDocument;

public class PartnerDocumentUploadService : IPartnerDocumentUploadService
{
    private readonly UserDbContext _context;

    private readonly ILogger<PartnerDocumentUploadService> _logger;

    private readonly IAttachmentService _attachmentService;

    private readonly ISpIdentifierService _spIdentifierService;

    private readonly IArrivalDateCalculationService _arrivalDateCalculationService;
    private readonly IProcessingManagerService _processingManagerService;

    private readonly IMapper _mapper;
    private readonly IDocumentProcessingStatusService _statusService;

    public PartnerDocumentUploadService(
        UserDbContext context,
        ILogger<PartnerDocumentUploadService> logger,
        IAttachmentService attachmentService,
        ISpIdentifierService spIdentifierService,
        IArrivalDateCalculationService arrivalDateCalculationService,
        IProcessingManagerService processingManagerService,
        IMapper mapper,
        IDocumentProcessingStatusService statusService
    )
    {
        _context = context;
        _logger = logger;
        _attachmentService = attachmentService;
        _spIdentifierService = spIdentifierService;
        _arrivalDateCalculationService = arrivalDateCalculationService;
        _processingManagerService = processingManagerService;
        _mapper = mapper;
        _statusService = statusService;
    }

    public async Task<DocumentUploadDto> CreateDocumentUploadDraft(int userId, CreateDocumentUploadDraftRequest request)
    {
        _logger.LogInformation("Creating document upload draft with context id {ContextId}", request.ContextId);
        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var documentUpload = new PartnerDocumentUpload
            {
                Id = request.ContextId,
                UserId = userId,
                ProjectNo = request.ProjectNo,
                ProjectDescription = request.ProjectDescription,
                ContractNo = request.ContractNo,
                Type = request.Type,
                Status = UploadStatusEnum.Draft,
                Comment = request.Comment,
                AttachmentDocumentTypes = [],
            };
            await documentUpload.AddAttachmentDocumentTypes(_spIdentifierService, request.AttachmentDocumentTypes);

            await _context.DocumentUploads.AddAsync(documentUpload);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Document upload created with id {DocumentUploadId}", documentUpload.Id);

            await CommitFiles(request.AttachmentDocumentTypes, userId, documentUpload.Id);

            await transaction.CommitAsync();

            return _mapper.Map<DocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error creating document upload draft with context id {ContextId}", request.ContextId);
            throw;
        }
    }

    public async Task<DocumentUploadDto> SubmitDocumentUpload(
        int userId,
        Guid documentUploadId,
        UpdateDocumentUploadDraftRequest request
    )
    {
        _logger.LogInformation("Submitting document upload with id {DocumentUploadId}", documentUploadId);
        var documentUpload = await _context
            .DocumentUploads.OfType<PartnerDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .FirstOrDefaultAsync(x => x.Id == documentUploadId && x.UserId == userId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        // Validate that the document is in draft status
        if (documentUpload.Status != UploadStatusEnum.Draft)
        {
            _logger.LogWarning(
                "Cannot submit document upload with id {DocumentUploadId} because it is not in draft status",
                documentUploadId
            );
            throw new ValidationException("Only draft documents can be submitted");
        }

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            await UpdateDocumentUpload(documentUpload, request);

            documentUpload.ValidateSubmit(out var validationError);
            if (validationError != null)
            {
                throw new ValidationException(validationError);
            }

            documentUpload.Status = UploadStatusEnum.Submitted;
            documentUpload.DateOfArrival = await _arrivalDateCalculationService.CalculateDateOfArrival(DateTime.Now);

            await _context.SaveChangesAsync();

            await CommitFiles(request.AttachmentDocumentTypes, documentUpload.UserId, documentUpload.Id);

            var documents = await _processingManagerService.PrepareManualDocuments(documentUpload);
            await _processingManagerService.TriggerDocumentAnalysisJobs(documents);

            await transaction.CommitAsync();

            _logger.LogInformation(
                "Document upload with id {DocumentUploadId} successfully submitted",
                documentUploadId
            );

            return _mapper.Map<DocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error submitting document upload with id {DocumentUploadId}", documentUploadId);
            throw;
        }
    }

    public async Task<DocumentUploadDto> UpdateDocumentUploadDraft(
        int userId,
        Guid documentUploadId,
        UpdateDocumentUploadDraftRequest request
    )
    {
        var documentUpload = await _context
            .DocumentUploads.OfType<PartnerDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .FirstOrDefaultAsync(x => x.Id == documentUploadId && x.UserId == userId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        _logger.LogInformation("Updating document upload with id {DocumentUploadId}", documentUploadId);

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            await UpdateDocumentUpload(documentUpload, request);

            await _context.SaveChangesAsync();

            await CommitFiles(request.AttachmentDocumentTypes, documentUpload.UserId, documentUpload.Id);

            await transaction.CommitAsync();

            _logger.LogInformation("Document upload updated successfully with id {DocumentUploadId}", documentUploadId);

            return _mapper.Map<DocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error updating document upload with id {DocumentUploadId}", documentUploadId);
            throw;
        }
    }

    public async Task<GetDocumentUploadTotalCountsResponse> GetDocumentUploadTotalCounts(int userId, string? projectNo)
    {
        var query = _context.DocumentUploads.Where(x => x.UserId == userId);
        if (!string.IsNullOrWhiteSpace(projectNo))
        {
            query = query.Where(x => x.ProjectNo == projectNo);
        }

        var counts = await query
            .GroupBy(x => x.Status)
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Status, x => x.Count);

        int draftCount = counts.GetValueOrDefault(UploadStatusEnum.Draft, 0);
        int submittedCount = counts.GetValueOrDefault(UploadStatusEnum.Submitted, 0);
        int totalCount = draftCount + submittedCount;

        return new GetDocumentUploadTotalCountsResponse
        {
            Total = totalCount,
            Draft = draftCount,
            Submitted = submittedCount,
        };
    }

    public async Task<GetPaginatedDocumentsResponse> ListDocumentUploads(
        int userId,
        UploadStatusEnum? status,
        string? projectNo,
        int? page,
        int? pageSize,
        string? searchTerm,
        DateTime? fromDate,
        DateTime? toDate,
        SortByEnum? sortBy,
        SortOrderEnum? sortOrder
    )
    {
        int actualPage = page ?? 1;
        int actualPageSize = pageSize ?? 10;
        int skip = (actualPage - 1) * actualPageSize;

        var query = _context.DocumentUploads.OfType<PartnerDocumentUpload>().Where(x => x.UserId == userId);

        if (status.HasValue)
        {
            query = query.Where(x => x.Status == status.Value);
        }

        if (!string.IsNullOrWhiteSpace(projectNo))
        {
            query = query.Where(x => x.ProjectNo == projectNo);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(x =>
                x.Id.ToString().Contains(searchTerm)
                || (x.ProjectNo != null && x.ProjectNo.Contains(searchTerm))
                || (x.ContractNo != null && x.ContractNo.Contains(searchTerm))
                || (x.ProjectDescription != null && x.ProjectDescription.Contains(searchTerm))
            );
        }

        if (fromDate.HasValue)
        {
            query = query.Where(x => x.CreatedDate >= fromDate.Value.Date);
        }

        if (toDate.HasValue)
        {
            // Include the entire day by setting time to end of day
            var endOfDay = toDate.Value.Date.AddDays(1).AddTicks(-1);
            query = query.Where(x => x.CreatedDate <= endOfDay);
        }

        int totalCount = await query.CountAsync();

        // Sorting
        if (sortBy.HasValue)
        {
            query = sortBy.Value switch
            {
                SortByEnum.CreatedDate => sortOrder == SortOrderEnum.Desc
                    ? query.OrderByDescending(x => x.CreatedDate)
                    : query.OrderBy(x => x.CreatedDate),
                _ => query.OrderByDescending(x => x.CreatedDate),
            };
        }
        else
        {
            query = query.OrderByDescending(x => x.CreatedDate);
        }

        var documentUploads = await query
            .Include(i => i.AttachmentDocumentTypes)
            .ThenInclude(x => x.Attachment)
            .Include(x => x.DocumentAnalysisResults)
            .Include(x => x.DocumentValidationResults)
            .Include(x => x.DocumentEloResults)
            .Skip(skip)
            .Take(actualPageSize)
            .AsSplitQuery()
            .AsNoTracking()
            .ToListAsync();

        var results = documentUploads.Select(x => _mapper.Map<DocumentUploadDto>(x)).ToList();
        if (status.HasValue && status.Value == UploadStatusEnum.Submitted)
        {
            var documentLookup = documentUploads.ToDictionary(d => d.Id);
            foreach (var result in results)
            {
                if (documentLookup.TryGetValue(result.Id, out var document))
                {
                    var processingStatus = _statusService.CalculateOverallStatusForPartner(
                        [.. document.DocumentAnalysisResults],
                        [.. document.DocumentValidationResults],
                        [.. document.DocumentEloResults]
                    );

                    result.ProcessingStatus = processingStatus;
                }
                else
                {
                    result.ProcessingStatus = ProcessingStatusEnum.Default;
                }
            }
        }

        return new GetPaginatedDocumentsResponse
        {
            Documents = results,
            TotalCount = totalCount,
            Page = actualPage,
            PageSize = actualPageSize,
        };
    }

    public async Task<DocumentUploadDto> GetDocumentUpload(int userId, Guid documentUploadId)
    {
        var documentUpload = await _context
            .DocumentUploads.OfType<PartnerDocumentUpload>()
            .Include(i => i.AttachmentDocumentTypes)
            .ThenInclude(x => x.Attachment)
            .AsNoTracking()
            .AsSplitQuery()
            .FirstOrDefaultAsync(x => x.Id == documentUploadId && x.UserId == userId);

        if (documentUpload == null)
        {
            _logger.LogWarning("Partner document upload with id {DocumentUploadId} not found", documentUploadId);
            throw new NotFoundException();
        }

        var result = _mapper.Map<DocumentUploadDto>(documentUpload);

        List<DocumentAnalysisResult> analysisResults = [];
        List<DocumentValidationResult> validationResults = [];
        List<DocumentEloResult> eloResults = [];

        foreach (
            var attachmentDocumentType in documentUpload.AttachmentDocumentTypes.Where(x =>
                x.IsInvoiceType || x.DocumentType == DocumentType.CompletionCert
            )
        )
        {
            DocumentEloResult? eloResult = null;
            if (attachmentDocumentType.IsInvoiceType)
            {
                eloResult = await _context
                    .DocumentEloResults.Where(x =>
                        x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                    )
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (eloResult != null)
                {
                    eloResults.Add(eloResult);
                }
            }

            var analysisResult = await _context
                .DocumentAnalysisResults.Where(x =>
                    x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                )
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (analysisResult != null)
            {
                analysisResults.Add(analysisResult);
            }

            var validationResult = await _context
                .DocumentValidationResults.Where(x =>
                    x.DocumentUploadId == documentUpload.Id && x.ProcessingSourceId == attachmentDocumentType.Id
                )
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (validationResult != null)
            {
                validationResults.Add(validationResult);
            }

            var (processingStatus, _) = _statusService.CalculatePerItemProcessingStatus(
                eloResult,
                validationResult,
                analysisResult,
                null
            );

            var resultAttachment = result.AttachmentDocumentTypes.First(x =>
                x.Attachment.FileId == attachmentDocumentType.AttachmentId
            );
            resultAttachment.ProcessingStatus = processingStatus;
        }

        result.ProcessingStatus = _statusService.CalculateOverallStatusForPartner(
            analysisResults,
            validationResults,
            eloResults
        );

        return result;
    }

    public async Task RemoveDocumentUpload(int userId, Guid documentUploadId)
    {
        var documentUpload =
            await _context
                .DocumentUploads.OfType<PartnerDocumentUpload>()
                .FirstOrDefaultAsync(x => x.Id == documentUploadId && x.UserId == userId)
            ?? throw new NotFoundException();

        if (documentUpload.Status != UploadStatusEnum.Draft)
        {
            throw new ValidationException("Only draft documents can be removed");
        }

        _context.DocumentUploads.Remove(documentUpload);
        await _context.SaveChangesAsync();

        await _attachmentService.DeleteFiles(documentUpload.Id.ToString(), documentUpload.UserId.ToString());
    }

    public async Task<DocumentUploadDto> CreateAndSubmitDocument(int userId, CreateDocumentUploadDraftRequest request)
    {
        _logger.LogInformation(
            "Creating and submitting document upload with context id {ContextId}",
            request.ContextId
        );

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var documentUpload = new PartnerDocumentUpload
            {
                Id = request.ContextId,
                UserId = userId,
                ProjectNo = request.ProjectNo,
                ProjectDescription = request.ProjectDescription,
                ContractNo = request.ContractNo,
                Type = request.Type,
                AttachmentDocumentTypes = [],
                Status = UploadStatusEnum.Submitted, // Set status to Submitted directly
                Comment = request.Comment,
            };

            await documentUpload.AddAttachmentDocumentTypes(_spIdentifierService, request.AttachmentDocumentTypes);

            documentUpload.ValidateSubmit(out var validationError);
            if (validationError != null)
            {
                throw new ValidationException(validationError);
            }

            documentUpload.DateOfArrival = await _arrivalDateCalculationService.CalculateDateOfArrival(DateTime.Now);

            await _context.DocumentUploads.AddAsync(documentUpload);
            await _context.SaveChangesAsync();

            _logger.LogInformation(
                "Document upload created and submitted with id {DocumentUploadId}",
                documentUpload.Id
            );

            await CommitFiles(request.AttachmentDocumentTypes, userId, documentUpload.Id);

            var documents = await _processingManagerService.PrepareManualDocuments(documentUpload);
            await _processingManagerService.TriggerDocumentAnalysisJobs(documents);

            await transaction.CommitAsync();

            return _mapper.Map<DocumentUploadDto>(documentUpload);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(
                ex,
                "Error creating and submitting document upload with context id {ContextId}",
                request.ContextId
            );
            throw;
        }
    }

    private async Task UpdateDocumentUpload(
        PartnerDocumentUpload documentUpload,
        UpdateDocumentUploadDraftRequest request
    )
    {
        documentUpload.ProjectNo = request.ProjectNo;
        documentUpload.ProjectDescription = request.ProjectDescription;
        documentUpload.ContractNo = request.ContractNo;
        documentUpload.Type = request.Type;
        documentUpload.Comment = request.Comment;
        documentUpload.RowVersion = request.RowVersion;
        await documentUpload.UpdateManualDocumentAttachments(_spIdentifierService, request.AttachmentDocumentTypes);
    }

    private async Task CommitFiles(
        List<AttachmentDocumentTypeDto> attachmentDocumentTypes,
        int userId,
        Guid documentUploadId
    )
    {
        var fileInfos = attachmentDocumentTypes
            .Select(x => new ContractsFileInfo { FileId = x.Attachment.FileId })
            .ToList();
        await _attachmentService.CommitFiles(documentUploadId.ToString(), userId.ToString(), fileInfos);

        _logger.LogInformation("Document upload files committed with id {DocumentUploadId}", documentUploadId);
    }
}

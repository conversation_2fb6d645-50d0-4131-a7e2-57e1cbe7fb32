using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;

namespace PartnerPortal.Backend.Document.Services.Extractors;

/// <summary>
/// Processes Azure Document Intelligence AnalyzeResult to extract completion certificate data
/// </summary>
public class CompletionCertExtractor : IAnalyzeResultExtractor<CompletionCertificateAiData>
{
    private readonly ILogger<CompletionCertExtractor> _logger;

    public CompletionCertExtractor(ILogger<CompletionCertExtractor> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Extracts completion certificate data from Azure Document Intelligence analysis result
    /// </summary>
    /// <param name="analyzeResult">The result from Azure Document Intelligence</param>
    /// <returns>Extracted completion certificate data</returns>
    public CompletionCertificateAiData ExtractData(AnalyzeResult analyzeResult)
    {
        var result = new CompletionCertificateAiData();
        var confidences = new Dictionary<string, double>();

        try
        {
            if (analyzeResult.Documents?.Count == 0 || analyzeResult.Documents?[0] == null)
            {
                return result;
            }

            var document = analyzeResult.Documents[0];

            result.TigId = ExtractStringFieldContent(nameof(CompletionCertificateAiData.TigId), document, confidences);
            result.ProjectName = ExtractStringFieldContent(
                nameof(CompletionCertificateAiData.ProjectName),
                document,
                confidences
            );
            result.ContractorName = ExtractStringFieldContent(
                nameof(CompletionCertificateAiData.ContractorName),
                document,
                confidences
            );
            result.TigUniqueId = ExtractStringFieldContent(
                nameof(CompletionCertificateAiData.TigUniqueId),
                document,
                confidences
            );
            result.ExecutionDate = ExtractExecutionDate(document, confidences);
            result.SubContractorName = ExtractStringFieldContent(
                nameof(CompletionCertificateAiData.SubContractorName),
                document,
                confidences
            );
            result.WorkNumber = ExtractStringFieldContent(
                nameof(CompletionCertificateAiData.WorkNumber),
                document,
                confidences
            );
            result.AdditionalWorkAfterFinalAcceptance = ExtractAmountAndCurrency(
                nameof(CompletionCertificateAiData.AdditionalWorkAfterFinalAcceptance),
                document,
                confidences
            );
            result.CurrentNetPerformanceValue = ExtractAmountAndCurrency(
                nameof(CompletionCertificateAiData.CurrentNetPerformanceValue),
                document,
                confidences
            );
            result.TaxAmount = ExtractAmountAndCurrency(
                nameof(CompletionCertificateAiData.TaxAmount),
                document,
                confidences
            );
            result.ContractWork = ExtractAmountAndCurrency(
                nameof(CompletionCertificateAiData.ContractWork),
                document,
                confidences
            );
            result.TotalContractAndAdditionalWork = ExtractAmountAndCurrency(
                nameof(CompletionCertificateAiData.TotalContractAndAdditionalWork),
                document,
                confidences
            );
            result.ActualGrossValue = ExtractAmountAndCurrency(
                nameof(CompletionCertificateAiData.ActualGrossValue),
                document,
                confidences
            );

            result.FieldConfidences = confidences;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting completion certificate data from document");
        }

        return result;
    }

    private static DateTime? ExtractExecutionDate(AnalyzedDocument document, Dictionary<string, double> confidences)
    {
        if (
            document.Fields.TryGetValue(nameof(CompletionCertificateAiData.ExecutionDate), out DocumentField? field)
            && field != null
            && field.FieldType == DocumentFieldType.Date
            && field.ValueDate.HasValue
        )
        {
            confidences[nameof(CompletionCertificateAiData.ExecutionDate)] = field.Confidence ?? 0.0;

            return field.ValueDate.Value.DateTime;
        }

        return null;
    }

    private static string? ExtractStringFieldContent(
        string fieldName,
        AnalyzedDocument document,
        Dictionary<string, double> confidences
    )
    {
        if (
            document.Fields.TryGetValue(fieldName, out DocumentField? field)
            && field != null
            && field.FieldType == DocumentFieldType.String
            && !string.IsNullOrWhiteSpace(field.Content)
        )
        {
            confidences[fieldName] = field.Confidence ?? 0.0;
            var value = field.Content.TrimEnd(',', ' ', '\t', '\r', '\n');

            return value;
        }

        return null;
    }

    private static (decimal? Amount, string? Currency) ExtractAmountAndCurrency(
        string fieldName,
        AnalyzedDocument document,
        Dictionary<string, double> confidences
    )
    {
        if (
            document.Fields.TryGetValue(fieldName, out DocumentField? field)
            && field != null
            && field.FieldType == DocumentFieldType.List
            && field.ValueList != null
            && field.ValueList.Count > 1
        )
        {
            confidences[fieldName] = field.Confidence ?? 0.0;

            var objectItem = field.ValueList[1];
            decimal? amount = null;
            string? currency = null;

            if (objectItem?.FieldType == DocumentFieldType.Dictionary && objectItem.ValueDictionary != null)
            {
                var valueObjectDict = objectItem.ValueDictionary;

                if (
                    valueObjectDict.TryGetValue("Amount", out DocumentField? amountField)
                    && amountField != null
                    && amountField.FieldType == DocumentFieldType.String
                    && !string.IsNullOrWhiteSpace(amountField.Content)
                )
                {
                    amount = CurrencyAmountParserUtility.ParseCurrencyAmount(amountField.Content.Trim());
                }

                if (
                    valueObjectDict.TryGetValue("Currency", out DocumentField? currencyField)
                    && currencyField != null
                    && currencyField.FieldType == DocumentFieldType.String
                    && !string.IsNullOrWhiteSpace(currencyField.Content)
                )
                {
                    currency = currencyField.Content.Trim();
                }
            }

            return (amount, currency);
        }

        return (null, null);
    }
}

using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;

namespace PartnerPortal.Backend.Document.Services.Extractors;

/// <summary>
/// Processes Azure Document Intelligence AnalyzeResult to extract invoice data
/// </summary>
public class InvoiceExtractor : IAnalyzeResultExtractor<InvoiceAiData>
{
    private readonly ILogger<InvoiceExtractor> _logger;
    private readonly IPatternMatchingService _patternMatchingService;

    // Field names from Azure Document Intelligence prebuilt invoice model
    private static class AzureFieldNames
    {
        public const string CustomerTaxId = "CustomerTaxId";
        public const string CustomerName = "CustomerName";
        public const string CustomerAddress = "CustomerAddress";
        public const string BillingAddress = "BillingAddress";
        public const string VendorTaxId = "VendorTaxId";
        public const string PaymentTerm = "PaymentTerm";
        public const string InvoiceTotal = "InvoiceTotal";
        public const string InvoiceId = "InvoiceId";
        public const string DueDate = "DueDate";
        public const string SubTotal = "SubTotal";
        public const string TotalTax = "TotalTax";
        public const string ServiceStartDate = "ServiceStartDate";
        public const string ServiceEndDate = "ServiceEndDate";
        public const string PaymentDetails = "PaymentDetails";
        public const string Iban = "IBAN";
        public const string Items = "Items";
        public const string TaxRate = "TaxRate";
        public const string InvoiceDate = "InvoiceDate";
    }

    public InvoiceExtractor(ILogger<InvoiceExtractor> logger, IPatternMatchingService patternMatchingService)
    {
        _logger = logger;
        _patternMatchingService = patternMatchingService;
    }

    public InvoiceAiData ExtractData(AnalyzeResult analyzeResult)
    {
        var result = new InvoiceAiData();

        if (analyzeResult.Documents == null || analyzeResult.Documents.Count == 0)
        {
            _logger.LogWarning("No documents found in AnalyzeResult");
            return result;
        }

        try
        {
            var document = analyzeResult.Documents[0];
            var content = analyzeResult.Content ?? string.Empty;
            var tables = analyzeResult.Tables ?? [];

            result.CustomerName = TryGetStringField(document, AzureFieldNames.CustomerName, result.FieldConfidences);
            result.CustomerTaxNumber = ExtractTaxNumber(
                AzureFieldNames.CustomerTaxId,
                document,
                result.FieldConfidences
            );
            ExtractCustomerAddress(document, result);
            result.VendorTaxId = ExtractTaxNumber(AzureFieldNames.VendorTaxId, document, result.FieldConfidences);
            result.InvoiceId = TryGetStringField(document, AzureFieldNames.InvoiceId, result.FieldConfidences);
            result.PaymentTerm = TryGetStringField(document, AzureFieldNames.PaymentTerm, result.FieldConfidences);
            result.InvoiceDate = TryGetDateField(document, AzureFieldNames.InvoiceDate, result.FieldConfidences);
            result.DueDate = TryGetDateField(document, AzureFieldNames.DueDate, result.FieldConfidences);
            result.ServiceStartDate = TryGetDateField(
                document,
                AzureFieldNames.ServiceStartDate,
                result.FieldConfidences
            );
            result.ServiceEndDate = TryGetDateField(document, AzureFieldNames.ServiceEndDate, result.FieldConfidences);
            result.FulfilmentDate =
                DateExtractionUtility.ExtractDateFromContent(content, DateType.FulfilmentDate)
                ?? DateExtractionUtility.ExtractDateFromTables(tables, DateType.FulfilmentDate);

            result.InvoiceTotal = ExtractCurrencyField(document, AzureFieldNames.InvoiceTotal, result.FieldConfidences);
            result.SubTotal = ExtractCurrencyField(document, AzureFieldNames.SubTotal, result.FieldConfidences);
            result.TotalTax = ExtractCurrencyField(document, AzureFieldNames.TotalTax, result.FieldConfidences);
            ExtractTaxRateAndCurrency(document, result);
            ExtractBankAccounts(document, content, result);
            result.IsCollectionInvoice = DetectCollectionInvoice(content);
            result.PatternMatches = _patternMatchingService.AnalyzeContent(content);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting invoice data");
            return result;
        }
    }

    private static (VatNumberType Type, string CleanedTaxNumber)? ExtractTaxNumber(
        string fieldName,
        AnalyzedDocument document,
        Dictionary<string, double> confidences
    )
    {
        var taxNumber = TryGetStringField(document, fieldName, confidences);
        if (taxNumber == null)
        {
            return null;
        }
        return TaxNumberCleaning.CleanAndParseTaxNumber(taxNumber);
    }

    private static string? TryGetStringField(
        AnalyzedDocument document,
        string fieldName,
        Dictionary<string, double> confidences
    )
    {
        if (
            document.Fields.TryGetValue(fieldName, out DocumentField field)
            && field.FieldType == DocumentFieldType.String
        )
        {
            if (field.Confidence.HasValue)
            {
                confidences[fieldName] = field.Confidence.Value;
            }
            return field.ValueString;
        }
        return null;
    }

    private static DateTime? TryGetDateField(
        AnalyzedDocument document,
        string fieldName,
        Dictionary<string, double> confidences
    )
    {
        if (
            document.Fields.TryGetValue(fieldName, out DocumentField field)
            && field.FieldType == DocumentFieldType.Date
        )
        {
            if (field.Confidence.HasValue)
            {
                confidences[fieldName] = field.Confidence.Value;
            }
            return field.ValueDate?.DateTime;
        }
        return null;
    }

    private static (decimal? amount, string? currency) ExtractCurrencyField(
        AnalyzedDocument document,
        string fieldName,
        Dictionary<string, double> confidences
    )
    {
        if (
            document.Fields.TryGetValue(fieldName, out DocumentField field)
            && field.FieldType == DocumentFieldType.Currency
        )
        {
            if (field.Confidence.HasValue)
            {
                confidences[fieldName] = field.Confidence.Value;
            }

            var currencyValue = field.ValueCurrency;
            decimal? amount = null;

            if (!string.IsNullOrEmpty(field.Content))
            {
                amount = CurrencyAmountParserUtility.ParseCurrencyAmount(field.Content);
            }

            return (amount, currencyValue?.CurrencyCode);
        }
        return (null, null);
    }

    private static void ExtractCustomerAddress(AnalyzedDocument document, InvoiceAiData result)
    {
        var addressField =
            TryGetAddressField(document, AzureFieldNames.CustomerAddress, result.FieldConfidences)
            ?? TryGetAddressField(document, AzureFieldNames.BillingAddress, result.FieldConfidences);

        if (addressField != null)
        {
            result.CustomerAddress = new CustomerAddress
            {
                Full = FormatFullAddress(addressField),
                PostalCode = addressField.PostalCode,
                City = addressField.City,
                StreetAddress = addressField.StreetAddress,
                Road = addressField.Road,
                HouseNumber = addressField.HouseNumber,
            };
        }

        var addressContent =
            TryGetFieldContent(document, AzureFieldNames.CustomerAddress, result.FieldConfidences)
            ?? TryGetFieldContent(document, AzureFieldNames.BillingAddress, result.FieldConfidences);

        if (!string.IsNullOrEmpty(addressContent))
        {
            result.CustomerAddress ??= new CustomerAddress { FieldContent = addressContent };
            result.CustomerAddress.FieldContent = addressContent;
        }
    }

    private static AddressValue? TryGetAddressField(
        AnalyzedDocument document,
        string fieldName,
        Dictionary<string, double> confidences
    )
    {
        if (
            document.Fields.TryGetValue(fieldName, out DocumentField field)
            && field.FieldType == DocumentFieldType.Address
        )
        {
            if (field.Confidence.HasValue)
            {
                confidences[fieldName] = field.Confidence.Value;
            }
            return field.ValueAddress;
        }
        return null;
    }

    private static string? TryGetFieldContent(
        AnalyzedDocument document,
        string fieldName,
        Dictionary<string, double> confidences
    )
    {
        if (document.Fields.TryGetValue(fieldName, out DocumentField field))
        {
            if (field.Confidence.HasValue)
            {
                confidences[fieldName] = field.Confidence.Value;
            }
            return field.Content?.Replace("\r\n", " ").Replace("\n", " ").Replace("\r", " ");
        }
        return null;
    }

    private static string FormatFullAddress(AddressValue address)
    {
        var parts = new List<string>();

        if (!string.IsNullOrEmpty(address.StreetAddress))
        {
            parts.Add(address.StreetAddress);
        }
        else if (!string.IsNullOrEmpty(address.Road))
        {
            var roadPart = address.Road;
            if (!string.IsNullOrEmpty(address.HouseNumber))
            {
                roadPart += " " + address.HouseNumber;
            }
            parts.Add(roadPart);
        }

        if (!string.IsNullOrEmpty(address.City))
        {
            var cityPart = address.City;
            if (!string.IsNullOrEmpty(address.PostalCode))
            {
                cityPart = address.PostalCode + " " + cityPart;
            }
            parts.Add(cityPart);
        }

        return string.Join(", ", parts);
    }

    private static void ExtractTaxRateAndCurrency(AnalyzedDocument document, InvoiceAiData result)
    {
        if (
            document.Fields.TryGetValue(AzureFieldNames.Items, out DocumentField itemsField)
            && itemsField.FieldType == DocumentFieldType.List
        )
        {
            if (itemsField.Confidence.HasValue)
            {
                result.FieldConfidences[AzureFieldNames.Items] = itemsField.Confidence.Value;
            }

            var taxRates = new List<string>();
            var currencies = new List<string>();

            foreach (DocumentField itemField in itemsField.ValueList)
            {
                if (itemField.FieldType == DocumentFieldType.Dictionary)
                {
                    var itemFields = itemField.ValueDictionary;

                    if (
                        itemFields.TryGetValue(AzureFieldNames.TaxRate, out DocumentField taxRateField)
                        && taxRateField.FieldType == DocumentFieldType.String
                        && !string.IsNullOrEmpty(taxRateField.ValueString)
                    )
                    {
                        taxRates.Add(taxRateField.ValueString);
                    }

                    if (
                        itemFields.TryGetValue("Amount", out DocumentField amountField)
                        && amountField.FieldType == DocumentFieldType.Currency
                        && !string.IsNullOrEmpty(amountField.ValueCurrency?.CurrencyCode)
                    )
                    {
                        currencies.Add(amountField.ValueCurrency.CurrencyCode);
                    }
                }
            }

            // Extract most common tax rate
            if (taxRates.Count > 0)
            {
                result.TaxRate = taxRates.GroupBy(x => x).OrderByDescending(g => g.Count()).First().Key;
            }

            // Extract most common currency
            if (currencies.Count > 0)
            {
                result.Currency = currencies.GroupBy(x => x).OrderByDescending(g => g.Count()).First().Key;
            }
        }
    }

    private static void ExtractBankAccounts(AnalyzedDocument document, string content, InvoiceAiData result)
    {
        var bankAccounts = new List<string>();

        if (
            document.Fields.TryGetValue(AzureFieldNames.PaymentDetails, out DocumentField paymentDetailsField)
            && paymentDetailsField.FieldType == DocumentFieldType.List
        )
        {
            foreach (DocumentField itemField in paymentDetailsField.ValueList)
            {
                if (itemField.FieldType == DocumentFieldType.Dictionary)
                {
                    var itemFields = itemField.ValueDictionary;
                    if (
                        itemFields.TryGetValue(AzureFieldNames.Iban, out DocumentField ibanField)
                        && ibanField.FieldType == DocumentFieldType.String
                        && !string.IsNullOrEmpty(ibanField.ValueString)
                    )
                    {
                        bankAccounts.Add(ibanField.ValueString);
                    }
                }
            }
        }

        bankAccounts.AddRange(BankAccountExtractionUtility.ExtractBankAccounts(content));

        result.BankAccounts = [.. bankAccounts.Distinct()];

        result.BankAccountVariations =
        [
            .. result
                .BankAccounts.Select(bankAccount =>
                    BankAccountExtractionUtility.GenerateBankAccountVariations(bankAccount)
                )
                .SelectMany(variations => variations)
                .Distinct(),
        ];
    }

    /// <summary>
    /// Detects if the invoice is a collection invoice (gyűjtőszámla) by searching for the term in the content
    /// </summary>
    /// <param name="content">The full text content of the invoice</param>
    /// <returns>True if the content contains "gyűjtőszámla" (collection invoice), false otherwise</returns>
    private static bool DetectCollectionInvoice(string? content)
    {
        if (string.IsNullOrEmpty(content))
        {
            return false;
        }

        return content.Contains("gyűjtőszámla", StringComparison.OrdinalIgnoreCase);
    }
}

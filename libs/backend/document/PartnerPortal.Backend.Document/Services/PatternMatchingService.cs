using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;

namespace PartnerPortal.Backend.Document.Services;

public interface IPatternMatchingService
{
    List<PatternMatchResult> AnalyzeContent(string content);
    List<PatternGroup> GetPatternGroups();
}

public class PatternMatchingService : IPatternMatchingService
{
    private readonly ILogger<PatternMatchingService> _logger;
    private readonly List<PatternGroup> _patternGroups;

    public PatternMatchingService(ILogger<PatternMatchingService> logger)
    {
        _logger = logger;
        _patternGroups = InitializePatternGroups();
    }

    public List<PatternGroup> GetPatternGroups() => _patternGroups;

    public List<PatternMatchResult> AnalyzeContent(string content)
    {
        var results = new List<PatternMatchResult>();

        foreach (var group in _patternGroups)
        {
            var groupMatches = new List<PatternMatch>();
            var groupMatchCount = 0;

            foreach (var pattern in group.Patterns)
            {
                try
                {
                    var regex = new Regex(pattern.Pattern, pattern.Options);
                    var matches = regex.Matches(content);

                    foreach (Match match in matches)
                    {
                        string? extractedContext = null;
                        if (pattern.RequireContextExtraction)
                        {
                            extractedContext = ExtractContext(
                                content,
                                match,
                                pattern.ContextType,
                                pattern.ContextWordCount
                            );
                        }

                        groupMatches.Add(
                            new PatternMatch(
                                pattern.Name,
                                match.Value,
                                match.Index,
                                extractedContext,
                                pattern.ContextType
                            )
                        );
                        groupMatchCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing pattern '{PatternName}'", pattern.Name);
                }
            }

            if (groupMatches.Count > 0)
            {
                results.Add(
                    new PatternMatchResult(
                        group.Name,
                        [.. groupMatches.DistinctBy(m => m.MatchedText)],
                        groupMatchCount
                    )
                );
            }
        }

        return results;
    }

    private static string? ExtractContext(
        string content,
        Match match,
        ContextExtractionType contextType,
        int? contextWordCount
    )
    {
        var context = contextType switch
        {
            ContextExtractionType.MatchedText => match.Value,
            ContextExtractionType.ContainingSentence => ExtractContainingSentence(content, match),
            ContextExtractionType.SurroundingWords => ExtractSurroundingWords(content, match, contextWordCount ?? 10),
            _ => null,
        };

        if (context == null)
        {
            return null;
        }

        return NormalizeWhitespace(context);
    }

    private static string? NormalizeWhitespace(string content)
    {
        return content.Replace("\n", " ").Replace("\r", " ").Replace("\t", " ");
    }

    private static string ExtractContainingSentence(string content, Match match)
    {
        var matchStart = match.Index;
        var matchEnd = match.Index + match.Length;

        // Treat punctuation and line breaks as sentence boundaries
        var boundaries = new char[] { '.', '!', '?', '\n', '\r' };

        // Start: after the last boundary before the match
        var startBoundaryIdx = matchStart > 0 ? content.LastIndexOfAny(boundaries, matchStart - 1) : -1;
        var sentenceStart = startBoundaryIdx == -1 ? 0 : startBoundaryIdx + 1;

        // Skip leading whitespace/newlines
        while (sentenceStart < content.Length && char.IsWhiteSpace(content[sentenceStart]))
        {
            sentenceStart++;
        }

        // End: at the next boundary after the match
        var endBoundaryIdx = matchEnd < content.Length ? content.IndexOfAny(boundaries, matchEnd) : -1;
        int sentenceEnd;
        if (endBoundaryIdx == -1)
        {
            sentenceEnd = content.Length;
        }
        else
        {
            // Include terminal punctuation if present; newlines will be trimmed below
            var boundaryChar = content[endBoundaryIdx];
            sentenceEnd =
                endBoundaryIdx + ((boundaryChar == '.' || boundaryChar == '!' || boundaryChar == '?') ? 1 : 0);
        }

        if (sentenceEnd < sentenceStart)
        {
            sentenceEnd = Math.Max(sentenceStart, Math.Min(content.Length, matchEnd));
        }

        return content.Substring(sentenceStart, Math.Max(0, sentenceEnd - sentenceStart)).Trim();
    }

    private static string ExtractSurroundingWords(string content, Match match, int wordCount)
    {
        // Normalize all whitespace (including line breaks) to single spaces for cleaner context
        var normalizedBuilder = new System.Text.StringBuilder(content.Length);
        var originalToNormalized = new int[content.Length + 1];
        int normalizedIndex = 0;
        bool lastWasWhitespace = false;

        for (int i = 0; i < content.Length; i++)
        {
            char c = content[i];
            bool isWhitespace = char.IsWhiteSpace(c);
            if (isWhitespace)
            {
                if (!lastWasWhitespace)
                {
                    normalizedBuilder.Append(' ');
                    normalizedIndex++;
                    lastWasWhitespace = true;
                }
            }
            else
            {
                normalizedBuilder.Append(c);
                normalizedIndex++;
                lastWasWhitespace = false;
            }
            originalToNormalized[i + 1] = normalizedIndex;
        }

        var normalized = normalizedBuilder.ToString().Trim();
        if (normalized.Length == 0)
        {
            return string.Empty;
        }

        // Map original match range to normalized range
        int normStart = originalToNormalized[Math.Max(0, match.Index)];
        int normEnd = originalToNormalized[Math.Min(content.Length, match.Index + match.Length)];
        normStart = Math.Min(normStart, normalized.Length);
        normEnd = Math.Min(normEnd, normalized.Length);

        // Tokenize the normalized string by spaces and keep token spans
        var tokenSpans = new List<(int start, int length)>();
        int pos = 0;
        while (pos < normalized.Length)
        {
            while (pos < normalized.Length && normalized[pos] == ' ')
                pos++;
            if (pos >= normalized.Length)
                break;
            int tokenStart = pos;
            while (pos < normalized.Length && normalized[pos] != ' ')
                pos++;
            int tokenLength = pos - tokenStart;
            tokenSpans.Add((tokenStart, tokenLength));
        }

        // Find first/last token overlapping the match range
        int firstTok = -1;
        int lastTok = -1;
        for (int t = 0; t < tokenSpans.Count; t++)
        {
            var (ts, tl) = tokenSpans[t];
            int te = ts + tl;
            bool overlaps = !(te <= normStart || ts >= normEnd);
            if (overlaps)
            {
                if (firstTok == -1)
                    firstTok = t;
                lastTok = t;
            }
        }

        if (firstTok == -1)
        {
            // Fallback to matched text if mapping fails
            return match.Value.Trim();
        }

        int contextStartTok = Math.Max(0, firstTok - wordCount);
        int contextEndTok = Math.Min(tokenSpans.Count - 1, lastTok + wordCount);

        int sliceStart = tokenSpans[contextStartTok].start;
        var (endStart, endLen) = tokenSpans[contextEndTok];
        int sliceEnd = endStart + endLen;

        return normalized[sliceStart..sliceEnd].Trim();
    }

    private static List<PatternGroup> InitializePatternGroups()
    {
        return
        [
            new PatternGroup(
                "e.v. jelölés és nyilvántartási szám",
                [
                    // E.V. designations
                    new PatternRule(
                        "e.v. abbreviations",
                        @"(?<!\w)(?:e\.v\.|ev\.?|EV)(?!\w)",
                        ContextType: ContextExtractionType.SurroundingWords,
                        ContextWordCount: 3
                    ),
                    new PatternRule("egyéni vállalkozó", @"\begyéni vállalkozó\b"),
                    new PatternRule("egyéni vállalkozás", @"\begyéni vállalkozás\b"),
                    // Registration numbers - consolidated to handle OCR errors and expect exactly 8 digits
                    new PatternRule(
                        "nyilvántartási szám variations",
                        @"(?:Nyilvántartási szám|nyilvántartási szám|nyilv\.szám|nyilv\. sz\.|ny\.sz\.|nysz\.|EV nyilvántartási szám)[:.\s]*\d{8}"
                    ),
                ]
            ),
            new PatternGroup("Kisadózó megjelölés", [new PatternRule("kisadózó", @"\bkisadózó\b")]),
            new PatternGroup(
                "pénzforgalmi elszámolás",
                [
                    new PatternRule(
                        "pénzforgalmi elszámolás",
                        @"\bpénzforgalmi elszámolás\b",
                        ContextType: ContextExtractionType.ContainingSentence
                    ),
                    new PatternRule(
                        "pénzforgalmi elszámolásos",
                        @"\bpénzforgalmi elszámolásos\b",
                        ContextType: ContextExtractionType.ContainingSentence
                    ),
                ]
            ),
            new PatternGroup("Önszámlázás", [new PatternRule("önszámlázás", @"\bönszámlázás\b")]),
            new PatternGroup(
                "Devizás számla követelmények",
                [
                    new PatternRule(
                        "árfolyam",
                        @"árfolyam?\s?(adatok|információk)?:?\s*(\d[\d,\.]*(\s?[a-z]+)?)?[\/=\s]*\d[\d,\.]*\s(huf|ft)[\/=\s]*((\d[\d,\.]*)*(\s?[a-z]+)?\s?)?",
                        ContextType: ContextExtractionType.ContainingSentence
                    ),
                    new PatternRule(
                        "valuta = HUF/Ft",
                        @"\b\d+(?:[ .]\d{3})*(?:[,.]\d+)?\s*[A-Z]{3}\s*=\s*\d+(?:[ .]\d{3})*(?:[,.]\d+)?\s*(?:HUF|Ft\.?)\b",
                        ContextType: ContextExtractionType.ContainingSentence
                    ),
                    new PatternRule(
                        "ÁFA összege HUF/Ft",
                        @"(?<!\w)ÁFA\s+összege\s*[:\-]?\s*\d+(?:[ .]\d{3})*(?:[,.]\d+)?\s*(?:HUF|Ft\.?)(?!\w)",
                        ContextType: ContextExtractionType.ContainingSentence
                    ),
                    new PatternRule(
                        "ÁFA árfolyama Ft/XXX",
                        @"(?<!\w)ÁFA\s+árfolyama\s*[:\-]?\s*\d+(?:[ .]\d{3})*(?:[,.]\d+)?\s*(?:HUF|Ft\.?)\/[A-Z]{3}(?!\w)",
                        ContextType: ContextExtractionType.ContainingSentence
                    ),
                ]
            ),
            new PatternGroup(
                "Adómentességi jogszabályi hivatkozás",
                [
                    new PatternRule(
                        "adómentes",
                        @"(?<!\w)adómentes(?!\w)",
                        ContextType: ContextExtractionType.ContainingSentence
                    ),
                ]
            ),
        ];
    }
}

using System.Text.Json;
using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.BcService;
using PartnerPortal.Backend.Document.Services.Extractors;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.MarketBcApiClient;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing.Validation;

public class CompletionCertValidator : BaseDocumentValidator
{
    private readonly IBcService _bcService;
    private readonly JsonSerializerOptions _jsonOptions = new()
    {
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
    };
    private readonly IAnalyzeResultExtractor<CompletionCertificateAiData> _completionCertExtractor;

    public CompletionCertValidator(
        ILogger<CompletionCertValidator> logger,
        IBcService bcService,
        IAnalyzeResultExtractor<CompletionCertificateAiData> completionCertExtractor
    )
        : base(logger)
    {
        _bcService = bcService;
        _completionCertExtractor = completionCertExtractor;
    }

    public override async Task ValidateDocument(
        AnalyzeResult analysisResult,
        DocumentValidationResult documentValidation,
        List<ValidationJson> results
    )
    {
        var completionCertAiData = _completionCertExtractor.ExtractData(analysisResult);
        CompletionCertEntityData? completionCert = await ValidateTigIdAndGetFromBc(completionCertAiData.TigId, results);
        results.Add(ValidateCompletionCertDate(completionCertAiData.ExecutionDate, completionCert));
        results.Add(ValidateCompletionCertStatus(completionCert));
        results.Add(ValidateCompletionCertLines(completionCertAiData.ActualGrossValue.amount, completionCert));
        results.Add(ValidateCompletionCertProjectName(completionCertAiData.ProjectName, completionCert));
        results.Add(ValidateCompletionCertProjectNumber(completionCertAiData.WorkNumber, completionCert));

        results.Add(
            ValidateStringFieldExistence(CompletionCertAiFieldNames.ContractorName, completionCertAiData.ContractorName)
        );
        results.Add(
            ValidateStringFieldExistence(
                CompletionCertAiFieldNames.SubContractorName,
                completionCertAiData.SubContractorName
            )
        );
        results.AddRange(
            ValidateAmountAndCurrencyFieldExistence(
                CompletionCertAiFieldNames.ContractTotalAmount,
                CompletionCertAiFieldNames.ContractTotalCurrencyCode,
                completionCertAiData.ContractWork.amount,
                completionCertAiData.ContractWork.currency
            )
        );
        results.AddRange(
            ValidateAmountAndCurrencyFieldExistence(
                CompletionCertAiFieldNames.ExtraAndMissedWorksAmount,
                CompletionCertAiFieldNames.ExtraAndMissedWorksCurrencyCode,
                completionCertAiData.AdditionalWorkAfterFinalAcceptance.amount,
                completionCertAiData.AdditionalWorkAfterFinalAcceptance.currency
            )
        );
        results.AddRange(
            ValidateAmountAndCurrencyFieldExistence(
                CompletionCertAiFieldNames.CurrentGrossAmount,
                CompletionCertAiFieldNames.CurrentGrossAmountCurrCode,
                completionCertAiData.ActualGrossValue.amount,
                completionCertAiData.ActualGrossValue.currency
            )
        );
        results.AddRange(
            ValidateAmountAndCurrencyFieldExistence(
                CompletionCertAiFieldNames.CurrentNetAmount,
                CompletionCertAiFieldNames.CurrentNetAmountCurrCode,
                completionCertAiData.CurrentNetPerformanceValue.amount,
                completionCertAiData.CurrentNetPerformanceValue.currency
            )
        );

        if (completionCert != null)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "TIG BC-ből (DEBUG)",
                    Status = ColumnStatus.Ok,
                    ValueOnly = JsonSerializer.Serialize(completionCert, _jsonOptions),
                }
            );
        }
    }

    private async Task<CompletionCertEntityData?> ValidateTigIdAndGetFromBc(
        string? completionCertNo,
        List<ValidationJson> validationResults
    )
    {
        if (string.IsNullOrEmpty(completionCertNo))
        {
            validationResults.Add(
                new ValidationJson { EloColumnName = CompletionCertAiFieldNames.TigId, Status = ColumnStatus.Missing }
            );
        }

        var completionCert = !string.IsNullOrEmpty(completionCertNo)
            ? await _bcService.GetCompletionCert(completionCertNo)
            : null;

        if (completionCert == null)
        {
            validationResults.Add(
                new ValidationJson
                {
                    EloColumnName = CompletionCertAiFieldNames.TigId,
                    DocumentAiColumnValue = completionCertNo,
                    Status = ColumnStatus.Missing,
                }
            );
        }
        else
        {
            validationResults.Add(
                new ValidationJson
                {
                    EloColumnName = CompletionCertAiFieldNames.TigId,
                    DocumentAiColumnValue = completionCertNo,
                    BcColumnValue = completionCert.DocumentNo,
                    Status = ColumnStatus.Ok,
                }
            );
        }

        return completionCert;
    }

    private static ValidationJson ValidateCompletionCertDate(
        DateTime? executionDate,
        CompletionCertEntityData? completionCert
    )
    {
        return CompareFieldValue(
            CompletionCertAiFieldNames.ExecutionDate,
            completionCert?.CompletionCertificateDate,
            executionDate?.ToString("yyyyMMdd")
        );
    }

    private static ValidationJson ValidateCompletionCertStatus(CompletionCertEntityData? completionCert)
    {
        string correctStatus = "60 Elküldve";
        if (completionCert?.ELOWorkflowStatus != correctStatus)
        {
            return new ValidationJson
            {
                EloColumnName = CompletionCertAiFieldNames.TigStatus,
                Status = ColumnStatus.Discrepancy,
                BcColumnValue = completionCert?.ELOWorkflowStatus,
            };
        }
        return new ValidationJson
        {
            EloColumnName = CompletionCertAiFieldNames.TigStatus,
            Status = ColumnStatus.Ok,
            BcColumnValue = completionCert.ELOWorkflowStatus,
        };
    }

    private static ValidationJson ValidateCompletionCertLines(
        decimal? grossAmountFromDocument,
        CompletionCertEntityData? completionCert
    )
    {
        var grossAmountSum = completionCert
            ?.CompletionCertLines.CompletionCertLineEntityData.ToList()
            .Sum(line => ParseHelper.ParseCurrencyAmount(line.GrossAmount));

        if (grossAmountFromDocument != grossAmountSum)
        {
            return new ValidationJson
            {
                EloColumnName = CompletionCertAiFieldNames.CurrentGrossAmount,
                Status = ColumnStatus.Discrepancy,
                BcColumnValue = grossAmountSum.ToString(),
                DocumentAiColumnValue = grossAmountFromDocument?.ToString(),
            };
        }

        return new ValidationJson
        {
            EloColumnName = CompletionCertAiFieldNames.CurrentGrossAmount,
            Status = ColumnStatus.Ok,
            BcColumnValue = grossAmountSum.ToString(),
            DocumentAiColumnValue = grossAmountFromDocument?.ToString(),
        };
    }

    private static ValidationJson ValidateCompletionCertProjectName(
        string? projectName,
        CompletionCertEntityData? completionCert
    )
    {
        return FuzzyCompareFieldValue(
            CompletionCertAiFieldNames.ProjectName,
            completionCert?.ProjectName,
            projectName,
            0.9
        );
    }

    private static ValidationJson ValidateCompletionCertProjectNumber(
        string? projectNumber,
        CompletionCertEntityData? completionCert
    )
    {
        return FuzzyCompareFieldValue(
            CompletionCertAiFieldNames.WorkNumber,
            completionCert?.ProjectNo,
            projectNumber,
            0.9
        );
    }
}

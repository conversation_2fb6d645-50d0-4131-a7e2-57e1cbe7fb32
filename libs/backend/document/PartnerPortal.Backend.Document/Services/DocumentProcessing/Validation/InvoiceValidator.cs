using System.Globalization;
using System.Text.Json;
using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Logging;
using Nav.Invoice.Client.Models;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.BcService;
using PartnerPortal.Backend.Document.Services.Extractors;
using PartnerPortal.Backend.Document.Services.NavService;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.MarketBcApiClient;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using ContactBank = PartnerPortal.Backend.Document.DocumentApiStub.Models.ContactBank;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing.Validation;

public class InvoiceValidator : BaseDocumentValidator
{
    private readonly IBcService _bcService;
    private readonly ILogger<InvoiceValidator> _logger;

    private readonly IAnalyzeResultExtractor<InvoiceAiData> _invoiceExtractor;
    private readonly INavService _navService;
    private readonly JsonSerializerOptions _jsonOptions = new()
    {
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
    };
    private readonly IInScopeRetrieverService _taskSourceRetrieverService;

    public InvoiceValidator(
        ILogger<InvoiceValidator> logger,
        IBcService bcService,
        IAnalyzeResultExtractor<InvoiceAiData> invoiceExtractor,
        INavService navService,
        IInScopeRetrieverService taskSourceRetrieverService
    )
        : base(logger)
    {
        _bcService = bcService;
        _logger = logger;

        _invoiceExtractor = invoiceExtractor;
        _navService = navService;
        _taskSourceRetrieverService = taskSourceRetrieverService;
    }

    public override async Task ValidateDocument(
        AnalyzeResult analysisResult,
        DocumentValidationResult documentValidation,
        List<ValidationJson> results
    )
    {
        var invoiceData = _invoiceExtractor.ExtractData(analysisResult);
        var (customer, customerDetails) = await ValidateCustomer(invoiceData.CustomerTaxNumber, results);
        results.Add(CompareFieldValue(ELOFieldNames.CustomerName, customer?.Name, invoiceData.CustomerName));
        results.AddRange(ValidateCustomerAddress(invoiceData.CustomerAddress, customer));
        var completionCerts = await _taskSourceRetrieverService.TryGetCompCertDocuments(documentValidation);
        var (vendorValidationResults, vendor) = await ValidateVendorTaxId(invoiceData.VendorTaxId);
        var navInvoiceData = await GetNavValidationInvoiceData(
            invoiceData.InvoiceId,
            customer,
            customerDetails,
            vendor
        );
        results.Add(IsFoundInNav(navInvoiceData));
        results.AddRange(vendorValidationResults);
        results.Add(ValidateStringFieldExistence(ELOFieldNames.Currency, invoiceData.Currency));
        results.Add(ValidateStringFieldExistence(ELOFieldNames.TaxRate, invoiceData.TaxRate));
        results.Add(
            ValidateStringToNavString(ELOFieldNames.PaymentTerm, invoiceData.PaymentTerm, navInvoiceData?.PaymentTerm)
        );
        var bankValidationResults = ValidateBankInformation(invoiceData.BankAccountVariations, vendor);
        results.AddRange(bankValidationResults);
        results.Add(ValidateStringFieldExistence(ELOFieldNames.InvoiceId, invoiceData.InvoiceId));
        results.Add(ValidateDateFieldExistence(ELOFieldNames.DueDate, invoiceData.DueDate));
        results.Add(
            ValidateAmountToNavAmount(
                ELOFieldNames.InvoiceTotalAmount,
                invoiceData.InvoiceTotal.amount,
                navInvoiceData?.InvoiceTotal
            )
        );

        results.Add(
            ValidateInvoiceDateToNavDate(
                ELOFieldNames.InvoiceDate,
                invoiceData.InvoiceDate,
                navInvoiceData?.InvoiceDate
            )
        );
        results.Add(
            ValidateInvoiceDateToNavDate(
                ELOFieldNames.FulfilmentDate,
                invoiceData.FulfilmentDate,
                navInvoiceData?.FulfillmentDate
            )
        );
        results.Add(
            ValidateInvoiceDateToNavDate(
                ELOFieldNames.ServiceStartDate,
                invoiceData.ServiceStartDate,
                navInvoiceData?.ServiceStartDate
            )
        );
        results.Add(
            ValidateInvoiceDateToNavDate(
                ELOFieldNames.ServiceEndDate,
                invoiceData.ServiceEndDate,
                navInvoiceData?.ServiceEndDate
            )
        );

        var fulfilmentDateAfterIssueDate = IsFulfilmentDateAfterIssueDate(
            invoiceData.FulfilmentDate ?? navInvoiceData?.FulfillmentDate,
            invoiceData.InvoiceDate
        );
        if (fulfilmentDateAfterIssueDate != null)
        {
            results.Add(fulfilmentDateAfterIssueDate);
        }

        var fulfilmentDateToCompletionCertExecutionDate = ValidateFulfilmentDateForTigInvoice(
            completionCerts,
            invoiceData.FulfilmentDate
        );
        if (fulfilmentDateToCompletionCertExecutionDate != null)
        {
            results.Add(fulfilmentDateToCompletionCertExecutionDate);
        }

        var invoiceGrossAmountToCompletionCertGrossAmount = InvoiceGrossAmountToCompletionCertGrossAmount(
            invoiceData.InvoiceTotal.amount,
            completionCerts
        );
        if (invoiceGrossAmountToCompletionCertGrossAmount != null)
        {
            results.Add(invoiceGrossAmountToCompletionCertGrossAmount);
        }

        var verifyCalculatedFulfilmentDateMatchesInvoice = VerifyCalculatedFulfilmentDateMatchesInvoice(
            invoiceData.FulfilmentDate ?? navInvoiceData?.FulfillmentDate,
            invoiceData.InvoiceDate,
            invoiceData.DueDate,
            invoiceData.ServiceEndDate ?? navInvoiceData?.ServiceEndDate
        );
        if (verifyCalculatedFulfilmentDateMatchesInvoice != null)
        {
            results.Add(verifyCalculatedFulfilmentDateMatchesInvoice);
        }

        var validateNoWireTransferWarning = ValidateIsNotWireTransfer(
            invoiceData.PaymentTerm ?? navInvoiceData?.PaymentTerm
        );
        if (validateNoWireTransferWarning != null)
        {
            results.Add(validateNoWireTransferWarning);
        }

        var validateCollectionInvoice = ValidateCollectionInvoice(invoiceData.IsCollectionInvoice);
        if (validateCollectionInvoice != null)
        {
            results.Add(validateCollectionInvoice);
        }

        var (validateTigInvoiceVatRateAgainstContract, contracts) = await ValidateTigInvoiceVatRateAgainstContract(
            completionCerts,
            invoiceData.TaxRate
        );
        if (validateTigInvoiceVatRateAgainstContract != null)
        {
            results.Add(validateTigInvoiceVatRateAgainstContract);
        }

        var validateLegalInfo = ValidateLegalInfo(invoiceData.PatternMatches);
        if (validateLegalInfo != null)
        {
            results.Add(validateLegalInfo);
        }

        AddAdditionalDebugInfo(
            results,
            customer,
            completionCerts,
            vendor,
            invoiceData.BankAccounts,
            invoiceData.BankAccountVariations,
            contracts,
            navInvoiceData
        );
    }

    private async Task<NavInvoiceData?> GetNavValidationInvoiceData(
        string? invoiceId,
        PartnerCache? customer,
        CustomerDetails? customerDetails,
        PartnerCache? vendor
    )
    {
        if (customer != null && vendor != null && customerDetails != null && invoiceId != null)
        {
            try
            {
                var invoiceData = await _navService.QueryInvoiceDataAsync(
                    new InvoiceNumberQueryType
                    {
                        InvoiceNumber = invoiceId,
                        InvoiceDirection = InvoiceDirectionType.Inbound,
                        SupplierTaxNumber = vendor.VatRegistrationNo[..8],
                    },
                    customerDetails
                );

                if (invoiceData != null)
                {
                    return new NavInvoiceData
                    {
                        FulfillmentDate =
                            invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.InvoiceDeliveryDate
                            == DateTime.MinValue
                                ? null
                                : invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.InvoiceDeliveryDate,
                        InvoiceDate =
                            invoiceData.InvoiceIssueDate == DateTime.MinValue ? null : invoiceData.InvoiceIssueDate,
                        PaymentTerm = GetPaymentMethod(
                            invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.PaymentMethod
                        ),
                        CurrencyCode = invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.CurrencyCode,
                        InvoiceId = invoiceData.InvoiceNumber,
                        InvoiceTotal = invoiceData
                            .InvoiceMain
                            .Invoice
                            ?.InvoiceSummary
                            .SummaryGrossData
                            ?.InvoiceGrossAmount,
                        ServiceStartDate =
                            invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.InvoiceDeliveryPeriodStart
                            == DateTime.MinValue
                                ? null
                                : invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.InvoiceDeliveryPeriodStart,
                        ServiceEndDate =
                            invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.InvoiceDeliveryPeriodEnd
                            == DateTime.MinValue
                                ? null
                                : invoiceData.InvoiceMain.Invoice?.InvoiceHead.InvoiceDetail.InvoiceDeliveryPeriodEnd,
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to query NAV invoice data for invoice {InvoiceId} Continue validation without NAV data",
                    invoiceId
                );
            }
        }

        return null;

        static string? GetPaymentMethod(PaymentMethodType? paymentMethod)
        {
            return paymentMethod switch
            {
                PaymentMethodType.Transfer => "Banki átutalás",
                PaymentMethodType.Cash => "Készpénz",
                PaymentMethodType.Card => "Bankkártya",
                PaymentMethodType.Voucher => "Utalvány",
                PaymentMethodType.Other => "Egyéb",
                _ => null,
            };
        }
    }

    public static ValidationJson? VerifyCalculatedFulfilmentDateMatchesInvoice(
        DateTime? fulfilmentDate,
        DateTime? invoiceDate,
        DateTime? dueDate,
        DateTime? serviceEndDate
    )
    {
        if (fulfilmentDate == null || invoiceDate == null || dueDate == null || serviceEndDate == null)
        {
            return null;
        }

        var calculatedPerformanceDate = FulfilmentCalculator.CalculateFulfilmentDate(
            serviceEndDate.Value,
            invoiceDate.Value,
            dueDate.Value
        );

        if (calculatedPerformanceDate.Date == fulfilmentDate.Value.Date)
        {
            return new ValidationJson
            {
                EloColumnName = ELOFieldNames.CalculatedPerformanceDateMatch,
                ValueOnly = "true",
            };
        }

        return new ValidationJson { EloColumnName = ELOFieldNames.CalculatedPerformanceDateMatch, ValueOnly = "false" };
    }

    public static ValidationJson IsFoundInNav(NavInvoiceData? navInvoiceData)
    {
        return new ValidationJson
        {
            EloColumnName = ELOFieldNames.FoundInNav,
            ValueOnly = navInvoiceData != null ? "true" : "false",
        };
    }

    public static ValidationJson? IsFulfilmentDateAfterIssueDate(DateTime? fulfilmentDate, DateTime? invoiceDate)
    {
        if (invoiceDate == null || fulfilmentDate == null)
        {
            return null;
        }

        if (fulfilmentDate > invoiceDate)
        {
            return new ValidationJson
            {
                EloColumnName = ELOFieldNames.FulfilmentDateAfterIssueDate,
                ValueOnly = "true",
            };
        }

        return new ValidationJson { EloColumnName = ELOFieldNames.FulfilmentDateAfterIssueDate, ValueOnly = "false" };
    }

    public static ValidationJson ValidateInvoiceDateToNavDate(
        string fieldName,
        DateTime? invoiceDate,
        DateTime? navDate
    )
    {
        // Both values exist - check if they match
        if (invoiceDate != null && navDate != null)
        {
            var status = invoiceDate.Value.Date == navDate.Value.Date ? ColumnStatus.Ok : ColumnStatus.NavDiscrepancy;

            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = status,
                DocumentAiColumnValue = invoiceDate.Value.ToString("yyyyMMdd"),
                NavColumnValue = navDate.Value.ToString("yyyyMMdd"),
            };
        }

        // Invoice date missing but NAV has value - use NAV as source
        if (navDate != null)
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Navsource,
                NavColumnValue = navDate.Value.ToString("yyyyMMdd"),
            };
        }

        // Invoice date exists but no NAV value - use invoice date
        if (invoiceDate != null)
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Ok,
                DocumentAiColumnValue = invoiceDate.Value.ToString("yyyyMMdd"),
            };
        }

        // Neither value exists
        return new ValidationJson { EloColumnName = fieldName, Status = ColumnStatus.Missing };
    }

    public static ValidationJson ValidateAmountToNavAmount(string fieldName, decimal? invoiceAmount, decimal? navAmount)
    {
        if (invoiceAmount.HasValue && navAmount.HasValue)
        {
            var status = invoiceAmount.Value == navAmount.Value ? ColumnStatus.Ok : ColumnStatus.NavDiscrepancy;

            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = status,
                DocumentAiColumnValue = invoiceAmount.Value.ToString(CultureInfo.InvariantCulture),
                NavColumnValue = navAmount.Value.ToString(CultureInfo.InvariantCulture),
            };
        }

        if (navAmount.HasValue)
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Navsource,
                NavColumnValue = navAmount.Value.ToString(CultureInfo.InvariantCulture),
            };
        }

        if (invoiceAmount.HasValue)
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Ok,
                DocumentAiColumnValue = invoiceAmount.Value.ToString(CultureInfo.InvariantCulture),
            };
        }

        return new ValidationJson { EloColumnName = fieldName, Status = ColumnStatus.Missing };
    }

    public static ValidationJson ValidateStringToNavString(string fieldName, string? invoiceString, string? navString)
    {
        if (!string.IsNullOrEmpty(invoiceString) && !string.IsNullOrEmpty(navString))
        {
            var status = invoiceString.Trim().Equals(navString.Trim(), StringComparison.InvariantCultureIgnoreCase)
                ? ColumnStatus.Ok
                : ColumnStatus.NavDiscrepancy;

            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = status,
                DocumentAiColumnValue = invoiceString,
                NavColumnValue = navString,
            };
        }

        if (!string.IsNullOrEmpty(navString))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Navsource,
                NavColumnValue = navString,
            };
        }

        if (!string.IsNullOrEmpty(invoiceString))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Ok,
                DocumentAiColumnValue = invoiceString,
            };
        }

        return new ValidationJson { EloColumnName = fieldName, Status = ColumnStatus.Missing };
    }

    private ValidationJson? ValidateFulfilmentDateForTigInvoice(
        List<ExtractedCompletionCert>? completionCerts,
        DateTime? invoiceFulfilmentDate
    )
    {
        if (completionCerts == null || invoiceFulfilmentDate == null)
        {
            return null;
        }

        List<InvoiceFulfilmentDateToCompletionCertExecutionDateResult> result = [];

        foreach (var completionCert in completionCerts)
        {
            if (completionCert.ExecutionDate?.Date != invoiceFulfilmentDate.Value.Date)
            {
                result.Add(
                    new InvoiceFulfilmentDateToCompletionCertExecutionDateResult
                    {
                        IsMatch = false,
                        TigId = completionCert.TigId,
                        InvoiceFulfilmentDate = invoiceFulfilmentDate,
                        CompletionCertExecutionDate = completionCert.ExecutionDate,
                    }
                );
            }
            else
            {
                result.Add(
                    new InvoiceFulfilmentDateToCompletionCertExecutionDateResult
                    {
                        IsMatch = true,
                        TigId = completionCert.TigId,
                        InvoiceFulfilmentDate = invoiceFulfilmentDate,
                        CompletionCertExecutionDate = completionCert.ExecutionDate,
                    }
                );
            }
        }

        return new ValidationJson
        {
            EloColumnName = ELOFieldNames.InvoiceFulfilmentDateToCompletionCertExecutionDate,
            ValueOnly = JsonSerializer.Serialize(result, _jsonOptions),
        };
    }

    private ValidationJson? InvoiceGrossAmountToCompletionCertGrossAmount(
        decimal? invoiceTotalAmount,
        List<ExtractedCompletionCert>? completionCerts
    )
    {
        if (invoiceTotalAmount == null || completionCerts == null)
        {
            return null;
        }

        var result = completionCerts
            .Select(completionCert => new InvoiceGrossAmountToCompletionCertGrossAmountResult
            {
                IsMatch = completionCert.GrossAmount == invoiceTotalAmount,
                TigId = completionCert.TigId,
                InvoiceGrossAmount = invoiceTotalAmount,
                CompletionCertGrossAmount = completionCert.GrossAmount,
            })
            .ToList();

        return new ValidationJson
        {
            EloColumnName = ELOFieldNames.InvoiceGrossAmountToCompletionCertGrossAmount,
            ValueOnly = JsonSerializer.Serialize(result, _jsonOptions),
        };
    }

    private async Task<(PartnerCache? customer, CustomerDetails? customerDetails)> ValidateCustomer(
        (VatNumberType Type, string CleanedTaxNumber)? customerTaxNumber,
        List<ValidationJson> results
    )
    {
        if (
            customerTaxNumber == null
            || string.IsNullOrEmpty(customerTaxNumber.Value.CleanedTaxNumber)
            || customerTaxNumber.Value.Type == VatNumberType.Unknown
        )
        {
            results.AddRange(
                [
                    new ValidationJson { EloColumnName = ELOFieldNames.CustomerTaxId, Status = ColumnStatus.Missing },
                    new ValidationJson { EloColumnName = ELOFieldNames.CompanyId, Status = ColumnStatus.Missing },
                ]
            );
            return (null, null);
        }

        var (customer, customerDetails) = await _bcService.GetCustomerByVatNo(
            customerTaxNumber.Value.Type,
            customerTaxNumber.Value.CleanedTaxNumber
        );

        if (customer == null)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = ELOFieldNames.CustomerNotFoundInCustomerCodes,
                    Status = ColumnStatus.Discrepancy,
                }
            );

            results.AddRange(
                [
                    new ValidationJson
                    {
                        EloColumnName = ELOFieldNames.CustomerTaxId,
                        Status = ColumnStatus.Missing,
                        DocumentAiColumnValue = customerTaxNumber.Value.CleanedTaxNumber,
                    },
                    new ValidationJson { EloColumnName = ELOFieldNames.CompanyId, Status = ColumnStatus.Missing },
                ]
            );

            return (null, null);
        }

        results.AddRange(
            [
                new ValidationJson
                {
                    EloColumnName = ELOFieldNames.CustomerTaxId,
                    Status = ColumnStatus.Ok,
                    BcColumnValue = customer.VatRegistrationNo,
                    DocumentAiColumnValue = customerTaxNumber.Value.CleanedTaxNumber,
                },
                new ValidationJson
                {
                    EloColumnName = ELOFieldNames.CompanyId,
                    Status = ColumnStatus.Ok,
                    BcColumnValue = customerDetails?.Code,
                },
            ]
        );

        return (customer, customerDetails);
    }

    private async Task<(List<ValidationJson> ValidationResults, PartnerCache? Vendor)> ValidateVendorTaxId(
        (VatNumberType Type, string CleanedTaxNumber)? vendorTaxId
    )
    {
        var results = new List<ValidationJson>();
        bool isMissing =
            vendorTaxId == null
            || string.IsNullOrEmpty(vendorTaxId.Value.CleanedTaxNumber)
            || vendorTaxId.Value.Type == VatNumberType.Unknown;
        if (isMissing)
        {
            results.Add(
                new ValidationJson { EloColumnName = ELOFieldNames.VendorTaxId, Status = ColumnStatus.Missing }
            );
        }

        var bcPartner = isMissing
            ? null
            : await _bcService.GetPartnerByVatNo(vendorTaxId!.Value.Type, vendorTaxId.Value.CleanedTaxNumber);

        if (bcPartner == null)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = ELOFieldNames.VendorTaxId,
                    DocumentAiColumnValue = vendorTaxId!.Value.CleanedTaxNumber,
                    Status = ColumnStatus.Missing,
                    BcColumnValue = null,
                }
            );
            results.Add(new ValidationJson { EloColumnName = ELOFieldNames.PartnerId, Status = ColumnStatus.Missing });
        }
        else
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = ELOFieldNames.VendorTaxId,
                    Status = ColumnStatus.Ok,
                    BcColumnValue = bcPartner.VatRegistrationNo,
                    DocumentAiColumnValue = vendorTaxId!.Value.CleanedTaxNumber,
                }
            );
            results.Add(
                new ValidationJson
                {
                    EloColumnName = ELOFieldNames.PartnerId,
                    BcColumnValue = bcPartner.ContactNo,
                    Status = ColumnStatus.Ok,
                }
            );
        }

        return (results, bcPartner);
    }

    private static List<ValidationJson> ValidateBankInformation(
        List<string>? bankAccountVariations,
        PartnerCache? bcPartner
    )
    {
        if (bankAccountVariations != null && bankAccountVariations.Count > 0)
        {
            ContactBank? bcBankInfo = null;
            var validAiBankAccount = "";

            foreach (var variation in bankAccountVariations)
            {
                bcBankInfo = bcPartner
                    ?.ContactBanks.Where(bank =>
                        BankAccountExtractionUtility.BankAccountEquals(bank.BankAccountNo, variation)
                    )
                    .FirstOrDefault();

                if (bcBankInfo != null)
                {
                    validAiBankAccount = variation;
                    break;
                }
            }

            if (bcBankInfo != null)
            {
                return
                [
                    new ValidationJson
                    {
                        EloColumnName = ELOFieldNames.BankAccountNo,
                        Status = ColumnStatus.Ok,
                        BcColumnValue = bcBankInfo.BankAccountNo,
                        DocumentAiColumnValue = validAiBankAccount,
                    },
                    new ValidationJson { EloColumnName = ELOFieldNames.BankAccountId, ValueOnly = bcBankInfo.BankCode },
                ];
            }
            else
            {
                return
                [
                    new ValidationJson
                    {
                        EloColumnName = ELOFieldNames.BankAccountNo,
                        Status = ColumnStatus.Missing,
                        DocumentAiColumnValue = validAiBankAccount,
                    },
                ];
            }
        }

        return
        [
            new ValidationJson { EloColumnName = ELOFieldNames.BankAccountNo, Status = ColumnStatus.Missing },
            new ValidationJson { EloColumnName = ELOFieldNames.BankAccountId, Status = ColumnStatus.Missing },
        ];
    }

    private static List<ValidationJson> ValidateCustomerAddress(CustomerAddress? aiAddress, PartnerCache? bcCustomer)
    {
        var results = new List<ValidationJson>();

        bool? postalCodeValid = null;
        if (!string.IsNullOrEmpty(aiAddress?.PostalCode) || !string.IsNullOrEmpty(bcCustomer?.PostCode))
        {
            postalCodeValid =
                CompareFieldValue(ELOFieldNames.PostalCode, bcCustomer?.PostCode, aiAddress?.PostalCode).Status
                == ColumnStatus.Ok;
        }
        else if (!string.IsNullOrEmpty(aiAddress?.FieldContent) && !string.IsNullOrEmpty(bcCustomer?.PostCode))
        {
            postalCodeValid = aiAddress.FieldContent.Contains(bcCustomer.PostCode);
        }

        results.Add(
            new ValidationJson
            {
                EloColumnName = ELOFieldNames.PostalCode,
                Status = GetStatus(postalCodeValid),
                BcColumnValue = bcCustomer?.PostCode,
                DocumentAiColumnValue = aiAddress?.PostalCode ?? aiAddress?.FieldContent,
            }
        );

        bool? cityValid = null;
        if (!string.IsNullOrEmpty(aiAddress?.City) || !string.IsNullOrEmpty(bcCustomer?.City))
        {
            cityValid =
                CompareFieldValue(ELOFieldNames.City, bcCustomer?.City, aiAddress?.City).Status == ColumnStatus.Ok;
        }
        else if (!string.IsNullOrEmpty(aiAddress?.FieldContent) && !string.IsNullOrEmpty(bcCustomer?.City))
        {
            cityValid = aiAddress.FieldContent.Contains(bcCustomer.City, StringComparison.InvariantCultureIgnoreCase);
        }

        results.Add(
            new ValidationJson
            {
                EloColumnName = ELOFieldNames.City,
                Status = GetStatus(cityValid),
                BcColumnValue = bcCustomer?.City,
                DocumentAiColumnValue = aiAddress?.City ?? aiAddress?.FieldContent,
            }
        );

        var streetValid = AddressValidationHelper.ValidateStreetAddress(bcCustomer?.Address, aiAddress);

        results.Add(
            new ValidationJson
            {
                EloColumnName = ELOFieldNames.StreetAddress,
                Status = GetStatus(streetValid),
                BcColumnValue = bcCustomer?.Address,
                DocumentAiColumnValue = aiAddress?.StreetAddress ?? (aiAddress?.Road + " " + aiAddress?.HouseNumber),
            }
        );

        return results;

        static ColumnStatus GetStatus(bool? isValid)
        {
            if (!isValid.HasValue)
            {
                return ColumnStatus.Missing;
            }
            return isValid.Value ? ColumnStatus.Ok : ColumnStatus.Discrepancy;
        }
    }

    private async Task<(
        ValidationJson? ValidationResult,
        List<(ContractEntityData contract, string tigId)>? contracts
    )> ValidateTigInvoiceVatRateAgainstContract(
        List<ExtractedCompletionCert>? extractedCompCerts,
        string? invoiceVatRate
    )
    {
        if (extractedCompCerts == null || extractedCompCerts.Count == 0 || invoiceVatRate == null)
        {
            return (null, null);
        }

        var contracts = await GetContractsFromCompletionCerts(extractedCompCerts);
        var mismatches = FindVatRateMismatches(contracts, invoiceVatRate);

        if (mismatches.Count == 0)
        {
            return (null, null);
        }

        return (
            new ValidationJson
            {
                EloColumnName = ELOFieldNames.TigInvoiceVatRateAgainstContractNotMatch,
                ValueOnly = JsonSerializer.Serialize(mismatches, _jsonOptions),
            },
            contracts
        );
    }

    private async Task<List<(ContractEntityData contract, string tigId)>> GetContractsFromCompletionCerts(
        List<ExtractedCompletionCert>? extractedCompCerts
    )
    {
        if (extractedCompCerts == null)
        {
            return [];
        }

        var contracts = new List<(ContractEntityData contract, string tigId)>();

        foreach (var extractedCompCert in extractedCompCerts)
        {
            var contract = await TryGetContractFromCompletionCert(extractedCompCert);
            if (contract.HasValue)
            {
                contracts.Add(contract.Value);
            }
        }

        return contracts;
    }

    private async Task<(ContractEntityData contract, string tigId)?> TryGetContractFromCompletionCert(
        ExtractedCompletionCert extractedCompCert
    )
    {
        if (string.IsNullOrEmpty(extractedCompCert.TigId))
        {
            return null;
        }

        var completionCert = await _bcService.GetCompletionCert(extractedCompCert.TigId);
        var normalContractLine = GetFirstNormalContractLine(completionCert);

        if (normalContractLine == null)
        {
            return null;
        }

        var contract = await _bcService.GetContractByNumber(normalContractLine.ContractDocumentNo);
        return contract != null ? (contract, extractedCompCert.TigId) : null;
    }

    private static CompletionCertLineEntityData? GetFirstNormalContractLine(CompletionCertEntityData? completionCert)
    {
        var contractLines = completionCert?.CompletionCertLines?.CompletionCertLineEntityData;
        if (contractLines == null)
        {
            return null;
        }

        foreach (var line in contractLines)
        {
            if (line?.ContractSubType == "Normál")
            {
                return line;
            }
        }

        return null;
    }

    private static List<TigInvoiceVatRateAgainstContractNotMatchResult> FindVatRateMismatches(
        List<(ContractEntityData contract, string tigId)> contracts,
        string invoiceVatRate
    )
    {
        return
        [
            .. contracts
                .Where(contract => !VatCategoryMatcher.IsMatch(contract.contract.VatType, invoiceVatRate))
                .Select(contract => new TigInvoiceVatRateAgainstContractNotMatchResult
                {
                    TigId = contract.tigId,
                    InvoiceVatRate = invoiceVatRate,
                    ContractVatRate = contract.contract.VatType,
                }),
        ];
    }

    private static ValidationJson? ValidateIsNotWireTransfer(string? paymentMethod)
    {
        if (string.IsNullOrEmpty(paymentMethod))
        {
            return null;
        }

        if (PaymentMethodUtility.IsWireTransfer(paymentMethod))
        {
            return null;
        }

        return new ValidationJson { EloColumnName = ELOFieldNames.NotWireTransferWarning, ValueOnly = paymentMethod };
    }

    private void AddAdditionalDebugInfo(
        List<ValidationJson> results,
        PartnerCache? customer,
        List<ExtractedCompletionCert>? completionCerts,
        PartnerCache? vendor,
        List<string>? extractedBankAccounts,
        List<string>? bankAccountVariations,
        List<(ContractEntityData contract, string tigId)>? contracts,
        NavInvoiceData? navInvoiceData
    )
    {
        if (vendor != null)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "Eladó BC-ből (DEBUG)",
                    ValueOnly = JsonSerializer.Serialize(vendor, _jsonOptions),
                }
            );
        }

        if (customer != null)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "Vevő BC-ből (DEBUG)",
                    ValueOnly = JsonSerializer.Serialize(customer, _jsonOptions),
                }
            );
        }

        if (completionCerts != null && completionCerts.Count > 0)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "Számlához tartozó TIG-ek (DEBUG)",
                    ValueOnly = JsonSerializer.Serialize(completionCerts, _jsonOptions),
                }
            );
        }

        if (contracts != null && contracts.Count > 0)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "Adókulcs ellenőrzésben részt vett szerződések (DEBUG)",
                    Status = ColumnStatus.Ok,
                    ValueOnly = JsonSerializer.Serialize(contracts, _jsonOptions),
                }
            );
        }

        if (extractedBankAccounts != null && extractedBankAccounts.Count > 0)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "Kivont számlaszámok (DEBUG)",
                    ValueOnly = JsonSerializer.Serialize(extractedBankAccounts, _jsonOptions),
                }
            );
        }

        if (bankAccountVariations != null && bankAccountVariations.Count > 0)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "Kivont számlaszámok változatai (DEBUG)",
                    ValueOnly = JsonSerializer.Serialize(bankAccountVariations, _jsonOptions),
                }
            );
        }

        if (navInvoiceData != null)
        {
            results.Add(
                new ValidationJson
                {
                    EloColumnName = "NAV számla (DEBUG)",
                    ValueOnly = JsonSerializer.Serialize(navInvoiceData, _jsonOptions),
                }
            );
        }
    }

    private ValidationJson? ValidateLegalInfo(List<PatternMatchResult>? patternMatches)
    {
        if (patternMatches == null)
        {
            return null;
        }

        return new ValidationJson
        {
            EloColumnName = ELOFieldNames.LegalInfo,
            ValueOnly = JsonSerializer.Serialize(patternMatches, _jsonOptions),
        };
    }

    /// <summary>
    /// Validates if the invoice is a collection invoice (gyűjtőszámla) and returns appropriate status
    /// </summary>
    /// <param name="isCollectionInvoice">True if the invoice is identified as a collection invoice</param>
    /// <returns>ValidationJson with Info status if collection invoice</returns>
    private static ValidationJson? ValidateCollectionInvoice(bool isCollectionInvoice)
    {
        if (isCollectionInvoice)
        {
            return new ValidationJson
            {
                EloColumnName = ELOFieldNames.CollectionInvoice,
                Status = ColumnStatus.Info,
                ValueOnly = "true",
            };
        }

        return null;
    }
}

using System.Globalization;
using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing.Validation;

public abstract class BaseDocumentValidator : IDocumentValidator
{
    protected readonly ILogger logger;

    public BaseDocumentValidator(ILogger logger)
    {
        this.logger = logger;
    }

    public abstract Task ValidateDocument(
        AnalyzeResult analysisResult,
        DocumentValidationResult documentValidation,
        List<ValidationJson> results
    );

    protected static ValidationJson CompareFieldValue(string fieldName, string? bcValue, string? azureValue)
    {
        if (string.IsNullOrEmpty(bcValue) && string.IsNullOrEmpty(azureValue))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Missing,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        if (string.IsNullOrEmpty(bcValue))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Missing,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        if (string.IsNullOrEmpty(azureValue))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Missing,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        var normalizedBcValue = bcValue.Trim().ToLowerInvariant();
        var normalizedDocValue = azureValue.Trim().ToLowerInvariant();

        if (normalizedBcValue == normalizedDocValue)
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Ok,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        return new ValidationJson
        {
            EloColumnName = fieldName,
            Status = ColumnStatus.Discrepancy,
            BcColumnValue = bcValue,
            DocumentAiColumnValue = azureValue,
        };
    }

    protected static ValidationJson FuzzyCompareFieldValue(
        string fieldName,
        string? bcValue,
        string? azureValue,
        double threshold = 0.9
    )
    {
        if (string.IsNullOrEmpty(bcValue) && string.IsNullOrEmpty(azureValue))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Missing,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        if (string.IsNullOrEmpty(bcValue))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Missing,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        if (string.IsNullOrEmpty(azureValue))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Missing,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        if (TextComparer.AreFuzzyEqual(bcValue, azureValue, threshold))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Ok,
                BcColumnValue = bcValue,
                DocumentAiColumnValue = azureValue,
            };
        }

        return new ValidationJson
        {
            EloColumnName = fieldName,
            Status = ColumnStatus.Discrepancy,
            BcColumnValue = bcValue,
            DocumentAiColumnValue = azureValue,
        };
    }

    protected static ValidationJson ValidateStringFieldExistence(string fieldName, string? extractedValue)
    {
        if (string.IsNullOrEmpty(extractedValue))
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Missing,
                DocumentAiColumnValue = null,
            };
        }

        return new ValidationJson
        {
            EloColumnName = fieldName,
            Status = ColumnStatus.Ok,
            DocumentAiColumnValue = extractedValue,
        };
    }

    protected static ValidationJson ValidateDateFieldExistence(string fieldName, DateTime? dateValue)
    {
        if (dateValue != null)
        {
            return new ValidationJson
            {
                EloColumnName = fieldName,
                Status = ColumnStatus.Ok,
                DocumentAiColumnValue = dateValue.Value.ToString("yyyyMMdd"),
            };
        }

        return new ValidationJson { EloColumnName = fieldName, Status = ColumnStatus.Missing };
    }

    protected static List<ValidationJson> ValidateAmountAndCurrencyFieldExistence(
        string amountFieldName,
        string currencyFieldName,
        decimal? extractedAmount,
        string? extractedCurrency
    )
    {
        var result = new List<ValidationJson>
        {
            new()
            {
                EloColumnName = amountFieldName,
                Status = extractedAmount.HasValue ? ColumnStatus.Ok : ColumnStatus.Missing,
                DocumentAiColumnValue = extractedAmount?.ToString(CultureInfo.InvariantCulture),
            },
            new()
            {
                EloColumnName = currencyFieldName,
                Status = string.IsNullOrEmpty(extractedCurrency) ? ColumnStatus.Missing : ColumnStatus.Ok,
                DocumentAiColumnValue = extractedCurrency,
            },
        };

        return result;
    }
}

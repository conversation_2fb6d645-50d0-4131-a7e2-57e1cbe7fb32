using System.Collections.Concurrent;
using Azure;
using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Utils;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public class FakeAzureApiService : IAzureApiService
{
    private readonly ILogger<FakeAzureApiService> _logger;
    private readonly Random _random = new();
    private readonly int _minLatencyMs = 2000;
    private readonly int _maxLatencyMs = 4500;

    // Custom rate limiter implementation
    private readonly ConcurrentQueue<DateTime> _requestTimestamps = new();
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private readonly int _maxRequestsPerSecond = 15;

    private readonly string _responsesFilePath;
    private readonly string _testResourcesPath;

    private readonly string _completionCertModelId;

    public FakeAzureApiService(ILogger<FakeAzureApiService> logger, IConfiguration config)
    {
        _logger = logger;
        var baseDirectory = AppContext.BaseDirectory;
        _testResourcesPath = Path.Combine(baseDirectory, "TestResources");

        _responsesFilePath = config.GetConfigValue<string>("AzureAI:ResponsesPath");
        _completionCertModelId = config.GetConfigValue<string>("AzureAI:CompletionCertModelId");
    }

    private async Task<bool> CheckRateLimitAsync()
    {
        try
        {
            await _semaphore.WaitAsync();

            // Remove timestamps older than 1 second
            var now = DateTime.UtcNow;
            var cutoff = now.AddSeconds(-1);

            while (_requestTimestamps.TryPeek(out var oldestTimestamp) && oldestTimestamp < cutoff)
            {
                _requestTimestamps.TryDequeue(out _);
            }

            // Check if adding a new request would exceed the limit
            if (_requestTimestamps.Count < _maxRequestsPerSecond)
            {
                _requestTimestamps.Enqueue(now);
                return true;
            }

            return false;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task<AnalyzeResult> AnalyzeLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        CancellationToken cancellationToken = default
    )
    {
        _logger.LogInformation("FakeAzureApiService analyzing document: {DocumentId}", documentId);

        if (!await CheckRateLimitAsync())
        {
            throw new RequestFailedException(
                429,
                "Rate limit exceeded. Try again in 1 seconds.",
                "RateLimitExceeded",
                new Exception("DocumentIntelligence service rate limit of 15 requests per second exceeded.")
            );
        }

        // Simulate network latency
        int latency = _random.Next(_minLatencyMs, _maxLatencyMs);
        await Task.Delay(latency, cancellationToken);

        try
        {
            string filePath;
            if (modelId == "prebuilt-invoice")
                filePath = Path.Combine(_testResourcesPath, "sample-invoice-response.json");
            else if (modelId == _completionCertModelId)
                filePath = Path.Combine(_testResourcesPath, "sample-completion-cert-response.json");
            else
                throw new Exception($"Unsupported modelId: {modelId}");

            var analyzeResult = await FileUtility.GetAnalyzeResult(filePath);
            CopyFakeResponseToFile(filePath, contextObjectId, storedName);

            return analyzeResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating mock AnalyzeResult");
            throw;
        }
    }

    public async Task<AnalyzeResult> ClassifyLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        SplitMode splitMode,
        CancellationToken cancellationToken = default
    )
    {
        _logger.LogInformation("FakeAzureApiService analyzing document: {DocumentId}", documentId);

        if (!await CheckRateLimitAsync())
        {
            throw new RequestFailedException(
                429,
                "Rate limit exceeded. Try again in 1 seconds.",
                "RateLimitExceeded",
                new Exception("DocumentIntelligence service rate limit of 15 requests per second exceeded.")
            );
        }

        // Simulate network latency
        int latency = _random.Next(_minLatencyMs, _maxLatencyMs);
        await Task.Delay(latency, cancellationToken);

        try
        {
            string filePath;

            filePath = Path.Combine(_testResourcesPath, "choice_2.json");

            var analyzeResult = await FileUtility.GetAnalyzeResult(filePath);
            CopyFakeResponseToFile(filePath, contextObjectId, storedName);

            return analyzeResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating mock AnalyzeResult");
            throw;
        }
    }

    private void CopyFakeResponseToFile(string responseFilePath, string contextObjectId, string storedName)
    {
        var filePath = FileUtility.DocumentResponseFilePath(_responsesFilePath, contextObjectId, storedName);
        File.Copy(responseFilePath, filePath, overwrite: true);
        _logger.LogInformation("Copied fake response to {FilePath}", filePath);
    }

    public async Task<AnalyzeResult> SplitAndAnalyzeLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        int startPage,
        int endPage,
        CancellationToken cancellationToken = default
    )
    {
        _logger.LogInformation("FakeAzureApiService analyzing document: {DocumentId}", documentId);

        if (!await CheckRateLimitAsync())
        {
            throw new RequestFailedException(
                429,
                "Rate limit exceeded. Try again in 1 seconds.",
                "RateLimitExceeded",
                new Exception("DocumentIntelligence service rate limit of 15 requests per second exceeded.")
            );
        }

        // Simulate network latency
        int latency = _random.Next(_minLatencyMs, _maxLatencyMs);
        await Task.Delay(latency, cancellationToken);

        try
        {
            string filePath;
            if (modelId == "prebuilt-invoice")
                filePath = Path.Combine(_testResourcesPath, "sample-invoice-response.json");
            else if (modelId == "TIG2")
                filePath = Path.Combine(_testResourcesPath, "sample-completion-cert-response.json");
            else
                throw new Exception($"Unsupported modelId: {modelId}");

            var analyzeResult = await FileUtility.GetAnalyzeResult(filePath);

            CopyFakeResponseToFile(
                filePath,
                contextObjectId,
                FileUtility.GetClassificationStoredName(storedName, startPage, endPage)
            );

            return analyzeResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating mock AnalyzeResult");
            throw;
        }
    }
}

using Azure;
using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Utils;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public class AzureApiService : IAzureApiService
{
    private readonly DocumentIntelligenceClient _client;
    private readonly ILogger<AzureApiService> _logger;
    private readonly string _documentsFilePath;
    private readonly string _responsesFilePath;

    public AzureApiService(ILogger<AzureApiService> logger, IConfiguration config)
    {
        var endpoint = config.GetConfigValue<string>("AzureAI:DocumentIntelligence:Endpoint");
        var key = config.GetConfigValue<string>("AzureAI:DocumentIntelligence:Key");

        var credential = new AzureKeyCredential(key);
        _client = new DocumentIntelligenceClient(new Uri(endpoint), credential);
        _logger = logger;

        _documentsFilePath = config.GetConfigValue<string>("DocumentModule:FilesPath");
        _responsesFilePath = config.GetConfigValue<string>("AzureAI:ResponsesPath");
    }

    public async Task<AnalyzeResult> AnalyzeLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        CancellationToken cancellationToken = default
    )
    {
        var fileContent = await GetFileContent(contextObjectId, storedName, cancellationToken);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogInformation(
                "Starting local document analysis for document: {DocumentId} model: {ModelId}",
                documentId,
                modelId
            );

            // Analyze the document using the file content
            Operation<AnalyzeResult> operation = await _client.AnalyzeDocumentAsync(
                WaitUntil.Completed,
                modelId,
                fileContent,
                cancellationToken: cancellationToken
            );

            stopwatch.Stop();
            _logger.LogInformation(
                "Local document analysis completed for document: {DocumentId} model: {ModelId}. Processing time: {ElapsedMs}ms",
                documentId,
                modelId,
                stopwatch.ElapsedMilliseconds
            );

            await SaveResponseToFile(operation, contextObjectId, storedName);

            return operation.Value;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Error analyzing local document: {DocumentId} model: {ModelId}. Time elapsed before error: {ElapsedMs}ms",
                documentId,
                modelId,
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }

    public async Task<AnalyzeResult> ClassifyLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        SplitMode splitMode,
        CancellationToken cancellationToken = default
    )
    {
        var fileContent = await GetFileContent(contextObjectId, storedName, cancellationToken);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogInformation(
                "Starting local document classification for document: {DocumentId} model: {ModelId}",
                documentId,
                modelId
            );
            var options = new ClassifyDocumentOptions(modelId, fileContent) { Split = splitMode };

            Operation<AnalyzeResult> operation = await _client.ClassifyDocumentAsync(
                WaitUntil.Completed,
                options,
                cancellationToken
            );

            stopwatch.Stop();
            _logger.LogInformation(
                "Local document classification completed for document: {DocumentId} model: {ModelId}. Processing time: {ElapsedMs}ms",
                documentId,
                modelId,
                stopwatch.ElapsedMilliseconds
            );

            await SaveResponseToFile(operation, contextObjectId, storedName);

            return operation.Value;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Error classifying local document: {DocumentId} model: {ModelId}. Time elapsed before error: {ElapsedMs}ms",
                documentId,
                modelId,
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }

    public async Task<AnalyzeResult> SplitAndAnalyzeLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        int startPage,
        int endPage,
        CancellationToken cancellationToken = default
    )
    {
        var filePath = FileUtility.GetFilePath(_documentsFilePath, contextObjectId, storedName);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            var pdfBytes = await PdfSplitHelper.ExtractPagesAsync(filePath, startPage, endPage);
            _logger.LogInformation(
                "Starting split document analysis for document: {DocumentId} model: {ModelId} startPage: {StartPage} endPage: {EndPage}",
                documentId,
                modelId,
                startPage,
                endPage
            );

            var operation = await _client.AnalyzeDocumentAsync(
                WaitUntil.Completed,
                modelId,
                pdfBytes,
                cancellationToken: cancellationToken
            );

            stopwatch.Stop();

            _logger.LogInformation(
                "Split document analysis completed for document: {DocumentId} model: {ModelId}. Processing time: {ElapsedMs}ms",
                documentId,
                modelId,
                stopwatch.ElapsedMilliseconds
            );

            await SaveResponseToFile(
                operation,
                contextObjectId,
                FileUtility.GetClassificationStoredName(storedName, startPage, endPage)
            );

            return operation.Value;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Error analyzing split document: {DocumentId} model: {ModelId}. Time elapsed before error: {ElapsedMs}ms",
                documentId,
                modelId,
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }

    private async Task<BinaryData> GetFileContent(
        string contextObjectId,
        string storedName,
        CancellationToken cancellationToken
    )
    {
        var filePath = FileUtility.GetFilePath(_documentsFilePath, contextObjectId, storedName);
        try
        {
            return await BinaryData.FromStreamAsync(File.OpenRead(filePath), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file from {FilePath}", filePath);
            throw;
        }
    }

    private async Task SaveResponseToFile(Operation<AnalyzeResult> operation, string contextObjectId, string storedName)
    {
        var filePath = FileUtility.DocumentResponseFilePath(_responsesFilePath, contextObjectId, storedName);
        _logger.LogDebug("Saving response to: {FilePath}", filePath);

        var stream = operation.GetRawResponse().ContentStream;
        if (stream != null)
        {
            using var sr = new StreamReader(stream);
            string j = sr.ReadToEnd();
            await File.WriteAllTextAsync(filePath, j);
        }
        else
        {
            _logger.LogWarning("ContentStream was null, unable to save raw response");
        }
    }
}

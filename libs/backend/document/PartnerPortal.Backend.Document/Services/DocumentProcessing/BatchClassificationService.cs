using Azure.AI.DocumentIntelligence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using Polly.Timeout;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IBatchClassificationService
{
    Task<JobOperationResult> ClassifyDocument(DocumentJobInputData inputData, CancellationToken cancellationToken);
}

public class BatchClassificationService : IBatchClassificationService
{
    private readonly UserDbContext _context;
    private readonly IAzureApiService _azureApiService;
    private readonly ILogger<BatchClassificationService> _logger;
    private readonly TiffAttachmentConvertService _tiffAttachmentConvertService;
    private readonly string _classificationModelId;

    public BatchClassificationService(
        UserDbContext context,
        IAzureApiService azureApiService,
        ILogger<BatchClassificationService> logger,
        TiffAttachmentConvertService tiffAttachmentConvertService,
        IConfiguration configuration
    )
    {
        _context = context;
        _azureApiService = azureApiService;
        _logger = logger;
        _tiffAttachmentConvertService = tiffAttachmentConvertService;
        _classificationModelId = configuration.GetConfigValue<string>("AzureAI:ClassificationModelId");
    }

    private static void AddOrMergeOther(List<DocumentClassification> classifications, DocumentClassification next)
    {
        if (classifications.Count > 0)
        {
            var last = classifications[classifications.Count - 1];
            if (
                next.DocumentType == DocumentType.Other
                && last.DocumentType == DocumentType.Other
                && last.EndPage + 1 == next.StartPage
            )
            {
                last.EndPage = next.EndPage;
                last.Confidence = (last.Confidence + next.Confidence) / 2;
                return;
            }
        }

        classifications.Add(next);
    }

    public async Task<JobOperationResult> ClassifyDocument(
        DocumentJobInputData inputData,
        CancellationToken cancellationToken
    )
    {
        DocumentClassificationResult? documentClassification = null;

        try
        {
            _logger.LogInformation(
                "Getting document classification for attachment {AttachmentDocumentTypeId} for document upload {DocumentUploadId}",
                inputData.ItemId,
                inputData.DocumentUploadId
            );
            documentClassification = await GetDocumentClassification(inputData, cancellationToken);
            if (documentClassification == null)
            {
                await SetDocumentClassificationStatus(
                    documentClassification!,
                    DocumentClassificationStatusEnum.Failed,
                    "Document classification not found",
                    cancellationToken
                );

                return new JobOperationResult { Status = JobOperationStatus.Fatal };
            }

            await SetDocumentClassificationStatus(
                documentClassification,
                DocumentClassificationStatusEnum.Processing,
                null,
                cancellationToken
            );

            if (documentClassification.Attachment.MimeType == "image/tiff")
            {
                _logger.LogDebug(
                    "Converting TIFF attachment {AttachmentDocumentTypeId} to PDF for document upload {DocumentUploadId}",
                    documentClassification.AttachmentId,
                    inputData.DocumentUploadId
                );
                await _tiffAttachmentConvertService.ConvertTiffAttachmentToPdf(
                    documentClassification.AttachmentId,
                    cancellationToken
                );
            }

            await AzureApiRateLimiter.SpaceEvenly(
                async () =>
                    await AzureApiRateLimiter.ResiliencePipeline.ExecuteAsync(
                        async token => await ProcessClassification(documentClassification, token),
                        cancellationToken
                    ),
                _logger
            );

            await SetDocumentClassificationStatus(
                documentClassification,
                DocumentClassificationStatusEnum.Success,
                null,
                cancellationToken
            );

            return new JobOperationResult { Status = JobOperationStatus.Success };
        }
        catch (TimeoutRejectedException ex)
        {
            _logger.LogWarning(
                ex,
                "Timeout exceeded for attachment {AttachmentDocumentTypeId} for document upload {DocumentUploadId}",
                inputData.ItemId,
                inputData.DocumentUploadId
            );

            await SetDocumentClassificationStatus(
                documentClassification!,
                DocumentClassificationStatusEnum.Timeout,
                "Operation timed out",
                cancellationToken
            );

            return new JobOperationResult { Status = JobOperationStatus.Timeout };
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error processing attachment {AttachmentDocumentTypeId} for document upload {DocumentUploadId}",
                inputData.ItemId,
                inputData.DocumentUploadId
            );

            await SetDocumentClassificationStatus(
                documentClassification!,
                DocumentClassificationStatusEnum.Failed,
                ex.ToString(),
                cancellationToken
            );

            return new JobOperationResult { Status = JobOperationStatus.Error };
        }
    }

    private async Task<DocumentClassificationResult?> GetDocumentClassification(
        DocumentJobInputData inputData,
        CancellationToken cancellationToken
    )
    {
        return await _context
            .DocumentClassificationResults.Include(dc => dc.Attachment)
            .Include(dc => dc.DocumentUpload)
            .AsSplitQuery()
            .FirstOrDefaultAsync(
                dc => dc.DocumentUploadId == inputData.DocumentUploadId && dc.AttachmentId == inputData.ItemId,
                cancellationToken
            );
    }

    private async Task SetDocumentClassificationStatus(
        DocumentClassificationResult documentClassification,
        DocumentClassificationStatusEnum status,
        string? errorMessage = null,
        CancellationToken cancellationToken = default
    )
    {
        documentClassification.Status = status;
        documentClassification.SetErrorMessage(errorMessage);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Document classification {DocumentClassificationId} status updated to {Status}",
            documentClassification.Id,
            status
        );

        if (errorMessage != null)
        {
            _logger.LogWarning(
                "Document classification {DocumentClassificationId} failed with error message: {ErrorMessage}",
                documentClassification.Id,
                errorMessage
            );
        }
    }

    private async Task ProcessClassification(
        DocumentClassificationResult documentClassificationResult,
        CancellationToken cancellationToken
    )
    {
        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        try
        {
            _logger.LogDebug(
                "Starting document classification for attachment {AttachmentDocumentTypeId} for document upload {DocumentUploadId}",
                documentClassificationResult.AttachmentId,
                documentClassificationResult.DocumentUploadId
            );
            var result = await _azureApiService.ClassifyLocalDocumentAsync(
                documentClassificationResult.AttachmentId.ToString(),
                documentClassificationResult.Attachment.Context,
                documentClassificationResult.Attachment.StoredName,
                _classificationModelId,
                SplitMode.Auto,
                cancellationToken
            );

            List<DocumentClassification> documentClassifications = [];
            var earliestInvoiceAssigned = false;

            foreach (var doc in result.Documents)
            {
                var documentType = doc.DocumentType switch
                {
                    "szamla" => DocumentType.Invoice,
                    "TIG" => DocumentType.CompletionCert,
                    "szallitolevel" => DocumentType.DeliveryNote,
                    _ => DocumentType.Other,
                };

                if (documentType == DocumentType.Invoice)
                {
                    if (!earliestInvoiceAssigned)
                    {
                        earliestInvoiceAssigned = true;
                    }
                    else
                    {
                        documentType = DocumentType.Other;
                    }
                }

                var startPage = doc.BoundingRegions[0].PageNumber;
                var endPage = doc.BoundingRegions[doc.BoundingRegions.Count - 1].PageNumber;

                var documentClassification = new DocumentClassification
                {
                    SecretaryDocumentUploadId = documentClassificationResult.DocumentUploadId,
                    AttachmentId = documentClassificationResult.AttachmentId,
                    StartPage = startPage,
                    EndPage = endPage,
                    DocumentType = documentType,
                    Confidence = doc.Confidence,
                };

                AddOrMergeOther(documentClassifications, documentClassification);
            }

            _logger.LogDebug(
                "Adding {DocumentClassificationCount} document classifications for attachment {AttachmentDocumentTypeId} for document upload {DocumentUploadId}",
                documentClassifications.Count,
                documentClassificationResult.AttachmentId,
                documentClassificationResult.DocumentUploadId
            );

            await _context.DocumentClassifications.AddRangeAsync(documentClassifications, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            await transaction.CommitAsync(cancellationToken);
        }
        catch (Exception)
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using Polly.Timeout;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IDocumentAnalysisService
{
    Task<JobOperationResult> AnalyzeDocument(DocumentJobInputData inputData, CancellationToken cancellationToken);
}

public class DocumentAnalysisService : IDocumentAnalysisService
{
    private readonly UserDbContext _context;
    private readonly IAzureApiService _azureApiService;
    private readonly ILogger<DocumentAnalysisService> _logger;
    private readonly TiffAttachmentConvertService _tiffAttachmentConvertService;
    private readonly string _completionCertModelId;

    public DocumentAnalysisService(
        UserDbContext context,
        IAzureApiService azureApiService,
        ILogger<DocumentAnalysisService> logger,
        TiffAttachmentConvertService tiffAttachmentConvertService,
        IConfiguration configuration
    )
    {
        _context = context;
        _azureApiService = azureApiService;
        _logger = logger;
        _tiffAttachmentConvertService = tiffAttachmentConvertService;
        _completionCertModelId = configuration.GetConfigValue<string>("AzureAI:CompletionCertModelId");
    }

    public async Task<JobOperationResult> AnalyzeDocument(
        DocumentJobInputData inputData,
        CancellationToken cancellationToken
    )
    {
        DocumentAnalysisResult? documentAnalysis = null;

        try
        {
            documentAnalysis = await GetDocumentAnalysis(inputData, cancellationToken);
            if (documentAnalysis == null)
            {
                await SetDocumentAnalysisStatus(
                    documentAnalysis!,
                    DocumentAnalysisStatusEnum.Failed,
                    "Document analysis not found",
                    cancellationToken
                );

                return new JobOperationResult { Status = JobOperationStatus.Fatal };
            }

            await SetDocumentAnalysisStatus(
                documentAnalysis,
                DocumentAnalysisStatusEnum.Processing,
                null,
                cancellationToken
            );

            await AzureApiRateLimiter.SpaceEvenly(
                async () =>
                    await AzureApiRateLimiter.ResiliencePipeline.ExecuteAsync(
                        async token => await ProcessDocument(documentAnalysis, token),
                        cancellationToken
                    ),
                _logger
            );

            await SetDocumentAnalysisStatus(
                documentAnalysis,
                DocumentAnalysisStatusEnum.Success,
                null,
                cancellationToken
            );

            return new JobOperationResult { Status = JobOperationStatus.Success };
        }
        catch (TimeoutRejectedException ex)
        {
            _logger.LogWarning(ex, "Timeout exceeded for document {AttachmentDocumentTypeId}", inputData.ItemId);

            await SetDocumentAnalysisStatus(
                documentAnalysis!,
                DocumentAnalysisStatusEnum.Timeout,
                "Operation timed out",
                cancellationToken
            );

            return new JobOperationResult { Status = JobOperationStatus.Timeout };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document {AttachmentDocumentTypeId}", inputData.ItemId);

            await SetDocumentAnalysisStatus(
                documentAnalysis!,
                DocumentAnalysisStatusEnum.Failed,
                ex.ToString(),
                cancellationToken
            );

            return new JobOperationResult { Status = JobOperationStatus.Error };
        }
    }

    private async Task<DocumentAnalysisResult?> GetDocumentAnalysis(
        DocumentJobInputData inputData,
        CancellationToken cancellationToken
    )
    {
        return await _context
            .DocumentAnalysisResults.Include(da => da.DocumentUpload)
            .Include(da => da.ProcessingSource)
            .ThenInclude(ad => ad.Attachment)
            .AsSplitQuery()
            .FirstOrDefaultAsync(
                da => da.ProcessingSourceId == inputData.ItemId && da.DocumentUploadId == inputData.DocumentUploadId,
                cancellationToken
            );
    }

    private async Task SetDocumentAnalysisStatus(
        DocumentAnalysisResult documentAnalysis,
        DocumentAnalysisStatusEnum status,
        string? errorMessage = null,
        CancellationToken cancellationToken = default
    )
    {
        documentAnalysis.Status = status;
        documentAnalysis.SetErrorMessage(errorMessage);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Document analysis {DocumentAnalysisId} status updated to {Status}",
            documentAnalysis.Id,
            status
        );

        if (errorMessage != null)
        {
            _logger.LogWarning(
                "Document analysis {DocumentAnalysisId} failed with error message: {ErrorMessage}",
                documentAnalysis.Id,
                errorMessage
            );
        }
    }

    private async Task ProcessDocument(DocumentAnalysisResult documentAnalysis, CancellationToken cancellationToken)
    {
        if (documentAnalysis.DocumentUpload is SecretaryDocumentUpload)
        {
            await AnalyzeSecretaryDocument(documentAnalysis, cancellationToken);
        }
        else
        {
            if (documentAnalysis.ProcessingSource.Attachment.MimeType == "image/tiff")
            {
                _logger.LogDebug(
                    "Converting partner document {AttachmentDocumentTypeId} to PDF for document upload {DocumentUploadId}",
                    documentAnalysis.ProcessingSource.AttachmentId,
                    documentAnalysis.DocumentUploadId
                );
                await _tiffAttachmentConvertService.ConvertTiffAttachmentToPdf(
                    documentAnalysis.ProcessingSource.AttachmentId,
                    cancellationToken
                );
            }

            await AnalyzeManualDocument(documentAnalysis, cancellationToken);
        }
    }

    private async Task AnalyzeSecretaryDocument(
        DocumentAnalysisResult documentAnalysis,
        CancellationToken cancellationToken
    )
    {
        if (documentAnalysis.ProcessingSource is DocumentClassification classification)
        {
            var modelId = classification.IsInvoiceType ? "prebuilt-invoice" : _completionCertModelId;
            var documentTypeLog = classification.IsInvoiceType ? "invoice" : "completion certificate";

            _logger.LogDebug(
                "Analyzing secretary {DocumentType} {DocumentId}",
                documentTypeLog,
                documentAnalysis.ProcessingSourceId
            );

            if (classification.SecretaryDocumentUpload.UploadType == SecretaryUploadTypeEnum.Batch) { }

            await _azureApiService.SplitAndAnalyzeLocalDocumentAsync(
                documentAnalysis.ProcessingSourceId.ToString(),
                classification.Attachment.Context,
                classification.Attachment.StoredName,
                modelId,
                classification.StartPage,
                classification.EndPage,
                cancellationToken
            );
        }
        else if (documentAnalysis.ProcessingSource is AttachmentDocumentType)
        {
            await AnalyzeManualDocument(documentAnalysis, cancellationToken);
        }
    }

    private async Task AnalyzeManualDocument(
        DocumentAnalysisResult documentAnalysis,
        CancellationToken cancellationToken
    )
    {
        var modelId = documentAnalysis.ProcessingSource.IsInvoiceType ? "prebuilt-invoice" : _completionCertModelId;
        var documentTypeLog = documentAnalysis.ProcessingSource.IsInvoiceType ? "invoice" : "completion certificate";

        _logger.LogDebug(
            "Analyzing partner {DocumentType} {DocumentId}",
            documentTypeLog,
            documentAnalysis.ProcessingSourceId
        );

        await _azureApiService.AnalyzeLocalDocumentAsync(
            documentAnalysis.ProcessingSourceId.ToString(),
            documentAnalysis.ProcessingSource.Attachment.Context,
            documentAnalysis.ProcessingSource.Attachment.StoredName,
            modelId,
            cancellationToken
        );
    }
}

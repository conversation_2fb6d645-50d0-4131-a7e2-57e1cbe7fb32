using Microsoft.EntityFrameworkCore;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IDocumentProcessingStatusService
{
    // In-memory aggregate status for Partner documents (manual-like pipeline)
    ProcessingStatusEnum CalculateOverallStatusForPartner(
        List<DocumentAnalysisResult> analysisResults,
        List<DocumentValidationResult> validationResults,
        List<DocumentEloResult> eloResults
    );

    // In-memory aggregate status for Secretary documents (classification only when required)
    ProcessingStatusEnum CalculateOverallStatusForSecretary(
        List<DocumentAnalysisResult> analysisResults,
        List<DocumentValidationResult> validationResults,
        List<DocumentEloResult> eloResults,
        List<DocumentClassificationResult> classificationResults,
        SecretaryUploadTypeEnum uploadType
    );

    // Per-item (attachment/classification) processing status and failure reason for details views
    (EloProcessingStatusEnum ProcessingStatus, string FailureReason) CalculatePerItemProcessingStatus(
        DocumentEloResult? eloResult,
        DocumentValidationResult? validationResult,
        DocumentAnalysisResult? analysisResult,
        DocumentClassificationResult? classificationResult
    );

    // EF-translatable predicates for database-side filtering by ProcessingStatus
    IQueryable<PartnerDocumentUpload> ApplyProcessingStatusFilterForPartner(
        IQueryable<PartnerDocumentUpload> query,
        ProcessingStatusEnum targetStatus
    );

    IQueryable<SecretaryDocumentUpload> ApplyProcessingStatusFilterForSecretary(
        IQueryable<SecretaryDocumentUpload> query,
        ProcessingStatusEnum targetStatus
    );
}

public class DocumentProcessingStatusService : IDocumentProcessingStatusService
{
    public ProcessingStatusEnum CalculateOverallStatusForPartner(
        List<DocumentAnalysisResult> analysisResults,
        List<DocumentValidationResult> validationResults,
        List<DocumentEloResult> eloResults
    )
    {
        if (analysisResults.Count == 0 && validationResults.Count == 0 && eloResults.Count == 0)
        {
            return ProcessingStatusEnum.Processing;
        }

        bool hasFailed = false;
        bool hasProcessing = false;
        bool hasSuccess = false;

        foreach (var result in analysisResults)
        {
            if (
                result.Status == DocumentAnalysisStatusEnum.Failed
                || result.Status == DocumentAnalysisStatusEnum.Timeout
            )
                hasFailed = true;
            else if (
                result.Status == DocumentAnalysisStatusEnum.Processing
                || result.Status == DocumentAnalysisStatusEnum.Pending
            )
                hasProcessing = true;
            else if (result.Status == DocumentAnalysisStatusEnum.Success)
                hasSuccess = true;
        }

        foreach (var result in validationResults)
        {
            if (
                result.Status == DocumentValidationStatusEnum.BcFailed
                || result.Status == DocumentValidationStatusEnum.TechnicalError
            )
                hasFailed = true;
            else if (
                result.Status == DocumentValidationStatusEnum.Processing
                || result.Status == DocumentValidationStatusEnum.Pending
            )
                hasProcessing = true;
            else if (result.Status == DocumentValidationStatusEnum.Success)
                hasSuccess = true;
        }

        foreach (var result in eloResults)
        {
            if (result.Status == DocumentEloStatusEnum.Failed)
                hasFailed = true;
            else if (
                result.Status == DocumentEloStatusEnum.Processing
                || result.Status == DocumentEloStatusEnum.Pending
            )
                hasProcessing = true;
            else if (result.Status == DocumentEloStatusEnum.Success)
                hasSuccess = true;
        }

        if (hasFailed)
            return ProcessingStatusEnum.Failed;
        if (hasProcessing)
            return ProcessingStatusEnum.Processing;
        if (hasSuccess && (analysisResults.Count + validationResults.Count + eloResults.Count) > 0)
            return ProcessingStatusEnum.Success;
        return ProcessingStatusEnum.Processing;
    }

    public ProcessingStatusEnum CalculateOverallStatusForSecretary(
        List<DocumentAnalysisResult> analysisResults,
        List<DocumentValidationResult> validationResults,
        List<DocumentEloResult> eloResults,
        List<DocumentClassificationResult> classificationResults,
        SecretaryUploadTypeEnum uploadType
    )
    {
        bool classificationRequired = uploadType != SecretaryUploadTypeEnum.Manual;

        if (classificationRequired)
        {
            if (classificationResults.Count == 0)
            {
                return ProcessingStatusEnum.Processing;
            }
        }

        bool hasFailed = false;
        bool hasProcessing = false;
        bool hasSuccess = false;

        foreach (var result in analysisResults)
        {
            if (
                result.Status == DocumentAnalysisStatusEnum.Failed
                || result.Status == DocumentAnalysisStatusEnum.Timeout
            )
                hasFailed = true;
            else if (
                result.Status == DocumentAnalysisStatusEnum.Processing
                || result.Status == DocumentAnalysisStatusEnum.Pending
            )
                hasProcessing = true;
            else if (result.Status == DocumentAnalysisStatusEnum.Success)
                hasSuccess = true;
        }

        foreach (var result in validationResults)
        {
            if (
                result.Status == DocumentValidationStatusEnum.BcFailed
                || result.Status == DocumentValidationStatusEnum.TechnicalError
            )
                hasFailed = true;
            else if (
                result.Status == DocumentValidationStatusEnum.Processing
                || result.Status == DocumentValidationStatusEnum.Pending
            )
                hasProcessing = true;
            else if (result.Status == DocumentValidationStatusEnum.Success)
                hasSuccess = true;
        }

        foreach (var result in eloResults)
        {
            if (result.Status == DocumentEloStatusEnum.Failed)
                hasFailed = true;
            else if (
                result.Status == DocumentEloStatusEnum.Processing
                || result.Status == DocumentEloStatusEnum.Pending
            )
                hasProcessing = true;
            else if (result.Status == DocumentEloStatusEnum.Success)
                hasSuccess = true;
        }

        if (classificationRequired)
        {
            foreach (var result in classificationResults)
            {
                if (
                    result.Status == DocumentClassificationStatusEnum.Failed
                    || result.Status == DocumentClassificationStatusEnum.Timeout
                )
                    hasFailed = true;
                else if (
                    result.Status == DocumentClassificationStatusEnum.Processing
                    || result.Status == DocumentClassificationStatusEnum.Pending
                )
                    hasProcessing = true;
                else if (result.Status == DocumentClassificationStatusEnum.Success)
                    hasSuccess = true;
            }
        }

        if (hasFailed)
            return ProcessingStatusEnum.Failed;
        if (hasProcessing)
            return ProcessingStatusEnum.Processing;
        if (hasSuccess)
            return ProcessingStatusEnum.Success;
        return ProcessingStatusEnum.Processing;
    }

    public (EloProcessingStatusEnum ProcessingStatus, string FailureReason) CalculatePerItemProcessingStatus(
        DocumentEloResult? eloResult,
        DocumentValidationResult? validationResult,
        DocumentAnalysisResult? analysisResult,
        DocumentClassificationResult? classificationResult
    )
    {
        if (
            eloResult?.Status == DocumentEloStatusEnum.Success
            && validationResult?.Status == DocumentValidationStatusEnum.Success
        )
        {
            return (EloProcessingStatusEnum.Success, string.Empty);
        }

        if (eloResult?.Status == DocumentEloStatusEnum.Failed)
        {
            var failureReason = ExtractMessageFromSoapXml(eloResult.CreateResponseXml);
            return (EloProcessingStatusEnum.Failed, failureReason);
        }

        const string GenericError = "Hiba történt a feldolgozás közben. A hiba naplózásra került.";
        const string TimeoutError =
            "A feldolgozás Azure AI szolgáltatás időtúllépés miatt meghiúsult. Kérjük, próbálja újra.";
        const string BcFailedError = "A feldolgozás a BC-nél meghiúsult. Kérjük, próbálja újra.";

        if (
            validationResult?.Status == DocumentValidationStatusEnum.BcFailed
            || validationResult?.Status == DocumentValidationStatusEnum.TechnicalError
        )
        {
            return (
                EloProcessingStatusEnum.Failed,
                validationResult.Status == DocumentValidationStatusEnum.BcFailed ? BcFailedError : GenericError
            );
        }

        if (
            analysisResult?.Status == DocumentAnalysisStatusEnum.Failed
            || analysisResult?.Status == DocumentAnalysisStatusEnum.Timeout
        )
        {
            return (
                EloProcessingStatusEnum.Failed,
                analysisResult.Status == DocumentAnalysisStatusEnum.Timeout ? TimeoutError : GenericError
            );
        }

        if (
            classificationResult?.Status == DocumentClassificationStatusEnum.Failed
            || classificationResult?.Status == DocumentClassificationStatusEnum.Timeout
        )
        {
            return (
                EloProcessingStatusEnum.Failed,
                classificationResult.Status == DocumentClassificationStatusEnum.Timeout ? TimeoutError : GenericError
            );
        }

        return (EloProcessingStatusEnum.Default, string.Empty);
    }

    public IQueryable<PartnerDocumentUpload> ApplyProcessingStatusFilterForPartner(
        IQueryable<PartnerDocumentUpload> query,
        ProcessingStatusEnum targetStatus
    )
    {
        return targetStatus switch
        {
            ProcessingStatusEnum.Failed => query.Where(d =>
                d.DocumentAnalysisResults.Any(r =>
                    r.Status == DocumentAnalysisStatusEnum.Failed || r.Status == DocumentAnalysisStatusEnum.Timeout
                )
                || d.DocumentValidationResults.Any(r =>
                    r.Status == DocumentValidationStatusEnum.BcFailed
                    || r.Status == DocumentValidationStatusEnum.TechnicalError
                )
                || d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Failed)
            ),
            ProcessingStatusEnum.Processing => query.Where(d =>
                !d.DocumentAnalysisResults.Any(r => r.Status == DocumentAnalysisStatusEnum.Failed)
                && !d.DocumentValidationResults.Any(r =>
                    r.Status == DocumentValidationStatusEnum.BcFailed
                    || r.Status == DocumentValidationStatusEnum.TechnicalError
                )
                && !d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Failed)
                && (
                    d.DocumentAnalysisResults.Any(r =>
                        r.Status == DocumentAnalysisStatusEnum.Processing
                        || r.Status == DocumentAnalysisStatusEnum.Pending
                    )
                    || d.DocumentValidationResults.Any(r =>
                        r.Status == DocumentValidationStatusEnum.Processing
                        || r.Status == DocumentValidationStatusEnum.Pending
                    )
                    || d.DocumentEloResults.Any(r =>
                        r.Status == DocumentEloStatusEnum.Processing || r.Status == DocumentEloStatusEnum.Pending
                    )
                    || (
                        d.DocumentAnalysisResults.Count == 0
                        && d.DocumentValidationResults.Count == 0
                        && d.DocumentEloResults.Count == 0
                    )
                )
            ),
            ProcessingStatusEnum.Success => query.Where(d =>
                !d.DocumentAnalysisResults.Any(r => r.Status == DocumentAnalysisStatusEnum.Failed)
                && !d.DocumentValidationResults.Any(r =>
                    r.Status == DocumentValidationStatusEnum.BcFailed
                    || r.Status == DocumentValidationStatusEnum.TechnicalError
                )
                && !d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Failed)
                && !d.DocumentAnalysisResults.Any(r =>
                    r.Status == DocumentAnalysisStatusEnum.Processing || r.Status == DocumentAnalysisStatusEnum.Pending
                )
                && !d.DocumentValidationResults.Any(r =>
                    r.Status == DocumentValidationStatusEnum.Processing
                    || r.Status == DocumentValidationStatusEnum.Pending
                )
                && !d.DocumentEloResults.Any(r =>
                    r.Status == DocumentEloStatusEnum.Processing || r.Status == DocumentEloStatusEnum.Pending
                )
                && (
                    d.DocumentAnalysisResults.Any(r => r.Status == DocumentAnalysisStatusEnum.Success)
                    || d.DocumentValidationResults.Any(r => r.Status == DocumentValidationStatusEnum.Success)
                    || d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Success)
                )
                && (
                    d.DocumentAnalysisResults.Count != 0
                    || d.DocumentValidationResults.Count != 0
                    || d.DocumentEloResults.Count != 0
                )
            ),
            _ => query,
        };
    }

    public IQueryable<SecretaryDocumentUpload> ApplyProcessingStatusFilterForSecretary(
        IQueryable<SecretaryDocumentUpload> query,
        ProcessingStatusEnum targetStatus
    )
    {
        return targetStatus switch
        {
            ProcessingStatusEnum.Failed => query.Where(d =>
                (
                    d.DocumentAnalysisResults.Any(r =>
                        r.Status == DocumentAnalysisStatusEnum.Failed || r.Status == DocumentAnalysisStatusEnum.Timeout
                    )
                    || d.DocumentValidationResults.Any(r =>
                        r.Status == DocumentValidationStatusEnum.BcFailed
                        || r.Status == DocumentValidationStatusEnum.TechnicalError
                    )
                    || (
                        d.UploadType != SecretaryUploadTypeEnum.Manual
                        && d.DocumentClassificationResults.Any(r =>
                            r.Status == DocumentClassificationStatusEnum.Failed
                            || r.Status == DocumentClassificationStatusEnum.Timeout
                        )
                    )
                    || d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Failed)
                )
            ),
            ProcessingStatusEnum.Processing => query.Where(d =>
                !d.DocumentAnalysisResults.Any(r => r.Status == DocumentAnalysisStatusEnum.Failed)
                && !d.DocumentValidationResults.Any(r =>
                    r.Status == DocumentValidationStatusEnum.BcFailed
                    || r.Status == DocumentValidationStatusEnum.TechnicalError
                )
                && !d.DocumentClassificationResults.Any(r =>
                    r.Status == DocumentClassificationStatusEnum.Failed
                    || r.Status == DocumentClassificationStatusEnum.Timeout
                )
                && !d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Failed)
                && (
                    d.DocumentAnalysisResults.Any(r =>
                        r.Status == DocumentAnalysisStatusEnum.Processing
                        || r.Status == DocumentAnalysisStatusEnum.Pending
                    )
                    || d.DocumentValidationResults.Any(r =>
                        r.Status == DocumentValidationStatusEnum.Processing
                        || r.Status == DocumentValidationStatusEnum.Pending
                    )
                    || (
                        d.UploadType != SecretaryUploadTypeEnum.Manual
                        && (
                            d.DocumentClassificationResults.Any(r =>
                                r.Status == DocumentClassificationStatusEnum.Processing
                                || r.Status == DocumentClassificationStatusEnum.Pending
                            )
                            || d.DocumentClassificationResults.Count == 0
                        )
                    )
                    || d.DocumentEloResults.Any(r =>
                        r.Status == DocumentEloStatusEnum.Processing || r.Status == DocumentEloStatusEnum.Pending
                    )
                )
            ),
            ProcessingStatusEnum.Success => query.Where(d =>
                !d.DocumentAnalysisResults.Any(r => r.Status == DocumentAnalysisStatusEnum.Failed)
                && !d.DocumentValidationResults.Any(r =>
                    r.Status == DocumentValidationStatusEnum.BcFailed
                    || r.Status == DocumentValidationStatusEnum.TechnicalError
                )
                && (
                    d.UploadType == SecretaryUploadTypeEnum.Manual
                    || !d.DocumentClassificationResults.Any(r =>
                        r.Status == DocumentClassificationStatusEnum.Failed
                        || r.Status == DocumentClassificationStatusEnum.Timeout
                    )
                )
                && !d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Failed)
                && !d.DocumentAnalysisResults.Any(r =>
                    r.Status == DocumentAnalysisStatusEnum.Processing || r.Status == DocumentAnalysisStatusEnum.Pending
                )
                && !d.DocumentValidationResults.Any(r =>
                    r.Status == DocumentValidationStatusEnum.Processing
                    || r.Status == DocumentValidationStatusEnum.Pending
                )
                && (
                    d.UploadType == SecretaryUploadTypeEnum.Manual
                    || !d.DocumentClassificationResults.Any(r =>
                        r.Status == DocumentClassificationStatusEnum.Processing
                        || r.Status == DocumentClassificationStatusEnum.Pending
                    )
                )
                && (d.UploadType == SecretaryUploadTypeEnum.Manual || d.DocumentClassificationResults.Count != 0)
                && !d.DocumentEloResults.Any(r =>
                    r.Status == DocumentEloStatusEnum.Processing || r.Status == DocumentEloStatusEnum.Pending
                )
                && (
                    d.DocumentAnalysisResults.Any(r => r.Status == DocumentAnalysisStatusEnum.Success)
                    || d.DocumentValidationResults.Any(r => r.Status == DocumentValidationStatusEnum.Success)
                    || d.DocumentEloResults.Any(r => r.Status == DocumentEloStatusEnum.Success)
                    || (
                        d.UploadType != SecretaryUploadTypeEnum.Manual
                        && d.DocumentClassificationResults.Any(r =>
                            r.Status == DocumentClassificationStatusEnum.Success
                        )
                    )
                )
            ),
            _ => query,
        };
    }

    private static string ExtractMessageFromSoapXml(string? xmlString)
    {
        if (string.IsNullOrWhiteSpace(xmlString))
        {
            return string.Empty;
        }

        try
        {
            const string StartTag = "<message>";
            const string EndTag = "</message>";

            var startIndex = xmlString.IndexOf(StartTag, StringComparison.OrdinalIgnoreCase);
            if (startIndex == -1)
            {
                return string.Empty;
            }

            startIndex += StartTag.Length;
            var endIndex = xmlString.IndexOf(EndTag, startIndex, StringComparison.OrdinalIgnoreCase);
            if (endIndex == -1)
            {
                return string.Empty;
            }

            var message = xmlString[startIndex..endIndex];

            return System.Net.WebUtility.HtmlDecode(message);
        }
        catch (Exception)
        {
            return string.Empty;
        }
    }
}

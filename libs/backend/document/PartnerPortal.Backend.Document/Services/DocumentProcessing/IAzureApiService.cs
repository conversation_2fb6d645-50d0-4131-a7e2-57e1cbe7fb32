using Azure.AI.DocumentIntelligence;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IAzureApiService
{
    Task<AnalyzeResult> AnalyzeLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        CancellationToken cancellationToken = default
    );

    Task<AnalyzeResult> ClassifyLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        SplitMode splitMode,
        CancellationToken cancellationToken = default
    );

    Task<AnalyzeResult> SplitAndAnalyzeLocalDocumentAsync(
        string documentId,
        string contextObjectId,
        string storedName,
        string modelId,
        int startPage,
        int endPage,
        CancellationToken cancellationToken = default
    );
}

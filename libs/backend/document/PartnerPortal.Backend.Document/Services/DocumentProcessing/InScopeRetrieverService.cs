using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Extensions;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.Extractors;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IInScopeRetrieverService
{
    Task<List<ExtractedCompletionCert>?> TryGetCompCertDocuments(DocumentValidationResult documentValidation);
    Task<List<EloCompletionCertData>> ExtractRelatedCompletionCerts(DocumentValidationResult documentValidation);
}

public class InScopeRetrieverService : IInScopeRetrieverService
{
    private readonly ILogger<InScopeRetrieverService> _logger;
    private readonly UserDbContext _context;
    private readonly IAnalyzeResultExtractor<CompletionCertificateAiData> _completionCertExtractor;
    private readonly string _responseFilePath;

    public InScopeRetrieverService(
        ILogger<InScopeRetrieverService> logger,
        UserDbContext context,
        IConfiguration config,
        IAnalyzeResultExtractor<CompletionCertificateAiData> completionCertExtractor
    )
    {
        _logger = logger;
        _context = context;
        _completionCertExtractor = completionCertExtractor;
        _responseFilePath = config.GetConfigValue<string>("AzureAI:ResponsesPath");
    }

    public async Task<List<ExtractedCompletionCert>?> TryGetCompCertDocuments(
        DocumentValidationResult documentValidation
    )
    {
        if (
            documentValidation.DocumentUpload is SecretaryDocumentUpload secretaryDocumentUpload
            && documentValidation.ProcessingSource is DocumentClassification currentClassification
        )
        {
            return await ProcessSecretaryClassificationUpload(secretaryDocumentUpload, currentClassification);
        }
        else
        {
            return await ProcessAttachmentDocumentTypeUpload(documentValidation.DocumentUploadId);
        }
    }

    private async Task<List<ExtractedCompletionCert>?> ProcessSecretaryClassificationUpload(
        SecretaryDocumentUpload secretaryDocumentUpload,
        DocumentClassification currentClassification
    )
    {
        var classifications = await GetClassificationsForSecretaryUpload(secretaryDocumentUpload.Id);

        if (classifications.Count == 0)
        {
            _logger.LogInformation("No related completion certificates found");
            return null;
        }

        var inScopeCompletionCerts = GetInScopeClassifications(
                secretaryDocumentUpload.UploadType,
                currentClassification,
                classifications
            )
            .Where(x => x.DocumentType == DocumentType.CompletionCert)
            .ToList();

        if (inScopeCompletionCerts.Count == 0)
        {
            return null;
        }

        return await ExtractCertsAsync(inScopeCompletionCerts, GetClassificationFilePath);
    }

    private async Task<List<ExtractedCompletionCert>?> ProcessAttachmentDocumentTypeUpload(Guid documentUploadId)
    {
        var completionCerts = await GetAttachmentDocumentTypesForUpload(documentUploadId);

        if (completionCerts.Count == 0)
        {
            _logger.LogInformation("No completion certificates found");
            return null;
        }

        return await ExtractCertsAsync(completionCerts, GetAttachmentDocumentFilePath);
    }

    private async Task<List<DocumentClassification>> GetClassificationsForSecretaryUpload(Guid secretaryUploadId)
    {
        return await _context
            .DocumentClassifications.Include(x => x.Attachment)
            .Where(x => x.SecretaryDocumentUploadId == secretaryUploadId)
            .ToListAsync();
    }

    private async Task<List<AttachmentDocumentType>> GetAttachmentDocumentTypesForUpload(Guid documentUploadId)
    {
        return await _context
            .AttachmentDocumentTypes.Include(x => x.Attachment)
            .Where(x => x.DocumentType == DocumentType.CompletionCert && x.DocumentUploadId == documentUploadId)
            .ToListAsync();
    }

    private string GetClassificationFilePath(DocumentClassification classification)
    {
        return FileUtility.GetFilePath(
            _responseFilePath,
            classification.Attachment.Context,
            FileUtility.GetClassificationStoredName(
                classification.Attachment.StoredName,
                classification.StartPage,
                classification.EndPage
            )
        );
    }

    private string GetAttachmentDocumentFilePath(AttachmentDocumentType document)
    {
        return FileUtility.GetFilePath(_responseFilePath, document.Attachment.Context, document.Attachment.StoredName);
    }

    private async Task<List<ExtractedCompletionCert>> ExtractCertsAsync<T>(
        List<T> entities,
        Func<T, string> getFilePath
    )
    {
        try
        {
            var certs = new List<ExtractedCompletionCert>();
            foreach (var entity in entities)
            {
                var filePath = getFilePath(entity);
                var analysisResult = await FileUtility.GetAnalyzeResult(filePath);
                var completionCertAiData = _completionCertExtractor.ExtractData(analysisResult);

                certs.Add(
                    new ExtractedCompletionCert
                    {
                        ExecutionDate = completionCertAiData.ExecutionDate,
                        TigId = completionCertAiData.TigId,
                        GrossAmount = completionCertAiData.ActualGrossValue.amount,
                    }
                );
            }
            return certs;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error extracting completion certificates from entity {EntityType}",
                entities.GetType().Name
            );
            throw;
        }
    }

    private static IEnumerable<DocumentClassification> GetInScopeClassifications(
        SecretaryUploadTypeEnum uploadType,
        DocumentClassification currentClassification,
        IEnumerable<DocumentClassification> allClassifications
    )
    {
        if (uploadType == SecretaryUploadTypeEnum.Batch)
        {
            // In Batch mode, each attachment is its own case; restrict to the current attachment only
            return allClassifications.Where(x => x.AttachmentId == currentClassification.AttachmentId);
        }

        // In PerCase, the whole upload is the case; include all classifications from the upload
        return allClassifications.Where(x =>
            x.SecretaryDocumentUploadId == currentClassification.SecretaryDocumentUploadId
        );
    }

    public async Task<List<EloCompletionCertData>> ExtractRelatedCompletionCerts(
        DocumentValidationResult documentValidation
    )
    {
        var completionCertSources = await GetCompletionCertSources(documentValidation);
        return await BuildEloCompletionCertData(documentValidation.DocumentUploadId, completionCertSources);
    }

    private async Task<IEnumerable<(int attachmentId, int processingSourceId)>> GetCompletionCertSources(
        DocumentValidationResult documentValidation
    )
    {
        if (
            documentValidation.DocumentUpload is SecretaryDocumentUpload secretaryDocumentUpload
            && documentValidation.ProcessingSource is DocumentClassification currentClassification
        )
        {
            var classifications = await _context
                .DocumentClassifications.Where(x => x.SecretaryDocumentUploadId == secretaryDocumentUpload.Id)
                .ToListAsync();

            var inScopeCompletionCerts = GetInScopeClassifications(
                    secretaryDocumentUpload.UploadType,
                    currentClassification,
                    classifications
                )
                .Where(x => x.DocumentType == DocumentType.CompletionCert)
                .ToList();

            return inScopeCompletionCerts.Select(x => (x.AttachmentId, x.Id));
        }
        else
        {
            var completionCerts = await _context
                .AttachmentDocumentTypes.Where(x =>
                    x.DocumentType == DocumentType.CompletionCert
                    && x.DocumentUploadId == documentValidation.DocumentUploadId
                )
                .ToListAsync();

            return completionCerts.Select(x => (x.AttachmentId, x.Id));
        }
    }

    private async Task<List<EloCompletionCertData>> BuildEloCompletionCertData(
        Guid documentUploadId,
        IEnumerable<(int attachmentId, int processingSourceId)> completionCertSources
    )
    {
        var result = new List<EloCompletionCertData>();
        var index = 0;

        foreach (var (attachmentId, processingSourceId) in completionCertSources)
        {
            var certValidationResult = await GetDocumentValidationResult(documentUploadId, processingSourceId);

            if (certValidationResult?.ValidationResultJson == null)
            {
                _logger.LogInformation(
                    "Completion cert validation result JSON is null for attachment {AttachmentId}",
                    attachmentId
                );
                continue;
            }

            var validationData = DeserializeValidationData(certValidationResult.ValidationResultJson);
            var completionCertId = validationData.GetFieldOneOfValues(CompletionCertAiFieldNames.TigId);

            index++;

            if (!string.IsNullOrEmpty(completionCertId))
            {
                result.Add(CreateEloCompletionCertData(completionCertId, index, validationData));
            }
        }

        _logger.LogInformation(
            "Extracted {Count} completion certificates from {DocumentUploadId}",
            result.Count,
            documentUploadId
        );
        return result;
    }

    private async Task<DocumentValidationResult?> GetDocumentValidationResult(
        Guid documentUploadId,
        int processingSourceId
    )
    {
        return await _context.DocumentValidationResults.FirstOrDefaultAsync(x =>
            x.DocumentUploadId == documentUploadId && x.ProcessingSourceId == processingSourceId
        );
    }

    private static List<ValidationJson> DeserializeValidationData(string validationResultJson)
    {
        return JsonSerializer.Deserialize<List<ValidationJson>>(validationResultJson)
            ?? throw new InvalidOperationException("Completion cert validation result JSON is null");
    }

    private static EloCompletionCertData CreateEloCompletionCertData(
        string completionCertId,
        int rowIndex,
        List<ValidationJson> validationData
    )
    {
        return new EloCompletionCertData
        {
            CompletionCertId = completionCertId,
            RowIndex = rowIndex,
            ValidationData = validationData,
        };
    }
}

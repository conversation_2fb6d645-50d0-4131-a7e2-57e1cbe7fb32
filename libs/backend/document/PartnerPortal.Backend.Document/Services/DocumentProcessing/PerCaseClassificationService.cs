using Azure.AI.DocumentIntelligence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Utils;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using Polly.Timeout;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IPerCaseClassificationService
{
    Task<JobOperationResult> ClassifyDocuments(DocumentJobInputData inputData, CancellationToken cancellationToken);
}

public class PerCaseClassificationService : IPerCaseClassificationService
{
    private readonly UserDbContext _context;
    private readonly IAzureApiService _azureApiService;
    private readonly ILogger<PerCaseClassificationService> _logger;
    private readonly TiffAttachmentConvertService _tiffAttachmentConvertService;
    private readonly string _classificationModelId;

    public PerCaseClassificationService(
        UserDbContext context,
        IAzureApiService azureApiService,
        ILogger<PerCaseClassificationService> logger,
        TiffAttachmentConvertService tiffAttachmentConvertService,
        IConfiguration configuration
    )
    {
        _context = context;
        _azureApiService = azureApiService;
        _logger = logger;
        _tiffAttachmentConvertService = tiffAttachmentConvertService;
        _classificationModelId = configuration.GetConfigValue<string>("AzureAI:ClassificationModelId");
    }

    public async Task<JobOperationResult> ClassifyDocuments(
        DocumentJobInputData inputData,
        CancellationToken cancellationToken
    )
    {
        // Special path for PerCase: classify all attachments of the upload in one go
        if (inputData.UploadType == SecretaryUploadTypeEnum.PerCase)
        {
            try
            {
                var (failureCount, timeoutCount) = await ProcessPerCaseClassification(
                    inputData.DocumentUploadId,
                    cancellationToken
                );

                if (timeoutCount > 0)
                {
                    return new JobOperationResult { Status = JobOperationStatus.Timeout };
                }

                // If any failures occurred, signal for retry
                if (failureCount > 0)
                {
                    return new JobOperationResult { Status = JobOperationStatus.Error };
                }

                return new JobOperationResult { Status = JobOperationStatus.Success };
            }
            catch (TimeoutRejectedException ex)
            {
                _logger.LogWarning(
                    ex,
                    "Timeout while classifying PerCase upload {DocumentUploadId}",
                    inputData.DocumentUploadId
                );
                return new JobOperationResult { Status = JobOperationStatus.Timeout };
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unhandled error while classifying PerCase upload {DocumentUploadId}",
                    inputData.DocumentUploadId
                );
                return new JobOperationResult { Status = JobOperationStatus.Error };
            }
        }
        throw new InvalidOperationException("Upload type is not PerCase");
    }

    private async Task<(int FailureCount, int TimeoutCount)> ProcessPerCaseClassification(
        Guid documentUploadId,
        CancellationToken cancellationToken
    )
    {
        var allClassifications = await _context
            .DocumentClassificationResults.Where(dc => dc.DocumentUploadId == documentUploadId)
            .Include(dc => dc.Attachment)
            .Include(dc => dc.DocumentUpload)
            .ToListAsync(cancellationToken);

        if (allClassifications.Count == 0)
        {
            _logger.LogWarning(
                "No DocumentClassificationResults found for PerCase upload {DocumentUploadId}",
                documentUploadId
            );
            return (FailureCount: 1, TimeoutCount: 0);
        }

        // Work only on items not already successfully classified
        var toProcess = allClassifications.Where(x => x.Status != DocumentClassificationStatusEnum.Success).ToList();

        // Mark all to-processing as Processing
        foreach (var classification in toProcess)
        {
            classification.Status = DocumentClassificationStatusEnum.Processing;
        }
        await _context.SaveChangesAsync(cancellationToken);

        foreach (var classification in toProcess)
        {
            try
            {
                if (classification.Attachment.MimeType == "image/tiff")
                {
                    _logger.LogDebug(
                        "Converting TIFF attachment {AttachmentDocumentTypeId} to PDF for document upload {DocumentUploadId}",
                        classification.AttachmentId,
                        classification.DocumentUploadId
                    );
                    await _tiffAttachmentConvertService.ConvertTiffAttachmentToPdf(
                        classification.AttachmentId,
                        cancellationToken
                    );
                }

                await AzureApiRateLimiter.SpaceEvenly(
                    async () =>
                        await AzureApiRateLimiter.ResiliencePipeline.ExecuteAsync(
                            async token => await ProcessClassification(classification, token),
                            cancellationToken
                        ),
                    _logger
                );

                // mark success
                classification.Status = DocumentClassificationStatusEnum.Success;
                classification.SetErrorMessage(null);
            }
            catch (TimeoutRejectedException tex)
            {
                classification.Status = DocumentClassificationStatusEnum.Timeout;
                classification.SetErrorMessage("Operation timed out");
                _logger.LogWarning(
                    tex,
                    "Timeout during PerCase classification for attachment {AttachmentId} upload {UploadId}",
                    classification.AttachmentId,
                    classification.DocumentUploadId
                );
            }
            catch (Exception ex)
            {
                classification.Status = DocumentClassificationStatusEnum.Failed;
                classification.SetErrorMessage(ex.ToString());
                _logger.LogError(
                    ex,
                    "Error during PerCase classification for attachment {AttachmentId} upload {UploadId}",
                    classification.AttachmentId,
                    classification.DocumentUploadId
                );
            }
        }

        await _context.SaveChangesAsync(cancellationToken);

        var timeoutCount = toProcess.Count(c => c.Status == DocumentClassificationStatusEnum.Timeout);
        var failureCount = toProcess.Count(c => c.Status == DocumentClassificationStatusEnum.Failed);

        // Apply highest-confidence invoice rule across the upload
        var createdClassifications = await _context
            .DocumentClassifications.Where(dc => dc.SecretaryDocumentUploadId == documentUploadId)
            .ToListAsync(cancellationToken);

        var invoiceCandidates = createdClassifications
            .Where(dc => dc.IsInvoiceType)
            .OrderByDescending(dc => dc.Confidence ?? 0.0)
            .ToList();

        if (invoiceCandidates.Count > 1)
        {
            var bestInvoiceId = invoiceCandidates.First().Id;
            foreach (var invoice in invoiceCandidates.Skip(1))
            {
                invoice.DocumentType = DocumentType.Other;
                invoice.Confidence = null;
            }

            await _context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation(
                "PerCase selection applied. Kept invoice classification {BestId} and demoted {CountDemoted} others to Other for upload {UploadId}",
                bestInvoiceId,
                invoiceCandidates.Count - 1,
                documentUploadId
            );
        }

        return (failureCount, timeoutCount);
    }

    private async Task ProcessClassification(
        DocumentClassificationResult documentClassificationResult,
        CancellationToken cancellationToken
    )
    {
        _logger.LogDebug(
            "Starting document classification for attachment {AttachmentDocumentTypeId} for document upload {DocumentUploadId}",
            documentClassificationResult.AttachmentId,
            documentClassificationResult.DocumentUploadId
        );
        var result = await _azureApiService.ClassifyLocalDocumentAsync(
            documentClassificationResult.AttachmentId.ToString(),
            documentClassificationResult.Attachment.Context,
            documentClassificationResult.Attachment.StoredName,
            _classificationModelId,
            SplitMode.None,
            cancellationToken
        );

        if (result.Documents == null || result.Documents.Count == 0)
        {
            throw new InvalidOperationException("No documents found in the classification result");
        }
        var doc = result.Documents[0];

        var documentType = doc.DocumentType switch
        {
            "szamla" => DocumentType.Invoice,
            "TIG" => DocumentType.CompletionCert,
            "szallitolevel" => DocumentType.DeliveryNote,
            _ => DocumentType.Other,
        };

        var documentClassification = new DocumentClassification
        {
            SecretaryDocumentUploadId = documentClassificationResult.DocumentUploadId,
            AttachmentId = documentClassificationResult.AttachmentId,
            StartPage = doc.BoundingRegions[0].PageNumber,
            EndPage = doc.BoundingRegions[doc.BoundingRegions.Count - 1].PageNumber,
            DocumentType = documentType,
            Confidence = doc.Confidence,
        };

        _logger.LogDebug(
            "Finished document classification for attachment {AttachmentDocumentTypeId} for document upload {DocumentUploadId}",
            documentClassificationResult.AttachmentId,
            documentClassificationResult.DocumentUploadId
        );

        await _context.DocumentClassifications.AddAsync(documentClassification, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }
}

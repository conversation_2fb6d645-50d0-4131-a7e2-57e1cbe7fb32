using System.Text.Json;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.DocumentProcessing.Jobs;
using PartnerPortal.Backend.Document.Services.SpIdentifier;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using Quartz;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IProcessingManagerService
{
    Task<List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)>> PrepareManualDocuments(
        DocumentUpload documentUpload
    );
    Task TriggerDocumentAnalysisJobs(List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)> list);

    Task<List<(int AttachmentId, Guid DocumentUploadId)>> ClassifySecretaryDocuments(
        SecretaryDocumentUpload documentUpload
    );
    Task TriggerBatchClassificationJobs(List<(int AttachmentId, Guid DocumentUploadId)> list);
    Task TriggerPerCaseClassificationJobs(Guid documentUploadId);
    Task<List<DocumentAnalysisResult>> PrepareSecretaryDocuments(List<DocumentClassification> documentClassifications);
}

public class ProcessingManagerService : IProcessingManagerService
{
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly ILogger<ProcessingManagerService> _logger;
    private readonly UserDbContext _dbContext;
    private readonly ISpIdentifierService _spIdentifierService;

    public ProcessingManagerService(
        ISchedulerFactory schedulerFactory,
        ILogger<ProcessingManagerService> logger,
        UserDbContext context,
        ISpIdentifierService spIdentifierService
    )
    {
        _schedulerFactory = schedulerFactory;
        _logger = logger;
        _dbContext = context;
        _spIdentifierService = spIdentifierService;
    }

    public async Task TriggerDocumentAnalysisJobs(List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)> list)
    {
        var scheduler = await _schedulerFactory.GetScheduler();

        var triggers = new List<ITrigger>();

        foreach (var (attachmentDocumentTypeId, documentUploadId) in list)
        {
            var jobData = JsonSerializer.Serialize(
                new DocumentJobInputData
                {
                    ItemId = attachmentDocumentTypeId,
                    RetryCount = 0,
                    DocumentUploadId = documentUploadId,
                }
            );

            triggers.Add(
                TriggerBuilder
                    .Create()
                    .ForJob(DocumentAnalyzerJob.Key)
                    .UsingJobData("jobData", jobData)
                    .StartNow()
                    .Build()
            );
        }

        var scheduleTasks = triggers.Select(trigger => scheduler.ScheduleJob(trigger));
        await Task.WhenAll(scheduleTasks);
        _logger.LogInformation(
            "Document analysis jobs scheduled for {DocumentUploadIds}",
            list.Select(l => l.DocumentUploadId)
        );
    }

    public async Task<List<(int AttachmentDocumentTypeId, Guid DocumentUploadId)>> PrepareManualDocuments(
        DocumentUpload documentUpload
    )
    {
        var documentAnalyses = new List<DocumentAnalysisResult>();
        var documentValidationResults = new List<DocumentValidationResult>();
        var eloResults = new List<DocumentEloResult>();

        foreach (var attachmentDocumentType in documentUpload.GetProcessableAttachments())
        {
            var documentAnalysis = new DocumentAnalysisResult
            {
                DocumentUploadId = documentUpload.Id,
                ProcessingSourceId = attachmentDocumentType.Id,
                Status = DocumentAnalysisStatusEnum.Pending,
            };

            var documentValidation = new DocumentValidationResult
            {
                DocumentUploadId = documentUpload.Id,
                ProcessingSourceId = attachmentDocumentType.Id,
                Status = DocumentValidationStatusEnum.Pending,
            };

            if (attachmentDocumentType.IsInvoiceType)
            {
                eloResults.Add(
                    new DocumentEloResult
                    {
                        DocumentUploadId = documentUpload.Id,
                        ProcessingSourceId = attachmentDocumentType.Id,
                        Status = DocumentEloStatusEnum.Pending,
                    }
                );
            }

            documentAnalyses.Add(documentAnalysis);
            documentValidationResults.Add(documentValidation);
        }

        await _dbContext.DocumentAnalysisResults.AddRangeAsync(documentAnalyses);
        await _dbContext.DocumentValidationResults.AddRangeAsync(documentValidationResults);
        await _dbContext.DocumentEloResults.AddRangeAsync(eloResults);
        await _dbContext.SaveChangesAsync();
        _logger.LogInformation("Document analysis results added for {DocumentUploadId}", documentUpload.Id);
        return [.. documentAnalyses.Select(da => (da.ProcessingSourceId, documentUpload.Id))];
    }

    public async Task<List<DocumentAnalysisResult>> PrepareSecretaryDocuments(
        List<DocumentClassification> documentClassifications
    )
    {
        var documentAnalyses = new List<DocumentAnalysisResult>();
        var documentValidationResults = new List<DocumentValidationResult>();
        var eloResults = new List<DocumentEloResult>();

        foreach (
            var documentClassification in documentClassifications.Where(x =>
                x.DocumentType == DocumentType.Invoice || x.DocumentType == DocumentType.CompletionCert
            )
        )
        {
            var documentAnalysis = new DocumentAnalysisResult
            {
                DocumentUploadId = documentClassification.SecretaryDocumentUploadId,
                ProcessingSourceId = documentClassification.Id,
                Status = DocumentAnalysisStatusEnum.Pending,
            };

            var documentValidation = new DocumentValidationResult
            {
                DocumentUploadId = documentClassification.SecretaryDocumentUploadId,
                ProcessingSourceId = documentClassification.Id,
                Status = DocumentValidationStatusEnum.Pending,
            };

            if (documentClassification.IsInvoiceType)
            {
                eloResults.Add(
                    new DocumentEloResult
                    {
                        DocumentUploadId = documentClassification.SecretaryDocumentUploadId,
                        ProcessingSourceId = documentClassification.Id,
                        Status = DocumentEloStatusEnum.Pending,
                    }
                );
                documentClassification.SpIdentifier = await _spIdentifierService.GetNewSpIdentifierAsync();
            }

            documentAnalyses.Add(documentAnalysis);
            documentValidationResults.Add(documentValidation);
        }

        await _dbContext.DocumentAnalysisResults.AddRangeAsync(documentAnalyses);
        await _dbContext.DocumentValidationResults.AddRangeAsync(documentValidationResults);
        await _dbContext.DocumentEloResults.AddRangeAsync(eloResults);
        await _dbContext.SaveChangesAsync();
        _logger.LogInformation(
            "Document analysis results added for {DocumentUploadId}",
            documentClassifications.First().SecretaryDocumentUploadId
        );
        return documentAnalyses;
    }

    public async Task TriggerBatchClassificationJobs(List<(int AttachmentId, Guid DocumentUploadId)> list)
    {
        var scheduler = await _schedulerFactory.GetScheduler();
        var triggers = new List<ITrigger>();

        foreach (var (attachmentId, documentUploadId) in list)
        {
            var jobData = JsonSerializer.Serialize(
                new DocumentJobInputData
                {
                    ItemId = attachmentId,
                    RetryCount = 0,
                    DocumentUploadId = documentUploadId,
                    UploadType = SecretaryUploadTypeEnum.Batch,
                }
            );

            triggers.Add(
                TriggerBuilder
                    .Create()
                    .ForJob(DocumentClassifierJob.Key)
                    .UsingJobData("jobData", jobData)
                    .StartNow()
                    .Build()
            );
        }

        var scheduleTasks = triggers.Select(trigger => scheduler.ScheduleJob(trigger));
        await Task.WhenAll(scheduleTasks);
        _logger.LogInformation(
            "Document classification jobs scheduled for {DocumentUploadIds}",
            list.Select(l => l.DocumentUploadId)
        );
    }

    public async Task TriggerPerCaseClassificationJobs(Guid documentUploadId)
    {
        var scheduler = await _schedulerFactory.GetScheduler();

        var jobData = JsonSerializer.Serialize(
            new DocumentJobInputData
            {
                RetryCount = 0,
                DocumentUploadId = documentUploadId,
                UploadType = SecretaryUploadTypeEnum.PerCase,
            }
        );

        var trigger = TriggerBuilder
            .Create()
            .ForJob(DocumentClassifierJob.Key)
            .UsingJobData("jobData", jobData)
            .StartNow()
            .Build();

        await scheduler.ScheduleJob(trigger);
        _logger.LogInformation("Document classification jobs scheduled for {DocumentUploadId}", documentUploadId);
    }

    public async Task<List<(int AttachmentId, Guid DocumentUploadId)>> ClassifySecretaryDocuments(
        SecretaryDocumentUpload documentUpload
    )
    {
        var documentClassificationResults = new List<DocumentClassificationResult>();
        foreach (var attachmentDocumentType in documentUpload.AttachmentDocumentTypes)
        {
            var documentClassification = new DocumentClassificationResult
            {
                DocumentUploadId = documentUpload.Id,
                AttachmentId = attachmentDocumentType.AttachmentId,
                Status = DocumentClassificationStatusEnum.Pending,
            };

            documentClassificationResults.Add(documentClassification);
        }

        await _dbContext.DocumentClassificationResults.AddRangeAsync(documentClassificationResults);
        await _dbContext.SaveChangesAsync();
        _logger.LogInformation("Document classification results added for {DocumentUploadId}", documentUpload.Id);
        return [.. documentClassificationResults.Select(dc => (dc.AttachmentId, documentUpload.Id))];
    }
}

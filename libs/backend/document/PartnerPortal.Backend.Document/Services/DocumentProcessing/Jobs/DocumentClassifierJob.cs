using System.Text.Json;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using Quartz;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing.Jobs;

public class DocumentClassifierJob : IJob
{
    public static readonly JobKey Key = new(nameof(DocumentClassifierJob), "DocumentProcessing");

    private readonly ILogger<DocumentClassifierJob> _logger;
    private readonly IBatchClassificationService _batchClassificationService;
    private readonly IBatchCoordinatorService _batchCoordinatorService;
    private readonly IPerCaseClassificationService _perCaseClassificationService;

    public DocumentClassifierJob(
        ILogger<DocumentClassifierJob> logger,
        IBatchClassificationService batchClassificationService,
        IBatchCoordinatorService batchCoordinatorService,
        IPerCaseClassificationService perCaseClassificationService
    )
    {
        _logger = logger;
        _batchClassificationService = batchClassificationService;
        _batchCoordinatorService = batchCoordinatorService;
        _perCaseClassificationService = perCaseClassificationService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var inputData = JsonSerializer.Deserialize<DocumentJobInputData>(
                context.Trigger.JobDataMap.GetString("jobData")
                    ?? throw new InvalidOperationException("Job data is null")
            )!;

            var result =
                inputData.UploadType == SecretaryUploadTypeEnum.PerCase
                    ? await _perCaseClassificationService.ClassifyDocuments(inputData, context.CancellationToken)
                    : await _batchClassificationService.ClassifyDocument(inputData, context.CancellationToken);

            if (result.Status == JobOperationStatus.Success)
            {
                _logger.LogInformation(
                    "Document classification successful for {AttachmentDocumentTypeId}",
                    inputData.ItemId
                );
                await ScheduleAnalysisJobs(context, inputData);
            }
            else if (JobRetryHelper.ShouldRetry(inputData.RetryCount, result.Status))
            {
                await JobRetryHelper.ScheduleRetry(context, inputData, result, _logger);
            }
            else
            {
                _logger.LogError(
                    "Document classification failed after {RetryCount} retries for {AttachmentDocumentTypeId}",
                    inputData.RetryCount,
                    inputData.ItemId
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Unexpected error in DocumentClassifierJob");
        }
    }

    private async Task ScheduleAnalysisJobs(IJobExecutionContext context, DocumentJobInputData inputData)
    {
        await _batchCoordinatorService.CheckClassificationCompletionAndTriggerAnalysis(
            inputData.DocumentUploadId,
            context.Scheduler
        );
    }
}

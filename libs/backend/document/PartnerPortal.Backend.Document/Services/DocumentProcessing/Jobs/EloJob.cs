using System.Text.Json;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.Elo;
using PartnerPortal.Backend.Document.Utils;
using Quartz;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing.Jobs;

public class EloJob : IJob
{
    public static readonly JobKey Key = new(nameof(EloJob), "DocumentProcessing");
    private readonly ILogger<EloJob> _logger;
    private readonly IEloService _eloService;

    public EloJob(ILogger<EloJob> logger, IEloService eloService)
    {
        _logger = logger;
        _eloService = eloService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var jobData = JsonSerializer.Deserialize<DocumentJobInputData>(
                context.MergedJobDataMap.GetString("jobData") ?? throw new InvalidOperationException("Job data is null")
            )!;

            var result = await _eloService.ProcessForElo(jobData);
            if (result.Status == JobOperationStatus.Success)
            {
                _logger.LogInformation("EloJob executed successfully");
            }
            else if (JobRetryHelper.ShouldRetry(jobData.RetryCount, result.Status))
            {
                await JobRetryHelper.ScheduleRetry(context, jobData, result, _logger);
            }
            else
            {
                _logger.LogError(
                    "EloJob failed after {RetryCount} retries for {ItemId}",
                    jobData.RetryCount,
                    jobData.ItemId
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Unexpected error in EloJob");
        }
    }
}

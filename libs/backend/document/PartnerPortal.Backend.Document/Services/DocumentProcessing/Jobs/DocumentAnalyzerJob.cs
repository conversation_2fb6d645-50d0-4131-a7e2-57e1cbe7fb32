using System.Text.Json;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using Quartz;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing.Jobs;

public class DocumentAnalyzerJob : IJob
{
    private readonly IDocumentAnalysisService _documentAnalysisService;
    private readonly ILogger<DocumentAnalyzerJob> _logger;
    public static readonly JobKey Key = new(nameof(DocumentAnalyzerJob), "DocumentProcessing");
    private readonly IBatchCoordinatorService _batchCoordinatorService;

    public DocumentAnalyzerJob(
        IDocumentAnalysisService documentAnalysisService,
        ILogger<DocumentAnalyzerJob> logger,
        IBatchCoordinatorService batchCoordinator
    )
    {
        _documentAnalysisService = documentAnalysisService;
        _logger = logger;
        _batchCoordinatorService = batchCoordinator;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var inputData = JsonSerializer.Deserialize<DocumentJobInputData>(
                context.Trigger.JobDataMap.GetString("jobData")
                    ?? throw new InvalidOperationException("Job data is null")
            )!;

            var result = await _documentAnalysisService.AnalyzeDocument(inputData, context.CancellationToken);

            if (result.Status == JobOperationStatus.Success)
            {
                _logger.LogInformation("Document analysis successful for {AttachmentDocumentTypeId}", inputData.ItemId);
                await ScheduleValidationJobs(context, inputData);
            }
            else if (JobRetryHelper.ShouldRetry(inputData.RetryCount, result.Status))
            {
                await JobRetryHelper.ScheduleRetry(context, inputData, result, _logger);
            }
            else
            {
                _logger.LogError(
                    "Document analysis failed after {RetryCount} retries for {AttachmentDocumentTypeId}",
                    inputData.RetryCount,
                    inputData.ItemId
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Unexpected error in DocumentAnalyzerJob");
        }
    }

    private async Task ScheduleValidationJobs(IJobExecutionContext context, DocumentJobInputData inputData)
    {
        await _batchCoordinatorService.CheckAnalysisCompletionAndTriggerValidation(
            inputData.DocumentUploadId,
            context.Scheduler
        );
    }
}

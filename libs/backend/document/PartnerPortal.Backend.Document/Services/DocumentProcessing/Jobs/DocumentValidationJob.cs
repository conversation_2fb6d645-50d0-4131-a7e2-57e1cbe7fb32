using System.Text.Json;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Utils;
using Quartz;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing.Jobs;

public class DocumentValidationJob : IJob
{
    public static readonly JobKey Key = new(nameof(DocumentValidationJob), "DocumentProcessing");

    private readonly ILogger<DocumentValidationJob> _logger;
    private readonly IDocumentValidationService _documentValidationService;
    private readonly IBatchCoordinatorService _batchCoordinatorService;

    public DocumentValidationJob(
        ILogger<DocumentValidationJob> logger,
        IDocumentValidationService documentValidationService,
        IBatchCoordinatorService batchCoordinatorService
    )
    {
        _logger = logger;
        _documentValidationService = documentValidationService;
        _batchCoordinatorService = batchCoordinatorService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var jobData = JsonSerializer.Deserialize<DocumentJobInputData>(
                context.MergedJobDataMap.GetString("jobData") ?? throw new InvalidOperationException("Job data is null")
            )!;

            var result = await _documentValidationService.ValidateDocument(jobData);

            if (result.Status == JobOperationStatus.Success)
            {
                _logger.LogInformation("Document validation successful for {AttachmentDocumentTypeId}", jobData.ItemId);
                await ScheduleEloJobs(jobData, context.Scheduler);
            }
            else if (JobRetryHelper.ShouldRetry(jobData.RetryCount, result.Status))
            {
                await JobRetryHelper.ScheduleRetry(context, jobData, result, _logger);
            }
            else
            {
                _logger.LogError(
                    "Document validation failed after {RetryCount} retries for {AttachmentDocumentTypeId}",
                    jobData.RetryCount,
                    jobData.ItemId
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Unexpected error in DocumentValidationJob");
        }
    }

    private async Task ScheduleEloJobs(DocumentJobInputData jobData, IScheduler scheduler)
    {
        await _batchCoordinatorService.CheckValidationCompletionAndTriggerElo(jobData.DocumentUploadId, scheduler);
    }
}

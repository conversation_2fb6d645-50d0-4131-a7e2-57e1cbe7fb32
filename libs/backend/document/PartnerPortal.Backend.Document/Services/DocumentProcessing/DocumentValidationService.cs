using System.Text.Json;
using Azure.AI.DocumentIntelligence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.DocumentProcessing.Validation;
using PartnerPortal.Backend.Document.Utils;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IDocumentValidationService
{
    Task<JobOperationResult> ValidateDocument(DocumentJobInputData jobInputData);
}

public class DocumentValidationService : IDocumentValidationService
{
    private readonly UserDbContext _context;
    private readonly ILogger<DocumentValidationService> _logger;
    private readonly string _responseFilePath;

    private readonly IDocumentValidatorFactory _validatorFactory;

    public DocumentValidationService(
        UserDbContext context,
        ILogger<DocumentValidationService> logger,
        IConfiguration config,
        IDocumentValidatorFactory validatorFactory
    )
    {
        _context = context;
        _logger = logger;
        _responseFilePath =
            config.GetValue<string>("AzureAI:ResponsesPath")
            ?? throw new InvalidOperationException("AzureAI:ResponsesPath is not set");
        _validatorFactory = validatorFactory;
    }

    public async Task<JobOperationResult> ValidateDocument(DocumentJobInputData jobInputData)
    {
        var documentAnalysis = await _context.DocumentAnalysisResults.FirstOrDefaultAsync(da =>
            da.ProcessingSourceId == jobInputData.ItemId && da.DocumentUploadId == jobInputData.DocumentUploadId
        );
        if (documentAnalysis is not { Status: DocumentAnalysisStatusEnum.Success })
        {
            return new JobOperationResult { Status = JobOperationStatus.Fatal };
        }

        var documentValidation = await _context
            .DocumentValidationResults.Include(da => da.ProcessingSource)
            .ThenInclude(ad => ad.Attachment)
            .Include(da => da.DocumentUpload)
            .AsSplitQuery()
            .FirstOrDefaultAsync(dv =>
                dv.DocumentUploadId == jobInputData.DocumentUploadId && dv.ProcessingSourceId == jobInputData.ItemId
            );

        if (documentValidation == null)
        {
            return new JobOperationResult { Status = JobOperationStatus.Fatal };
        }

        var results = new List<ValidationJson>();
        try
        {
            string filePath;
            if (documentValidation.ProcessingSource is DocumentClassification classification)
            {
                filePath = FileUtility.GetFilePath(
                    _responseFilePath,
                    classification.Attachment.Context,
                    FileUtility.GetClassificationStoredName(
                        classification.Attachment.StoredName,
                        classification.StartPage,
                        classification.EndPage
                    )
                );
            }
            else
            {
                filePath = FileUtility.GetFilePath(
                    _responseFilePath,
                    documentValidation.ProcessingSource.Attachment.Context,
                    documentValidation.ProcessingSource.Attachment.StoredName
                );
            }

            AnalyzeResult analysisResult = await FileUtility.GetAnalyzeResult(filePath);

            var documentType = documentValidation.ProcessingSource.DocumentType;
            var validator = _validatorFactory.GetValidator(documentType);

            await validator.ValidateDocument(analysisResult, documentValidation, results);

            await SetDocumentValidationStatus(documentValidation, DocumentValidationStatusEnum.Success, results);

            return new JobOperationResult { Status = JobOperationStatus.Success };
        }
        catch (BcApiException ex)
        {
            _logger.LogError(
                ex,
                "Error when validating document due to BC API error {DocumentId} {AttachmentDocumentTypeId}",
                documentValidation.DocumentUploadId,
                documentValidation.ProcessingSourceId
            );
            await SetDocumentValidationStatus(
                documentValidation,
                DocumentValidationStatusEnum.BcFailed,
                results,
                ex.ToString()
            );
            return new JobOperationResult { Status = JobOperationStatus.Error };
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error when validating document due to technical error {DocumentId} {AttachmentDocumentTypeId}",
                documentValidation.DocumentUploadId,
                documentValidation.ProcessingSourceId
            );
            await SetDocumentValidationStatus(
                documentValidation,
                DocumentValidationStatusEnum.TechnicalError,
                results,
                ex.ToString()
            );
            return new JobOperationResult { Status = JobOperationStatus.Fatal };
        }
    }

    private async Task SetDocumentValidationStatus(
        DocumentValidationResult documentValidation,
        DocumentValidationStatusEnum status,
        List<ValidationJson> results,
        string? errorMessage = null,
        CancellationToken cancellationToken = default
    )
    {
        documentValidation.Status = status;
        documentValidation.SetErrorMessage(errorMessage);

        try
        {
            documentValidation.ValidationResultJson = JsonSerializer.Serialize(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error when serializing validation results");
        }

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation(
            "Document validation {DocumentValidationId} status updated to {Status}",
            documentValidation.Id,
            status
        );

        if (errorMessage != null)
        {
            _logger.LogWarning(
                "Document validation {DocumentValidationId} failed with error message: {ErrorMessage}",
                documentValidation.Id,
                errorMessage
            );
        }
    }
}

using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using Quartz;

namespace PartnerPortal.Backend.Document.Services.DocumentProcessing;

public interface IBatchCoordinatorService
{
    Task<bool> CheckAnalysisCompletionAndTriggerValidation(Guid documentUploadId, IScheduler scheduler);
    Task<bool> CheckValidationCompletionAndTriggerElo(Guid documentUploadId, IScheduler scheduler);
    Task<bool> CheckClassificationCompletionAndTriggerAnalysis(Guid documentUploadId, IScheduler scheduler);
}

public class BatchCoordinatorService : IBatchCoordinatorService
{
    private static readonly SemaphoreSlim AnalysisSemaphore = new(1, 1);
    private static readonly SemaphoreSlim ValidationSemaphore = new(1, 1);
    private static readonly SemaphoreSlim ClassificationSemaphore = new(1, 1);
    private readonly ILogger<BatchCoordinatorService> _logger;
    private readonly UserDbContext _dbContext;
    private readonly IProcessingManagerService _processingManagerService;

    public BatchCoordinatorService(
        ILogger<BatchCoordinatorService> logger,
        UserDbContext dbContext,
        IProcessingManagerService processingManagerService
    )
    {
        _logger = logger;
        _dbContext = dbContext;
        _processingManagerService = processingManagerService;
    }

    /// <summary>
    /// Checks if all document analyses are complete and triggers validation jobs if so by using a semaphore to ensure only one thread can check/update the database for this document at a time.
    /// </summary>
    /// <param name="documentUploadId">The ID of the document upload to check.</param>
    /// <param name="scheduler">The scheduler to use to schedule the validation jobs.</param>
    /// <returns>True if validation jobs were triggered, false otherwise.</returns>
    public async Task<bool> CheckAnalysisCompletionAndTriggerValidation(Guid documentUploadId, IScheduler scheduler)
    {
        await AnalysisSemaphore.WaitAsync();
        try
        {
            _logger.LogDebug("Checking analysis completion for document upload {DocumentUploadId}", documentUploadId);

            bool allAnalysesComplete = await _dbContext
                .DocumentAnalysisResults.Where(r => r.DocumentUploadId == documentUploadId)
                .AllAsync(r => r.Status == DocumentAnalysisStatusEnum.Success);

            if (!allAnalysesComplete)
            {
                _logger.LogDebug(
                    "Not all analyses are complete yet for document upload {DocumentUploadId}",
                    documentUploadId
                );
                return false;
            }

            bool validationJobsStarted = await _dbContext
                .DocumentValidationResults.Where(r =>
                    r.DocumentUploadId == documentUploadId && r.Status != DocumentValidationStatusEnum.Pending
                )
                .AnyAsync();

            if (validationJobsStarted)
            {
                _logger.LogDebug(
                    "Validation jobs have already been started for document upload {DocumentUploadId}",
                    documentUploadId
                );
                return false;
            }

            _logger.LogInformation(
                "All document analyses for {DocumentUploadId} completed successfully, scheduling validation jobs",
                documentUploadId
            );

            var validationResults = await _dbContext
                .DocumentValidationResults.Where(r => r.DocumentUploadId == documentUploadId)
                .ToListAsync();

            if (validationResults.Count == 0)
            {
                _logger.LogDebug(
                    "No validation results to process for document upload {DocumentUploadId}",
                    documentUploadId
                );
                return false;
            }

            var triggers = new List<ITrigger>();

            foreach (var validationResult in validationResults)
            {
                var jobData = JsonSerializer.Serialize(
                    new DocumentJobInputData
                    {
                        ItemId = validationResult.ProcessingSourceId,
                        DocumentUploadId = documentUploadId,
                        RetryCount = 0,
                    }
                );

                triggers.Add(
                    TriggerBuilder
                        .Create()
                        .ForJob(Jobs.DocumentValidationJob.Key)
                        .UsingJobData("jobData", jobData)
                        .StartNow()
                        .Build()
                );

                validationResult.Status = DocumentValidationStatusEnum.Processing;
            }

            await _dbContext.SaveChangesAsync();

            var scheduleTasks = triggers.Select(trigger => scheduler.ScheduleJob(trigger));
            await Task.WhenAll(scheduleTasks);

            _logger.LogInformation(
                "Scheduled {Count} validation jobs for document upload {DocumentUploadId}",
                triggers.Count,
                documentUploadId
            );

            return true;
        }
        finally
        {
            AnalysisSemaphore.Release();
        }
    }

    /// <summary>
    /// Checks if all document validations are complete and triggers ELO jobs
    /// </summary>
    /// <param name="documentUploadId">The ID of the document upload to check.</param>
    /// <param name="scheduler">The scheduler to use to schedule the ELO jobs.</param>
    /// <returns>True if ELO jobs were triggered, false otherwise.</returns>
    public async Task<bool> CheckValidationCompletionAndTriggerElo(Guid documentUploadId, IScheduler scheduler)
    {
        await ValidationSemaphore.WaitAsync();
        try
        {
            _logger.LogDebug("Checking validation completion for document upload {DocumentUploadId}", documentUploadId);

            bool allValidationsComplete = await _dbContext
                .DocumentValidationResults.Where(r => r.DocumentUploadId == documentUploadId)
                .AllAsync(r => r.Status == DocumentValidationStatusEnum.Success);

            if (!allValidationsComplete)
            {
                _logger.LogDebug(
                    "Not all validations are complete yet for document upload {DocumentUploadId}",
                    documentUploadId
                );
                return false;
            }

            bool eloJobsStarted = await _dbContext
                .DocumentEloResults.Where(r =>
                    r.DocumentUploadId == documentUploadId && r.Status != DocumentEloStatusEnum.Pending
                )
                .AnyAsync();

            if (eloJobsStarted)
            {
                _logger.LogDebug(
                    "ELO jobs have already been started for document upload {DocumentUploadId}",
                    documentUploadId
                );
                return false;
            }

            _logger.LogInformation(
                "All document validations for {DocumentUploadId} completed successfully, scheduling ELO jobs",
                documentUploadId
            );

            var eloResults = await _dbContext
                .DocumentEloResults.Where(r => r.DocumentUploadId == documentUploadId)
                .ToListAsync();

            if (eloResults.Count == 0)
            {
                _logger.LogWarning("No ELO results to process for {DocumentUploadId}", documentUploadId);
                return false;
            }

            var triggers = new List<ITrigger>();

            foreach (var eloResult in eloResults)
            {
                var jobData = JsonSerializer.Serialize(
                    new DocumentJobInputData
                    {
                        ItemId = eloResult.ProcessingSourceId,
                        DocumentUploadId = documentUploadId,
                        RetryCount = 0,
                    }
                );

                triggers.Add(
                    TriggerBuilder.Create().ForJob(Jobs.EloJob.Key).UsingJobData("jobData", jobData).StartNow().Build()
                );
                eloResult.Status = DocumentEloStatusEnum.Processing;
            }

            await _dbContext.SaveChangesAsync();

            var scheduleTasks = triggers.Select(trigger => scheduler.ScheduleJob(trigger));
            await Task.WhenAll(scheduleTasks);

            _logger.LogInformation(
                "Scheduled {Count} ELO jobs for document upload {DocumentUploadId}",
                triggers.Count,
                documentUploadId
            );

            return true;
        }
        finally
        {
            ValidationSemaphore.Release();
        }
    }

    public async Task<bool> CheckClassificationCompletionAndTriggerAnalysis(Guid documentUploadId, IScheduler scheduler)
    {
        await ClassificationSemaphore.WaitAsync();
        try
        {
            _logger.LogDebug(
                "Checking classification completion for document upload {DocumentUploadId}",
                documentUploadId
            );

            bool allClassificationsComplete = await _dbContext
                .DocumentClassificationResults.Where(r => r.DocumentUploadId == documentUploadId)
                .AllAsync(r => r.Status == DocumentClassificationStatusEnum.Success);

            if (!allClassificationsComplete)
            {
                _logger.LogDebug(
                    "Not all classifications are complete yet for document upload {DocumentUploadId}",
                    documentUploadId
                );
                return false; // Not all classifications are complete yet
            }

            bool analysisJobsStarted = await _dbContext
                .DocumentAnalysisResults.Where(r =>
                    r.DocumentUploadId == documentUploadId && r.Status != DocumentAnalysisStatusEnum.Pending
                )
                .AnyAsync();

            if (analysisJobsStarted)
            {
                _logger.LogDebug(
                    "Analysis jobs have already been started for document upload {DocumentUploadId}",
                    documentUploadId
                );
                return false; // Analysis jobs have already been started
            }

            _logger.LogInformation(
                "All document classifications for {DocumentUploadId} completed successfully, scheduling analysis jobs",
                documentUploadId
            );

            var documentClassifications = await _dbContext
                .DocumentClassifications.Where(r => r.SecretaryDocumentUploadId == documentUploadId)
                .ToListAsync();

            if (documentClassifications.Count == 0)
            {
                _logger.LogWarning("No document classifications to process for {DocumentUploadId}", documentUploadId);
                return false; // No document classifications to process
            }

            var documentAnalyses = await _processingManagerService.PrepareSecretaryDocuments(documentClassifications);

            var triggers = new List<ITrigger>();
            foreach (var documentAnalysis in documentAnalyses)
            {
                var jobData = JsonSerializer.Serialize(
                    new DocumentJobInputData
                    {
                        ItemId = documentAnalysis.ProcessingSourceId,
                        DocumentUploadId = documentAnalysis.DocumentUploadId,
                        RetryCount = 0,
                    }
                );

                triggers.Add(
                    TriggerBuilder
                        .Create()
                        .ForJob(Jobs.DocumentAnalyzerJob.Key)
                        .UsingJobData("jobData", jobData)
                        .StartNow()
                        .Build()
                );
                documentAnalysis.Status = DocumentAnalysisStatusEnum.Processing;
            }

            await _dbContext.SaveChangesAsync();
            var scheduleTasks = triggers.Select(trigger => scheduler.ScheduleJob(trigger));
            await Task.WhenAll(scheduleTasks);

            _logger.LogInformation(
                "Scheduled {Count} analysis jobs for document upload {DocumentUploadId} after classification",
                triggers.Count,
                documentUploadId
            );

            return true;
        }
        finally
        {
            ClassificationSemaphore.Release();
        }
    }
}

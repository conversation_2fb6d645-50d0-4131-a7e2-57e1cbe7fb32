using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Utils;

namespace PartnerPortal.Backend.Document.Services.Base;

/// <summary>
/// Base service for data table operations with support for filtering, sorting, pagination, and custom logic.
/// </summary>
/// <typeparam name="TEntity">The entity type from the database</typeparam>
/// <typeparam name="TDto">The DTO type to return</typeparam>
/// <typeparam name="TResponse">The response type containing the paginated results</typeparam>
public abstract class BaseDataTableService<TEntity, TDto, TResponse> : IBaseDataTableService<TEntity, TDto, TResponse>
    where TEntity : class
    where TDto : class
    where TResponse : class, new()
{
    protected readonly IMapper mapper;

    protected BaseDataTableService(IMapper mapper)
    {
        this.mapper = mapper;
    }

    /// <summary>
    /// Build the base query with all necessary includes and initial filtering.
    /// This replaces the need to pass baseQuery as a parameter.
    /// </summary>
    protected abstract IQueryable<TEntity> BuildBaseQuery();

    /// <summary>
    /// Build the final response object with the results and metadata.
    /// </summary>
    protected abstract TResponse BuildResponse(List<TDto> results, int totalCount, TableRequest request);

    /// <summary>
    /// Fields that map directly to entity properties and can be handled by standard database filtering.
    /// </summary>
    protected virtual HashSet<string> GetStandardDatabaseFields()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Fields that can be translated to SQL (joins, concatenations, etc.) but require custom logic.
    /// </summary>
    protected virtual HashSet<string> GetDatabaseComputedFields()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Map display field names to actual entity property paths.
    /// </summary>
    protected virtual Dictionary<string, string> GetFieldMappings()
    {
        return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Resolve display field name to actual entity property path.
    /// </summary>
    protected string ResolveFieldName(string displayFieldName)
    {
        var mappings = GetFieldMappings();
        return mappings.TryGetValue(displayFieldName, out var actualField) ? actualField : displayFieldName;
    }

    /// <summary>
    /// Main method to execute data table operations with full customization support.
    /// </summary>
    public async Task<TResponse> ExecuteDataTableQuery(
        TableRequest request,
        string[]? globalSearchableFields = null,
        CancellationToken cancellationToken = default
    )
    {
        var baseQuery = BuildBaseQuery();

        var filteredQuery = ApplyStandardDatabaseFiltering(baseQuery, request);

        var customFilteredQuery = await ApplyDatabaseComputedFiltering(filteredQuery, request, cancellationToken);

        if (!string.IsNullOrEmpty(request.GlobalFilter) && globalSearchableFields?.Length > 0)
        {
            customFilteredQuery = TableUtility.ApplyGlobalFilter(
                customFilteredQuery,
                request.GlobalFilter,
                globalSearchableFields
            );
        }

        var totalCount = await GetTotalCount(customFilteredQuery, request, cancellationToken);

        var sortedQuery = ApplyStandardDatabaseSorting(customFilteredQuery, request);
        sortedQuery = await ApplyDatabaseComputedSorting(sortedQuery, request, cancellationToken);

        var paginatedQuery = ApplyPagination(sortedQuery, request);

        var projectedResults = await ProjectToDto(paginatedQuery, cancellationToken);

        var postQueryResults = await ApplyPostQueryOperations(projectedResults, request, cancellationToken);

        return BuildResponse(postQueryResults, totalCount, request);
    }

    /// <summary>
    /// Apply standard database filtering using TableUtility for direct entity properties.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyStandardDatabaseFiltering(
        IQueryable<TEntity> query,
        TableRequest request
    )
    {
        if (request.ColumnFilters?.Count == 0 || request.ColumnFilters == null)
            return query;

        var standardFields = GetStandardDatabaseFields();
        var standardFilters = request
            .ColumnFilters.Where(f => standardFields.Contains(f.Field, StringComparer.OrdinalIgnoreCase))
            .Select(f => new ColumnFilter
            {
                Field = ResolveFieldName(f.Field),
                Value = f.Value,
                MatchMode = f.MatchMode,
            })
            .ToList();

        return TableUtility.ApplyColumnFilters(query, standardFilters);
    }

    /// <summary>
    /// Apply database-level computed field filtering.
    /// </summary>
    protected virtual async Task<IQueryable<TEntity>> ApplyDatabaseComputedFiltering(
        IQueryable<TEntity> query,
        TableRequest request,
        CancellationToken cancellationToken
    )
    {
        var databaseComputedFields = GetDatabaseComputedFields();
        var applicableFilters =
            request
                .ColumnFilters?.Where(f => databaseComputedFields.Contains(f.Field, StringComparer.OrdinalIgnoreCase))
                .ToList() ?? [];

        return await ApplyDatabaseComputedFiltersImplementation(query, applicableFilters, cancellationToken);
    }

    /// <summary>
    /// Apply standard database sorting using TableUtility for direct entity properties.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyStandardDatabaseSorting(IQueryable<TEntity> query, TableRequest request)
    {
        if (request.SortFields?.Count == 0 || request.SortFields == null)
            return query;

        var standardFields = GetStandardDatabaseFields();
        var standardSorts = request
            .SortFields.Where(s => standardFields.Contains(s.Field, StringComparer.OrdinalIgnoreCase))
            .Select(s => new SortField { Field = ResolveFieldName(s.Field), Order = s.Order })
            .ToList();

        return TableUtility.ApplySorting(query, standardSorts);
    }

    /// <summary>
    /// Apply database-level computed field sorting.
    /// </summary>
    protected virtual async Task<IQueryable<TEntity>> ApplyDatabaseComputedSorting(
        IQueryable<TEntity> query,
        TableRequest request,
        CancellationToken cancellationToken
    )
    {
        var databaseComputedFields = GetDatabaseComputedFields();
        var applicableSorts =
            request
                .SortFields?.Where(s => databaseComputedFields.Contains(s.Field, StringComparer.OrdinalIgnoreCase))
                .ToList() ?? [];

        return await ApplyDatabaseComputedSortsImplementation(query, applicableSorts, cancellationToken);
    }

    /// <summary>
    /// Override to implement custom database-computed filtering logic.
    /// </summary>
    protected virtual Task<IQueryable<TEntity>> ApplyDatabaseComputedFiltersImplementation(
        IQueryable<TEntity> query,
        List<ColumnFilter> filters,
        CancellationToken cancellationToken
    )
    {
        return Task.FromResult(query);
    }

    /// <summary>
    /// Override to implement custom database-computed sorting logic.
    /// </summary>
    protected virtual Task<IQueryable<TEntity>> ApplyDatabaseComputedSortsImplementation(
        IQueryable<TEntity> query,
        List<SortField> sorts,
        CancellationToken cancellationToken
    )
    {
        return Task.FromResult(query);
    }

    /// <summary>
    /// Get total count. Override if you need custom counting logic.
    /// </summary>
    protected virtual async Task<int> GetTotalCount(
        IQueryable<TEntity> query,
        TableRequest request,
        CancellationToken cancellationToken
    )
    {
        return await query.CountAsync(cancellationToken);
    }

    /// <summary>
    /// Apply pagination. Override if you need custom pagination logic.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyPagination(IQueryable<TEntity> query, TableRequest request)
    {
        return TableUtility.ApplyPagination(query, request.Page, request.PageSize);
    }

    /// <summary>
    /// Project entities to DTOs using AutoMapper. Override to customize projection.
    /// </summary>
    protected virtual async Task<List<TDto>> ProjectToDto(
        IQueryable<TEntity> query,
        CancellationToken cancellationToken
    )
    {
        return await query.ProjectTo<TDto>(mapper.ConfigurationProvider).AsNoTracking().ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Apply post-query operations like computing complex fields.
    /// Override to implement operations that can't be done in the database.
    /// </summary>
    protected virtual Task<List<TDto>> ApplyPostQueryOperations(
        List<TDto> results,
        TableRequest request,
        CancellationToken cancellationToken
    )
    {
        return Task.FromResult(results);
    }
}

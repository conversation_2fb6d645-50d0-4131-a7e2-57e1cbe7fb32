using System.Collections;
using System.Linq.Expressions;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Document.Utils;

public static class TableUtility
{
    public static IQueryable<T> ApplyGlobalFilter<T>(
        IQueryable<T> query,
        string? globalFilter,
        string[] searchableFields
    )
    {
        if (string.IsNullOrEmpty(globalFilter))
            return query;

        var parameter = Expression.Parameter(typeof(T), "x");
        Expression? combinedExpression = null;

        foreach (var field in searchableFields)
        {
            var property = GetNestedProperty(parameter, field);
            if (property == null)
                continue;

            // For enum properties, handle them differently
            if (property.Type.IsEnum)
            {
                var enumContainsExpression = CreateEnumContainsExpression(property, globalFilter);
                if (enumContainsExpression != null)
                {
                    combinedExpression =
                        combinedExpression == null
                            ? enumContainsExpression
                            : Expression.OrElse(combinedExpression, enumContainsExpression);
                }
            }
            else
            {
                // Convert to string and check if contains the filter
                var toString = Expression.Call(property, "ToString", null);
                var toLower = Expression.Call(toString, "ToLower", null);
                var contains = Expression.Call(toLower, "Contains", null, Expression.Constant(globalFilter.ToLower()));

                combinedExpression =
                    combinedExpression == null ? contains : Expression.OrElse(combinedExpression, contains);
            }
        }

        if (combinedExpression == null)
            return query;

        var lambda = Expression.Lambda<Func<T, bool>>(combinedExpression, parameter);
        return query.Where(lambda);
    }

    public static IQueryable<T> ApplyColumnFilters<T>(IQueryable<T> query, List<ColumnFilter> filters)
    {
        foreach (var filter in filters)
        {
            if (filter.Value == null)
                continue;

            var parameter = Expression.Parameter(typeof(T), "x");
            var property = GetNestedProperty(parameter, filter.Field);
            if (property == null)
                continue;

            Expression? filterExpression = filter.MatchMode switch
            {
                ColumnFilter.MatchModeEnum.Contains => CreateContainsExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.Equals => CreateEqualsExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.StartsWith => CreateStartsWithExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.EndsWith => CreateEndsWithExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.Lt => CreateComparisonExpression(
                    property,
                    filter.Value,
                    ExpressionType.LessThan
                ),
                ColumnFilter.MatchModeEnum.Gt => CreateComparisonExpression(
                    property,
                    filter.Value,
                    ExpressionType.GreaterThan
                ),
                ColumnFilter.MatchModeEnum.In => CreateInExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.DateIs => CreateDateIsExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.DateIsNot => CreateDateIsNotExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.DateBefore => CreateDateBeforeExpression(property, filter.Value),
                ColumnFilter.MatchModeEnum.DateAfter => CreateDateAfterExpression(property, filter.Value),
                _ => null,
            };

            if (filterExpression != null)
            {
                var lambda = Expression.Lambda<Func<T, bool>>(filterExpression, parameter);
                query = query.Where(lambda);
            }
        }

        return query;
    }

    public static IQueryable<T> ApplySorting<T>(IQueryable<T> query, List<SortField> sortFields)
    {
        if (sortFields.Count == 0)
            return query;

        IOrderedQueryable<T>? orderedQuery = null;

        foreach (var sort in sortFields)
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = GetNestedProperty(parameter, sort.Field);
            if (property == null)
                continue;

            var lambda = Expression.Lambda(property, parameter);
            var methodName =
                orderedQuery == null
                    ? (sort.Order == SortField.OrderEnum.Desc ? "OrderByDescending" : "OrderBy")
                    : (sort.Order == SortField.OrderEnum.Desc ? "ThenByDescending" : "ThenBy");

            var method = typeof(Queryable)
                .GetMethods()
                .First(m => m.Name == methodName && m.GetParameters().Length == 2)
                .MakeGenericMethod(typeof(T), property.Type);

            // For first sort, use original query; for subsequent sorts, use orderedQuery
            var currentQuery = orderedQuery ?? query;
            orderedQuery = (IOrderedQueryable<T>)method.Invoke(null, [currentQuery, lambda])!;
        }

        return orderedQuery ?? query;
    }

    public static IQueryable<T> ApplyPagination<T>(IQueryable<T> query, int page, int pageSize)
    {
        return query.Skip((page - 1) * pageSize).Take(pageSize);
    }

    private static Expression? GetNestedProperty(Expression parameter, string propertyPath)
    {
        try
        {
            var properties = propertyPath.Split('.');
            Expression property = parameter;

            foreach (var prop in properties)
            {
                property = Expression.Property(property, prop);
            }

            return property;
        }
        catch
        {
            return null;
        }
    }

    private static bool IsCollectionProperty(Type type)
    {
        return type != typeof(string) && typeof(IEnumerable).IsAssignableFrom(type);
    }

    private static Expression CreateContainsExpression(Expression property, object value)
    {
        // Handle collection properties (like List<string>)
        if (IsCollectionProperty(property.Type))
        {
            return CreateCollectionContainsExpression(property, value);
        }

        // Handle enum properties
        if (property.Type.IsEnum)
        {
            return CreateEnumContainsExpression(property, value.ToString()!) ?? Expression.Constant(false);
        }

        // Handle standard properties
        var toString = Expression.Call(property, "ToString", null);
        var toLower = Expression.Call(toString, "ToLower", null);
        return Expression.Call(toLower, "Contains", null, Expression.Constant(value.ToString()!.ToLower()));
    }

    private static Expression CreateStartsWithExpression(Expression property, object value)
    {
        // Handle collection properties
        if (IsCollectionProperty(property.Type))
        {
            return CreateCollectionStartsWithExpression(property, value);
        }

        // For enum properties, we can check if any enum value starts with the search term
        if (property.Type.IsEnum)
        {
            return CreateEnumStartsWithExpression(property, value.ToString()!) ?? Expression.Constant(false);
        }

        var toString = Expression.Call(property, "ToString", null);
        return Expression.Call(toString, "StartsWith", null, Expression.Constant(value.ToString()));
    }

    private static Expression CreateEndsWithExpression(Expression property, object value)
    {
        // Handle collection properties
        if (IsCollectionProperty(property.Type))
        {
            return CreateCollectionEndsWithExpression(property, value);
        }

        // For enum properties, we can check if any enum value ends with the search term
        if (property.Type.IsEnum)
        {
            return CreateEnumEndsWithExpression(property, value.ToString()!) ?? Expression.Constant(false);
        }

        var toString = Expression.Call(property, "ToString", null);
        return Expression.Call(toString, "EndsWith", null, Expression.Constant(value.ToString()));
    }

    private static Expression CreateEqualsExpression(Expression property, object value)
    {
        // Handle collection properties
        if (IsCollectionProperty(property.Type))
        {
            return CreateCollectionEqualsExpression(property, value);
        }

        // Handle enum properties
        if (property.Type.IsEnum && value is string stringValue)
        {
            try
            {
                var enumValue = Enum.Parse(property.Type, stringValue, true);
                var enumConstant = Expression.Constant(enumValue, property.Type);
                return Expression.Equal(property, enumConstant);
            }
            catch
            {
                return Expression.Constant(false);
            }
        }

        try
        {
            var convertedValue = Convert.ChangeType(value, property.Type);
            var valueConstant = Expression.Constant(convertedValue, property.Type);
            return Expression.Equal(property, valueConstant);
        }
        catch
        {
            return Expression.Constant(false);
        }
    }

    private static Expression CreateComparisonExpression(Expression property, object value, ExpressionType comparison)
    {
        try
        {
            var convertedValue = Convert.ChangeType(value, property.Type);
            var valueConstant = Expression.Constant(convertedValue, property.Type);
            return Expression.MakeBinary(comparison, property, valueConstant);
        }
        catch
        {
            return Expression.Constant(false);
        }
    }

    private static Expression CreateInExpression(Expression property, object value)
    {
        if (value is not IEnumerable enumerable)
            return Expression.Constant(false);

        var values = enumerable.Cast<object>().ToList();
        if (values.Count == 0)
            return Expression.Constant(false);

        Expression? orExpression = null;
        foreach (var val in values)
        {
            // Handle enum properties in 'in' expressions
            if (property.Type.IsEnum && val is string stringVal)
            {
                try
                {
                    var enumValue = Enum.Parse(property.Type, stringVal, true);
                    var equals = Expression.Equal(property, Expression.Constant(enumValue, property.Type));
                    orExpression = orExpression == null ? equals : Expression.OrElse(orExpression, equals);
                }
                catch
                {
                    // Skip invalid enum values
                    continue;
                }
            }
            else
            {
                try
                {
                    var convertedValue = Convert.ChangeType(val, property.Type);
                    var equals = Expression.Equal(property, Expression.Constant(convertedValue, property.Type));
                    orExpression = orExpression == null ? equals : Expression.OrElse(orExpression, equals);
                }
                catch
                {
                    // Skip invalid values
                    continue;
                }
            }
        }

        return orExpression ?? Expression.Constant(false);
    }

    private static Expression CreateCollectionContainsExpression(Expression property, object value)
    {
        var searchValue = value.ToString()!;

        // For List<string> properties: collection.Any(item => item != null && item.Contains(searchValue))
        if (property.Type.IsGenericType && property.Type.GetGenericArguments()[0] == typeof(string))
        {
            var itemType = typeof(string);
            var itemParameter = Expression.Parameter(itemType, "item");

            // item != null && item.ToLower().Contains(searchValue.ToLower())
            var notNull = Expression.NotEqual(itemParameter, Expression.Constant(null, typeof(string)));
            var toLower = Expression.Call(itemParameter, "ToLower", null);
            var contains = Expression.Call(toLower, "Contains", null, Expression.Constant(searchValue.ToLower()));
            var condition = Expression.AndAlso(notNull, contains);

            var anyMethod = typeof(Enumerable)
                .GetMethods()
                .First(m => m.Name == "Any" && m.GetParameters().Length == 2)
                .MakeGenericMethod(itemType);

            var lambda = Expression.Lambda(condition, itemParameter);
            return Expression.Call(anyMethod, property, lambda);
        }

        return Expression.Constant(false);
    }

    private static Expression CreateCollectionStartsWithExpression(Expression property, object value)
    {
        var searchValue = value.ToString()!;

        // For List<string> properties: collection.Any(item => item != null && item.StartsWith(searchValue))
        if (property.Type.IsGenericType && property.Type.GetGenericArguments()[0] == typeof(string))
        {
            var itemType = typeof(string);
            var itemParameter = Expression.Parameter(itemType, "item");

            // item != null && item.StartsWith(searchValue)
            var notNull = Expression.NotEqual(itemParameter, Expression.Constant(null, typeof(string)));
            var startsWith = Expression.Call(itemParameter, "StartsWith", null, Expression.Constant(searchValue));
            var condition = Expression.AndAlso(notNull, startsWith);

            var anyMethod = typeof(Enumerable)
                .GetMethods()
                .First(m => m.Name == "Any" && m.GetParameters().Length == 2)
                .MakeGenericMethod(itemType);

            var lambda = Expression.Lambda(condition, itemParameter);
            return Expression.Call(anyMethod, property, lambda);
        }

        return Expression.Constant(false);
    }

    private static Expression CreateCollectionEndsWithExpression(Expression property, object value)
    {
        var searchValue = value.ToString()!;

        // For List<string> properties: collection.Any(item => item != null && item.EndsWith(searchValue))
        if (property.Type.IsGenericType && property.Type.GetGenericArguments()[0] == typeof(string))
        {
            var itemType = typeof(string);
            var itemParameter = Expression.Parameter(itemType, "item");

            // item != null && item.EndsWith(searchValue)
            var notNull = Expression.NotEqual(itemParameter, Expression.Constant(null, typeof(string)));
            var endsWith = Expression.Call(itemParameter, "EndsWith", null, Expression.Constant(searchValue));
            var condition = Expression.AndAlso(notNull, endsWith);

            var anyMethod = typeof(Enumerable)
                .GetMethods()
                .First(m => m.Name == "Any" && m.GetParameters().Length == 2)
                .MakeGenericMethod(itemType);

            var lambda = Expression.Lambda(condition, itemParameter);
            return Expression.Call(anyMethod, property, lambda);
        }

        return Expression.Constant(false);
    }

    private static Expression CreateCollectionEqualsExpression(Expression property, object value)
    {
        var searchValue = value.ToString()!;

        // For List<string> properties: collection.Any(item => item == searchValue)
        if (property.Type.IsGenericType && property.Type.GetGenericArguments()[0] == typeof(string))
        {
            var itemType = typeof(string);
            var itemParameter = Expression.Parameter(itemType, "item");

            // item == searchValue
            var equals = Expression.Equal(itemParameter, Expression.Constant(searchValue));

            var anyMethod = typeof(Enumerable)
                .GetMethods()
                .First(m => m.Name == "Any" && m.GetParameters().Length == 2)
                .MakeGenericMethod(itemType);

            var lambda = Expression.Lambda(equals, itemParameter);
            return Expression.Call(anyMethod, property, lambda);
        }

        return Expression.Constant(false);
    }

    private static Expression? CreateEnumContainsExpression(Expression property, string searchValue)
    {
        if (!property.Type.IsEnum)
            return null;

        var enumType = property.Type;
        var enumNames = Enum.GetNames(enumType);
        var searchValueLower = searchValue.ToLower();

        Expression? orExpression = null;

        foreach (var enumName in enumNames)
        {
            if (enumName.Contains(searchValueLower, StringComparison.InvariantCultureIgnoreCase))
            {
                var enumValue = Enum.Parse(enumType, enumName);
                var enumConstant = Expression.Constant(enumValue, enumType);
                var equals = Expression.Equal(property, enumConstant);

                orExpression = orExpression == null ? equals : Expression.OrElse(orExpression, equals);
            }
        }

        return orExpression;
    }

    private static Expression? CreateEnumStartsWithExpression(Expression property, string searchValue)
    {
        if (!property.Type.IsEnum)
            return null;

        var enumType = property.Type;
        var enumNames = Enum.GetNames(enumType);
        var searchValueLower = searchValue.ToLower();

        Expression? orExpression = null;

        foreach (var enumName in enumNames)
        {
            if (enumName.StartsWith(searchValueLower, StringComparison.InvariantCultureIgnoreCase))
            {
                var enumValue = Enum.Parse(enumType, enumName);
                var enumConstant = Expression.Constant(enumValue, enumType);
                var equals = Expression.Equal(property, enumConstant);

                orExpression = orExpression == null ? equals : Expression.OrElse(orExpression, equals);
            }
        }

        return orExpression;
    }

    private static Expression? CreateEnumEndsWithExpression(Expression property, string searchValue)
    {
        if (!property.Type.IsEnum)
            return null;

        var enumType = property.Type;
        var enumNames = Enum.GetNames(enumType);
        var searchValueLower = searchValue.ToLower();

        Expression? orExpression = null;

        foreach (var enumName in enumNames)
        {
            if (enumName.ToLower().EndsWith(searchValueLower))
            {
                var enumValue = Enum.Parse(enumType, enumName);
                var enumConstant = Expression.Constant(enumValue, enumType);
                var equals = Expression.Equal(property, enumConstant);

                orExpression = orExpression == null ? equals : Expression.OrElse(orExpression, equals);
            }
        }

        return orExpression;
    }

    private static Expression? CreateDateIsExpression(Expression property, object value)
    {
        return CreateDateComparisonExpression(property, value, ExpressionType.Equal);
    }

    private static Expression? CreateDateIsNotExpression(Expression property, object value)
    {
        return CreateDateComparisonExpression(property, value, ExpressionType.NotEqual);
    }

    private static Expression? CreateDateBeforeExpression(Expression property, object value)
    {
        return CreateDateComparisonExpression(property, value, ExpressionType.LessThan);
    }

    private static Expression? CreateDateAfterExpression(Expression property, object value)
    {
        return CreateDateComparisonExpression(property, value, ExpressionType.GreaterThan);
    }

    private static Expression? CreateDateComparisonExpression(
        Expression property,
        object value,
        ExpressionType comparisonType
    )
    {
        try
        {
            var propertyType = property.Type;
            var underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

            if (underlyingType != typeof(DateTime) && underlyingType != typeof(DateOnly))
                return Expression.Constant(false);

            DateTime dateValue;
            if (value is string stringValue)
            {
                if (!DateTime.TryParse(stringValue, out dateValue))
                    return Expression.Constant(false);
            }
            else if (value is DateTime dt)
            {
                dateValue = dt;
            }
            else
            {
                try
                {
                    dateValue = Convert.ToDateTime(value);
                }
                catch
                {
                    return Expression.Constant(false);
                }
            }

            Expression comparison;
            if (underlyingType == typeof(DateTime))
            {
                // For DateTime comparisons, we want to compare only the date part
                // to avoid time-related issues unless specifically comparing with time
                var dateOnlyValue = dateValue.Date;
                var dateConstant = Expression.Constant(dateOnlyValue, typeof(DateTime));

                var propertyDate =
                    property.Type == typeof(DateTime?)
                        ? Expression.Property(Expression.Property(property, "Value"), "Date")
                        : Expression.Property(property, "Date");

                comparison = Expression.MakeBinary(comparisonType, propertyDate, dateConstant);

                if (propertyType == typeof(DateTime?))
                {
                    var hasValue = Expression.Property(property, "HasValue");
                    comparison = Expression.AndAlso(hasValue, comparison);
                }
            }
            else // DateOnly
            {
                var dateOnlyValue = DateOnly.FromDateTime(dateValue);
                var dateConstant = Expression.Constant(dateOnlyValue, typeof(DateOnly));

                if (propertyType == typeof(DateOnly?))
                {
                    var hasValue = Expression.Property(property, "HasValue");
                    var valueProperty = Expression.Property(property, "Value");
                    comparison = Expression.MakeBinary(comparisonType, valueProperty, dateConstant);
                    comparison = Expression.AndAlso(hasValue, comparison);
                }
                else
                {
                    comparison = Expression.MakeBinary(comparisonType, property, dateConstant);
                }
            }

            return comparison;
        }
        catch
        {
            return Expression.Constant(false);
        }
    }
}

using System.Text.RegularExpressions;
using PartnerPortal.Backend.Document.Models;

namespace PartnerPortal.Backend.Document.Utils;

public static partial class AddressValidationHelper
{
    public static bool? ValidateStreetAddress(string? bcAddress, CustomerAddress? aiAddress)
    {
        if (string.IsNullOrEmpty(aiAddress?.FieldContent) && string.IsNullOrEmpty(bcAddress) && aiAddress == null)
            return null;

        if (string.IsNullOrEmpty(bcAddress))
            return false;

        // Normalize BC address
        var normalizedBcAddress = NormalizeAddress(bcAddress);

        // Try to match with the structured address first
        if (aiAddress != null)
        {
            string? aiStreetAddress = null;

            // Try different combinations of fields
            if (!string.IsNullOrEmpty(aiAddress.StreetAddress))
            {
                aiStreetAddress = aiAddress.StreetAddress;
            }
            else if (!string.IsNullOrEmpty(aiAddress.Road) || !string.IsNullOrEmpty(aiAddress.HouseNumber))
            {
                aiStreetAddress = $"{aiAddress.Road} {aiAddress.HouseNumber}".Trim();
            }

            if (!string.IsNullOrEmpty(aiStreetAddress))
            {
                var standardizedAiAddress = StandardizeHouseNumberPosition(aiStreetAddress);
                var normalizedAiAddress = NormalizeAddress(standardizedAiAddress);

                // Check for containment in either direction
                if (
                    normalizedBcAddress.Contains(normalizedAiAddress)
                    || normalizedAiAddress.Contains(normalizedBcAddress)
                )
                    return true;

                // Check for high similarity
                if (TextComparer.CalculateSimilarity(normalizedBcAddress, normalizedAiAddress) > 0.8)
                    return true;
            }
        }

        // If structured validation failed, try with the raw content
        if (!string.IsNullOrEmpty(aiAddress?.FieldContent))
        {
            // Split content into lines and check each line
            var lines = aiAddress.FieldContent.Split(['\n', '\r'], StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in lines)
            {
                var normalizedLine = NormalizeAddress(line);

                // Check for containment in either direction
                if (normalizedBcAddress.Contains(normalizedLine) || normalizedLine.Contains(normalizedBcAddress))
                    return true;

                // Check for high similarity
                if (TextComparer.CalculateSimilarity(normalizedBcAddress, normalizedLine) > 0.8)
                    return true;
            }
        }

        return false;
    }

    private static string NormalizeAddress(string address)
    {
        var result = address
            .ToLowerInvariant()
            .Replace("utca", "u")
            .Replace("út", "u")
            .Replace("u.", "u")
            .Replace(".", "")
            .Replace(",", "")
            .Replace("-", "")
            .Replace(" ", "");

        return result;
    }

    public static string StandardizeHouseNumberPosition(string address)
    {
        string originalAddress = address;

        // Case 1: Simple number (e.g., "51 Bojtár u.")
        var simpleNumberMatch = SimpleNumberRegex().Match(originalAddress);

        // Case 2: Number with period and letter suffix (e.g., "14.B Késmárk u.")
        var periodLetterSuffixMatch = PeriodLetterSuffixRegex().Match(originalAddress);

        // Case 3: Number with letter suffix without period (e.g., "14B Késmárk u.")
        var letterSuffixMatch = LetterSuffixRegex().Match(originalAddress);

        // Case 4: Slash format (e.g., "1/E Kenyérgyári út")
        var slashFormatMatch = SlashFormatRegex().Match(originalAddress);

        // Case 5: Range format (e.g., "160-162 Lajos utca")
        var rangeFormatMatch = RangeFormatRegex().Match(originalAddress);

        // Check matches in order of specificity
        if (rangeFormatMatch.Success)
        {
            var houseNumber = rangeFormatMatch.Groups[1].Value;
            var streetPart = rangeFormatMatch.Groups[2].Value;
            return $"{streetPart} {houseNumber}";
        }
        else if (slashFormatMatch.Success)
        {
            var houseNumber = slashFormatMatch.Groups[1].Value;
            var streetPart = slashFormatMatch.Groups[2].Value;
            return $"{streetPart} {houseNumber}";
        }
        else if (periodLetterSuffixMatch.Success)
        {
            var houseNumber = periodLetterSuffixMatch.Groups[1].Value;
            var streetPart = periodLetterSuffixMatch.Groups[2].Value;
            return $"{streetPart} {houseNumber}";
        }
        else if (letterSuffixMatch.Success)
        {
            var houseNumber = letterSuffixMatch.Groups[1].Value;
            var streetPart = letterSuffixMatch.Groups[2].Value;
            return $"{streetPart} {houseNumber}";
        }
        else if (simpleNumberMatch.Success)
        {
            var houseNumber = simpleNumberMatch.Groups[1].Value;
            var streetPart = simpleNumberMatch.Groups[2].Value;
            return $"{streetPart} {houseNumber}";
        }

        return originalAddress;
    }

    [GeneratedRegex(@"^(\d+)[,\s.]+(.+)$")]
    private static partial Regex SimpleNumberRegex();

    [GeneratedRegex(@"^(\d+\.[A-Za-z])[,\s.]+(.+)$")]
    private static partial Regex PeriodLetterSuffixRegex();

    [GeneratedRegex(@"^(\d+[A-Za-z])[,\s.]+(.+)$")]
    private static partial Regex LetterSuffixRegex();

    [GeneratedRegex(@"^(\d+\/[A-Za-z])[,\s.]+(.+)$")]
    private static partial Regex SlashFormatRegex();

    [GeneratedRegex(@"^(\d+[-–—]\d+)[,\s.]+(.+)$")]
    private static partial Regex RangeFormatRegex();
}

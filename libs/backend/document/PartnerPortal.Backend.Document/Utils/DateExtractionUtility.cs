using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Azure.AI.DocumentIntelligence;

namespace PartnerPortal.Backend.Document.Utils;

public enum DateType
{
    FulfilmentDate,
    ServiceStartDate,
    ServiceEndDate,
}

public static partial class DateExtractionUtility
{
    private static readonly Dictionary<DateType, string[]> DatePrefixes = new()
    {
        [DateType.FulfilmentDate] =
        [
            "a teljesítés dátuma",
            "a teljesítés időpontja",
            "date of performance",
            "delivery date",
            "execution date",
            "fulfilment date",
            "leteljesítés dátuma",
            "megvalósulás dátuma",
            "megvalósulás ideje",
            "performance date",
            "szállítás dátuma",
            "teljesítés dátuma",
            "teljesítés dátum",
            "teljesítés ideje",
            "teljesítés időpontja",
            "teljes<PERSON><PERSON><PERSON> kelte",
            "teljesítés napja",
            "teljes<PERSON><PERSON><PERSON> nap",
            "teljes<PERSON>tési dátum",
            "teljesítési idő",
            "teljesítés",
            "teljesítve ekkor",
            "teljesítve",
            "telj. dátuma",
            "telj. dátum",
            "telj. időpontja",
            "telj. idő",
            "t.i.",
            "(teljesítés időpontja)",
            "telj.dátuma",
            "telj.dátum",
            "telj.időpontja",
            "telj.idő",
            "teljesítés időpontja, helye",
        ],
        [DateType.ServiceStartDate] = ["időszak kezdete"],
        [DateType.ServiceEndDate] = ["időszak vége"],
    };

    private static readonly Dictionary<DateType, Regex> DatePrefixRegexes = CreateDatePrefixRegexes();

    // Common date formats in Hungary and international formats
    private static readonly string[] DateFormats =
    [
        "yyyy-MM-dd", // 2025-01-03
        "yyyy.MM.dd.", // 2025.01.03.
        "yyyy.MM.dd", // 2025.01.03
        "dd.MM.yyyy", // 03.01.2025
        "dd.MM.yyyy.", // 03.01.2025.
        "dd/MM/yyyy", // 03/01/2025
        "yyyy/MM/dd", // 2025/01/03
        "dd-MM-yyyy", // 03-01-2025
        "MM/dd/yyyy", // 01/03/2025
    ];

    private static string RemoveDiacritics(string text)
    {
        if (text == null)
            return string.Empty;
        var normalizedString = text.Normalize(NormalizationForm.FormD);
        var sb = new StringBuilder();
        foreach (var c in normalizedString)
        {
            var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark)
            {
                sb.Append(c);
            }
        }
        return sb.ToString().Normalize(NormalizationForm.FormC);
    }

    private static Dictionary<DateType, Regex> CreateDatePrefixRegexes()
    {
        var regexes = new Dictionary<DateType, Regex>();

        foreach (var kvp in DatePrefixes)
        {
            var deaccentedPrefixes = kvp.Value.Select(RemoveDiacritics);
            string pattern = $"({string.Join("|", deaccentedPrefixes.Select(Regex.Escape))})(:)?[\\s]*([\\d\\.\\-/]+)";
            regexes[kvp.Key] = new Regex(pattern, RegexOptions.IgnoreCase | RegexOptions.CultureInvariant);
        }

        return regexes;
    }

    /// <summary>
    /// Extracts a date from the given text, looking for known date prefixes of the specified type followed by a date string.
    /// Returns null if multiple matches are found to avoid ambiguity.
    /// </summary>
    /// <param name="text">The text to extract the date from</param>
    /// <param name="dateType">The type of date to extract</param>
    public static DateTime? ExtractDateFromContent(string text, DateType dateType)
    {
        if (string.IsNullOrEmpty(text))
            return null;

        if (!DatePrefixRegexes.TryGetValue(dateType, out var regex))
            return null;

        var normalizedText = RemoveDiacritics(text);
        var matches = regex.Matches(normalizedText);

        var validDates = new List<DateTime>();

        foreach (Match match in matches)
        {
            if (match.Groups.Count < 4)
                continue;

            // The date string is in group 3
            string dateStr = match.Groups[3].Value.Trim();

            if (TryParseDate(dateStr, out DateTime result))
            {
                validDates.Add(result);
            }
        }

        if (validDates.Count == 0)
            return null;

        if (validDates.Count > 1)
        {
            var firstDate = validDates[0];
            // If all dates are the same, we can safely return it
            if (validDates.All(d => d == firstDate))
                return firstDate;

            // If dates are different, we can't decide which one is correct - return null
            return null;
        }

        return validDates[0];
    }

    private static bool TryParseDate(string dateStr, out DateTime result)
    {
        // Try parsing with each known format
        foreach (var format in DateFormats)
        {
            if (DateTime.TryParseExact(dateStr, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out result))
            {
                return true;
            }
        }

        return DateTime.TryParse(dateStr, CultureInfo.InvariantCulture, DateTimeStyles.None, out result);
    }

    /// <summary>
    /// Checks if the provided text matches any of the known date prefixes for the specified type,
    /// ignoring special characters like ":" or ")" in the comparison.
    /// </summary>
    /// <param name="text">The text to check for matching</param>
    /// <param name="dateType">The type of date prefix to check for</param>
    /// <returns>True if a match is found, otherwise false</returns>
    public static bool IsDatePrefix(string text, DateType dateType)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        if (!DatePrefixes.TryGetValue(dateType, out var prefixes))
            return false;

        // Normalize input text (trim, remove special characters)
        string normalizedText = NormalizeRegex().Replace(text.Trim(), "");

        // Check if the normalized text matches any of the prefixes using culture-invariant comparison
        return prefixes.Any(prefix =>
        {
            var normalizedPrefix = NormalizeRegex().Replace(prefix, "");
            return string.Compare(
                    normalizedText,
                    normalizedPrefix,
                    CultureInfo.InvariantCulture,
                    CompareOptions.IgnoreCase | CompareOptions.IgnoreNonSpace
                ) == 0;
        });
    }

    /// <summary>
    /// Extracts dates from tables based on the specified date type.
    /// </summary>
    /// <param name="tables">The tables to extract dates from</param>
    /// <param name="dateType">The type of date to extract</param>
    /// <returns>The extracted date if found, otherwise null</returns>
    public static DateTime? ExtractDateFromTables(IEnumerable<DocumentTable> tables, DateType dateType)
    {
        foreach (var table in tables)
        {
            foreach (var cell in table.Cells)
            {
                if (string.IsNullOrEmpty(cell.Content))
                    continue;

                var dateInCell = ExtractDateFromContent(cell.Content, dateType);
                if (dateInCell != null)
                    return dateInCell;

                if (IsDatePrefix(cell.Content, dateType))
                {
                    // Find cells in the same column but in the next row
                    var valueCell = table
                        .Cells.Where(c => c.ColumnIndex == cell.ColumnIndex && c.RowIndex == cell.RowIndex + 1)
                        .FirstOrDefault();

                    if (!string.IsNullOrEmpty(valueCell?.Content))
                    {
                        // Try parsing with our existing date formats first
                        if (TryParseDate(valueCell.Content, out DateTime date))
                        {
                            return date;
                        }
                    }
                }
            }
        }
        return null;
    }

    [GeneratedRegex("[:\\(\\)]")]
    private static partial Regex NormalizeRegex();
}

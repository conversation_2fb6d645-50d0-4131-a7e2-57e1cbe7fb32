using System.Globalization;
using System.Text;

namespace PartnerPortal.Backend.Document.Utils;

public static class PaymentMethodUtility
{
    private static readonly string[] WireTransferTokens =
    [
        // Hungarian
        "utalas", // e.g., "utal<PERSON>", "átu<PERSON><PERSON>", "utal<PERSON>sal"
        "atut", // e.g., abbreviation "átut."
        // English
        "banktransfer",
        "wiretransfer",
        // German
        "uberweisung",
        "bankuberweisung",
        // SEPA variants
        "sepabanktransfer",
        "sepatransfer",
        "sepauberweisung",
    ];

    /// <summary>
    /// Returns true if the input clearly indicates a bank/wire transfer payment method
    /// in Hungarian, English, or German, including common abbreviations.
    /// </summary>
    public static bool IsWireTransfer(string? paymentMethodText)
    {
        if (string.IsNullOrWhiteSpace(paymentMethodText))
        {
            return false;
        }

        string normalized = NormalizeForMatch(paymentMethodText);

        foreach (string token in WireTransferTokens)
        {
            if (normalized.Contains(token, StringComparison.Ordinal))
            {
                return true;
            }
        }

        return false;
    }

    private static string NormalizeForMatch(string text)
    {
        // 1) Lowercase
        string lower = text.ToLowerInvariant();

        // 2) Remove diacritics
        string decomposed = lower.Normalize(NormalizationForm.FormD);
        var sb = new StringBuilder(decomposed.Length);
        foreach (char c in decomposed)
        {
            var uc = CharUnicodeInfo.GetUnicodeCategory(c);
            if (uc != UnicodeCategory.NonSpacingMark)
            {
                sb.Append(c);
            }
        }
        string noDiacritics = sb.ToString().Normalize(NormalizationForm.FormC);

        // 3) Remove non-alphanumeric characters (spaces, punctuation, slashes, etc.)
        sb.Clear();
        foreach (char c in noDiacritics)
        {
            if (char.IsLetterOrDigit(c))
            {
                sb.Append(c);
            }
        }

        return sb.ToString();
    }
}

using System.Text.Json;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;
using Quartz;

namespace PartnerPortal.Backend.Document.Utils;

public static class JobRetryHelper
{
    private const int MaxRetries = 3;

    public static bool ShouldRetry(int currentRetryCount, JobOperationStatus status)
    {
        // Only retry for timeout or errors
        return currentRetryCount < MaxRetries
            && (status == JobOperationStatus.Timeout || status == JobOperationStatus.Error);
    }

    public static async Task ScheduleRetry(
        IJobExecutionContext context,
        DocumentJobInputData inputData,
        JobOperationResult result,
        ILogger logger
    )
    {
        var retryData = new DocumentJobInputData
        {
            DocumentUploadId = inputData.DocumentUploadId,
            ItemId = inputData.ItemId,
            RetryCount = inputData.RetryCount + 1,
            UploadType = inputData.UploadType,
        };

        var retryCount = retryData.RetryCount;
        var delayWithJitter = RetryUtility.CalculateExponentialBackoffWithJitter(retryCount);
        var oldTrigger = context.Trigger;
        var retryTrigger = TriggerBuilder
            .Create()
            .ForJob(context.JobDetail)
            .WithIdentity($"{oldTrigger.Key.Name}-retry-{retryCount}", oldTrigger.Key.Group)
            .UsingJobData("jobData", JsonSerializer.Serialize(retryData))
            .StartAt(DateTimeOffset.UtcNow.AddSeconds(delayWithJitter))
            .Build();

        await context.Scheduler.ScheduleJob(retryTrigger);

        logger.LogInformation(
            "Scheduled retry #{RetryCount} for document {AttachmentDocumentTypeId} after {Delay:0.0} seconds. Status: {Status}",
            retryCount,
            inputData.ItemId,
            delayWithJitter,
            result.Status
        );
    }
}

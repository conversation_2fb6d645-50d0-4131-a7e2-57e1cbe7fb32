using System.Threading.RateLimiting;
using Microsoft.Extensions.Logging;
using Polly;

namespace PartnerPortal.Backend.Document.Utils;

/// <summary>
/// Utility for managing API rate limiting with evenly spaced requests
/// </summary>
public static class AzureApiRateLimiter
{
    // Semaphore to control concurrency across all uses
    private static readonly SemaphoreSlim RequestSemaphore = new(1, 1);

    // Last request timestamp for calculating delay
    private static DateTime _lastRequestTime = DateTime.MinValue;

    /// <summary>
    /// Ensures requests are evenly spaced based on the specified rate limit
    /// </summary>
    /// <param name="maxRequestsPerSecond">Maximum number of requests per second</param>
    /// <param name="logger">Optional logger for debugging</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public static async Task EnsureEvenRequestSpacing(int maxRequestsPerSecond, ILogger? logger = null)
    {
        // Calculate minimum time between requests
        TimeSpan minTimeBetweenRequests = TimeSpan.FromMilliseconds(1000.0 / maxRequestsPerSecond);

        await RequestSemaphore.WaitAsync();
        try
        {
            var now = DateTime.UtcNow;
            var timeSinceLastRequest = now - _lastRequestTime;

            // If less than minimum time has passed, delay the request
            if (timeSinceLastRequest < minTimeBetweenRequests)
            {
                var delayTime = minTimeBetweenRequests - timeSinceLastRequest;
                logger?.LogTrace("Spacing request by {DelayMs}ms", delayTime.TotalMilliseconds);
                await Task.Delay(delayTime);
            }

            // Update last request time after any delay
            _lastRequestTime = DateTime.UtcNow;
        }
        finally
        {
            RequestSemaphore.Release();
        }
    }

    /// <summary>
    /// Creates a rate limiter configuration for evenly spaced requests
    /// </summary>
    /// <param name="maxRequestsPerSecond">Maximum number of requests per second</param>
    /// <param name="action">The action to execute after applying rate limiting</param>
    /// <param name="logger">Optional logger for debugging</param>
    /// <returns>A task that ensures even spacing before completion</returns>
    public static async Task SpaceEvenly(Func<Task> action, ILogger? logger = null)
    {
        await EnsureEvenRequestSpacing(MaxRequestsPerSecond, logger);
        await action();
    }

    /// <summary>
    /// Creates a rate limiter configuration for evenly spaced requests with return value
    /// </summary>
    /// <typeparam name="T">The type returned by the action</typeparam>
    /// <param name="maxRequestsPerSecond">Maximum number of requests per second</param>
    /// <param name="action">The action to execute after applying rate limiting</param>
    /// <param name="logger">Optional logger for debugging</param>
    /// <returns>The result from the action</returns>
    public static async Task<T> SpaceEvenly<T>(Func<Task<T>> action, ILogger? logger = null)
    {
        await EnsureEvenRequestSpacing(MaxRequestsPerSecond, logger);
        return await action();
    }

    /// <summary>
    /// Maximum requests per second (staying safely below the 15/sec API limit of Azure Document Intelligence)
    /// </summary>
    public const int MaxRequestsPerSecond = 14;

    public static readonly ResiliencePipeline ResiliencePipeline = new ResiliencePipelineBuilder()
        .AddRateLimiter(
            new TokenBucketRateLimiter(
                new TokenBucketRateLimiterOptions
                {
                    TokensPerPeriod = MaxRequestsPerSecond,
                    ReplenishmentPeriod = TimeSpan.FromSeconds(1),
                    TokenLimit = MaxRequestsPerSecond,
                    QueueLimit = int.MaxValue,
                }
            )
        )
        .AddTimeout(TimeSpan.FromMinutes(10))
        .Build();
}

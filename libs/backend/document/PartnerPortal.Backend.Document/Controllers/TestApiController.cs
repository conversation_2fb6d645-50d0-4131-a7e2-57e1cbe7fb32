#if DEBUG
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services.BcService;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Document.Utils;
using Nav.Invoice.Client;
using Nav.Invoice.Client.Models;
using Nav.Invoice.Client.Configuration;
using Microsoft.Extensions.Options;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Data;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Services.Elo;

namespace PartnerPortal.Backend.Document.Controllers;

[Route("api/[controller]")]
[ApiController]
public class TestApiController : ControllerBase
{
    private readonly IAzureApiService _azureApiService;
    private readonly ILogger<TestApiController> _logger;
    private readonly IProcessingManagerService _documentProcessingService;
    private readonly IDocumentValidationService _documentValidationService;
    private readonly IBcService _bcPartnersService;
    private readonly IEloService _eloService;
    private readonly IOptions<NavConfig> _navModeConfig;
    private readonly UserDbContext _context;

    public TestApiController(
        IAzureApiService azureApiService,
        ILogger<TestApiController> logger,
        IProcessingManagerService documentProcessingService,
        IDocumentValidationService documentValidationService,
        IBcService bcPartnersService,
        IEloService eloService,
        IOptions<NavConfig> navModeConfig,
        UserDbContext context
    )
    {
        _azureApiService = azureApiService;
        _logger = logger;
        _documentProcessingService = documentProcessingService;
        _documentValidationService = documentValidationService;
        _bcPartnersService = bcPartnersService;
        _eloService = eloService;
        _navModeConfig = navModeConfig;
        _context = context;
    }

    [HttpGet("analyze")]
    public Task<IActionResult> AnalyzeLocalDocument(
        [FromQuery] string documentPath = "file:///Users/<USER>/Downloads/V01145_25_00960.pdf"
    )
    {
        try
        {
            _logger.LogInformation("Document path received: {DocumentPath}", documentPath);

            // Convert file URI to local path if necessary
            string localFilePath;
            if (documentPath.StartsWith("file:///"))
            {
                // Extract the actual filepath from the URI
                localFilePath = Uri.UnescapeDataString(new Uri(documentPath).LocalPath);
                _logger.LogInformation("Converted to local path: {LocalPath}", localFilePath);
            }
            else
            {
                localFilePath = documentPath;
            }

            //var result = await _documentProcessingService.ProcessDocument(localFilePath);
            return Task.FromResult<IActionResult>(Ok());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing local document");
            return Task.FromResult<IActionResult>(
                StatusCode(500, new { error = "Internal server error", message = ex.Message })
            );
        }
    }

    [HttpGet("test-document-processing")]
    public Task<IActionResult> TestDocumentProcessing(
        [FromQuery]
            string documentUrl =
            "https://raw.githubusercontent.com/Azure-Samples/cognitive-services-REST-api-samples/master/curl/form-recognizer/sample-invoice.pdf"
    )
    {
        var list = new List<(string DocumentId, Uri DocumentUri)>();
        for (int i = 0; i < 1; i++)
        {
            list.Add((i.ToString(), new Uri(documentUrl)));
        }
        //await _documentProcessingService.QueueDocumentsForProcessing(list);
        return Task.FromResult<IActionResult>(Ok());
    }

    [HttpGet("validation")]
    public async Task<IActionResult> TestValidation()
    {
        var result = await _documentValidationService.ValidateDocument(
            new DocumentJobInputData
            {
                ItemId = 1104,
                DocumentUploadId = Guid.Parse("1F01AC3B-07B7-6070-92D6-7DA91C5A7EE6"),
            }
        );
        return Ok(result);
    }

    [HttpGet("test-pdf-merge")]
    public async Task<IActionResult> TestPdfMerge()
    {
        var result = await PdfHelper.MergeFilesAsync(
            [
                "/Users/<USER>/Downloads/KG_2024-018993.pdf",
                "/Users/<USER>/Downloads/bau-systeme_92_kft_kj24-z0251.pdf",
                "/Users/<USER>/Downloads/LD_2025_1_Vilati_Hungaroring_Paddock_PA1_PA3_es_TIG.pdf",
                "/Users/<USER>/Downloads/vass-balazs.JPG",
                "/Users/<USER>/Downloads/asd.png",
            ]
        );
        await PdfHelper.SavePdfToFileAsync(result, "/Users/<USER>/Downloads/asd.pdf");
        return Ok();
    }

    [HttpGet("test-pdf-merge-range")]
    public async Task<IActionResult> TestPdfMergeRange()
    {
        var result = await PdfHelper.MergeFilesWithPageRangesAsync(
            [
                ("/Users/<USER>/Downloads/KG_2024-018993.pdf", 1, 1),
                ("/Users/<USER>/Downloads/bau-systeme_92_kft_kj24-z0251.pdf", 1, 1),
                ("/Users/<USER>/Downloads/LD_2025_1_Vilati_Hungaroring_Paddock_PA1_PA3_es_TIG.pdf", 3, 3),
                ("/Users/<USER>/Downloads/vass-balazs.JPG", 1, 1),
                ("/Users/<USER>/Downloads/asd.png", 1, 1),
            ]
        );
        await PdfHelper.SavePdfToFileAsync(result, "/Users/<USER>/Downloads/asd.pdf");
        return Ok();
    }

    [HttpGet("test-nav-invoice")]
    public async Task<IActionResult> TestNavInvoice()
    {
        var userData = new UserData
        {
            Login = "uc9sm4xdszcgwe0",
            Password = "IBTtechnikai25",
            TaxNumber = "13327824",
            SignKey = "e8-89bb-d757f484ba73517TBDZP25GJ",
            ExchangeKey = "5745517TBDZO0IN6",
        };

        var softwareData = new Nav.Invoice.Client.Models.SoftwareType
        {
            SoftwareId = _navModeConfig.Value.SoftwareType.SoftwareId,
            SoftwareName = _navModeConfig.Value.SoftwareType.SoftwareName,
            SoftwareOperation = _navModeConfig.Value.SoftwareType.SoftwareOperation,
            SoftwareMainVersion = _navModeConfig.Value.SoftwareType.SoftwareMainVersion,
            SoftwareDevName = _navModeConfig.Value.SoftwareType.SoftwareDevName,
            SoftwareDevContact = _navModeConfig.Value.SoftwareType.SoftwareDevContact,
        };

        var navMode = _navModeConfig.Value.NavMode;
        _logger.LogInformation("Software type: {SoftwareType}", _navModeConfig.Value.SoftwareType);
        _logger.LogInformation("Nav mode: {NavMode}", navMode);

        var config = new NavConfiguration(
            NavConfiguration.TestUrl,
            userData,
            softwareData,
            logger: _logger
        ).EnableLogging(LoggingOptions.ForDevelopment());

        using var client = new NavClient(config);

        // Query invoice data
        var result = await client.QueryInvoiceDataAsync(
            new InvoiceNumberQueryType
            {
                InvoiceNumber = "4013835481",
                InvoiceDirection = InvoiceDirectionType.Inbound,
                SupplierTaxNumber = "10216945",
            }
        );

        return Ok(result);
    }

    [HttpPost("generate-fake-partner-documents")]
    public async Task<IActionResult> GenerateFakePartnerDocuments([FromQuery] int count = 5)
    {
        try
        {
            _logger.LogInformation("Generating {Count} fake partner documents", count);

            var attachmentIds = new[]
            {
                9072,
                9073,
                9074,
                9075,
                9077,
                9078,
                9079,
                9080,
                9081,
                10081,
                10082,
                10083,
                10084,
                11084,
                11085,
            };
            var random = new Random();
            var createdDocuments = new List<object>();
            var usedSpNumbers = new HashSet<string>();

            for (int i = 0; i < count; i++)
            {
                var documentUpload = new PartnerDocumentUpload
                {
                    Id = Guid.NewGuid(),
                    UserId = 1, // Assuming user ID 1 exists
                    ProjectNo = $"PRJ-{random.Next(1000, 9999)}",
                    ProjectDescription = $"Test Project {i + 1} - Generated Data",
                    ContractNo = $"CNT-{random.Next(100, 999)}",
                    Type = random.Next(2) == 0 ? AccountTypeEnum.TigInvoice : AccountTypeEnum.NonTigAccount,
                    Status = random.Next(2) == 0 ? UploadStatusEnum.Draft : UploadStatusEnum.Submitted,
                    Comment = $"Generated test comment for document {i + 1}",
                    AttachmentDocumentTypes = [],
                };

                // Add random attachments
                var attachmentCount = random.Next(1, 4);
                var processingSourcesCreated = new List<int>();

                for (int j = 0; j < attachmentCount; j++)
                {
                    var attachmentId = attachmentIds[random.Next(attachmentIds.Length)];
                    var docType =
                        j == 0
                            ? DocumentType.Invoice
                            : (random.Next(3) == 0 ? DocumentType.CompletionCert : DocumentType.Other);

                    string? spIdentifier = null;
                    if (
                        documentUpload.Status == UploadStatusEnum.Submitted
                        && (docType == DocumentType.Invoice || docType == DocumentType.CompletionCert)
                    )
                    {
                        // Generate unique SP identifier with random date
                        do
                        {
                            var randomYear = random.Next(2023, 2026);
                            var randomMonth = random.Next(1, 13);
                            var randomDay = random.Next(1, DateTime.DaysInMonth(randomYear, randomMonth) + 1);
                            var randomNumber = random.Next(1000, 9999);
                            spIdentifier = $"SP{randomYear:0000}{randomMonth:00}{randomDay:00}{randomNumber}";
                        } while (usedSpNumbers.Contains(spIdentifier));

                        usedSpNumbers.Add(spIdentifier);
                    }

                    var attachmentDocumentType = new AttachmentDocumentType
                    {
                        DocumentUploadId = documentUpload.Id,
                        AttachmentId = attachmentId,
                        DocumentType = docType,
                        SpIdentifier = spIdentifier,
                    };

                    documentUpload.AttachmentDocumentTypes.Add(attachmentDocumentType);
                }

                if (documentUpload.Status == UploadStatusEnum.Submitted)
                {
                    documentUpload.DateOfArrival = DateTime.Now.AddDays(-random.Next(1, 30));
                }

                // Save document upload first
                await _context.DocumentUploads.AddAsync(documentUpload);
                await _context.SaveChangesAsync();

                // Create processing results for submitted documents
                if (documentUpload.Status == UploadStatusEnum.Submitted)
                {
                    foreach (
                        var attachmentDocType in documentUpload.AttachmentDocumentTypes.Where(adt =>
                            adt.DocumentType == DocumentType.Invoice || adt.DocumentType == DocumentType.CompletionCert
                        )
                    )
                    {
                        processingSourcesCreated.Add(attachmentDocType.Id);

                        // Create DocumentAnalysisResult (for both Invoice and CompletionCert)
                        var analysisResult = new DocumentAnalysisResult
                        {
                            DocumentUploadId = documentUpload.Id,
                            ProcessingSourceId = attachmentDocType.Id,
                            Status =
                                random.Next(10) < 9
                                    ? DocumentAnalysisStatusEnum.Success
                                    : DocumentAnalysisStatusEnum.Failed,
                            RetryCount = random.Next(2),
                            CreatedBy = "System",
                            CreatedDate = DateTime.UtcNow,
                            ModifiedBy = "System",
                            LastModDate = DateTime.UtcNow,
                        };

                        if (analysisResult.Status != DocumentAnalysisStatusEnum.Success)
                        {
                            analysisResult.SetErrorMessage("Simulated analysis error for testing purposes");
                        }

                        await _context.DocumentAnalysisResults.AddAsync(analysisResult);

                        // Create DocumentValidationResult (for both Invoice and CompletionCert)
                        // Status depends on analysis success
                        DocumentValidationStatusEnum validationStatus;
                        string? validationJson = null;

                        if (analysisResult.Status == DocumentAnalysisStatusEnum.Success)
                        {
                            // Analysis succeeded, so validation can proceed
                            validationStatus =
                                random.Next(10) < 8
                                    ? DocumentValidationStatusEnum.Success
                                    : (
                                        random.Next(2) == 0
                                            ? DocumentValidationStatusEnum.BcFailed
                                            : DocumentValidationStatusEnum.TechnicalError
                                    );
                            validationJson = GenerateFakeValidationJson(attachmentDocType.DocumentType);
                        }
                        else
                        {
                            // Analysis failed, so validation stays pending
                            validationStatus = DocumentValidationStatusEnum.Pending;
                        }

                        var validationResult = new DocumentValidationResult
                        {
                            DocumentUploadId = documentUpload.Id,
                            ProcessingSourceId = attachmentDocType.Id,
                            Status = validationStatus,
                            ValidationResultJson = validationJson,
                            RetryCount = validationStatus == DocumentValidationStatusEnum.Pending ? 0 : random.Next(3),
                            CreatedBy = "System",
                            CreatedDate = DateTime.UtcNow,
                            ModifiedBy = "System",
                            LastModDate = DateTime.UtcNow,
                        };

                        if (
                            validationResult.Status != DocumentValidationStatusEnum.Success
                            && validationResult.Status != DocumentValidationStatusEnum.Pending
                        )
                        {
                            validationResult.SetErrorMessage("Simulated validation error for testing purposes");
                        }

                        await _context.DocumentValidationResults.AddAsync(validationResult);

                        // Create DocumentEloResult (only for Invoices)
                        if (attachmentDocType.DocumentType == DocumentType.Invoice)
                        {
                            // ELO status depends on both analysis and validation success
                            DocumentEloStatusEnum eloStatus;
                            string? eloResult = null;
                            string? eloGuid = null;
                            string? eloRegId = null;
                            string? bcId = null;
                            string? bcRegId = null;
                            string? fileId = null;
                            string? workflowId = null;
                            string? createRequestXml = null;
                            string? createResponseXml = null;
                            string? uploadRequestXml = null;
                            string? uploadResponseXml = null;
                            string? startWorkflowRequestXml = null;
                            string? startWorkflowResponseXml = null;

                            if (
                                analysisResult.Status == DocumentAnalysisStatusEnum.Success
                                && validationResult.Status == DocumentValidationStatusEnum.Success
                            )
                            {
                                // Both analysis and validation succeeded, so ELO can proceed
                                eloStatus =
                                    random.Next(10) < 8 ? DocumentEloStatusEnum.Success : DocumentEloStatusEnum.Failed;

                                if (eloStatus == DocumentEloStatusEnum.Success)
                                {
                                    eloResult = "Document processed successfully";
                                    eloGuid = Guid.NewGuid().ToString();
                                    eloRegId = $"ELO-{random.Next(100000, 999999)}";
                                    bcId = $"BC-{random.Next(10000, 99999)}";
                                    bcRegId = $"BCR-{random.Next(10000, 99999)}";
                                    fileId = $"FILE-{random.Next(100000, 999999)}";
                                    workflowId = $"WF-{random.Next(1000, 9999)}";
                                    createRequestXml = GenerateFakeEloXml("CreateRequest");
                                    createResponseXml = GenerateFakeEloXml("CreateResponse");
                                    uploadRequestXml = GenerateFakeEloXml("UploadRequest");
                                    uploadResponseXml = GenerateFakeEloXml("UploadResponse");
                                    startWorkflowRequestXml = GenerateFakeEloXml("StartWorkflowRequest");
                                    startWorkflowResponseXml = GenerateFakeEloXml("StartWorkflowResponse");
                                }
                            }
                            else
                            {
                                // Analysis or validation failed, so ELO stays pending
                                eloStatus = DocumentEloStatusEnum.Pending;
                            }

                            var eloResultEntity = new DocumentEloResult
                            {
                                DocumentUploadId = documentUpload.Id,
                                ProcessingSourceId = attachmentDocType.Id,
                                Status = eloStatus,
                                Result = eloResult,
                                RetryCount = eloStatus == DocumentEloStatusEnum.Pending ? 0 : random.Next(2),
                                EloGuid = eloGuid,
                                EloRegId = eloRegId,
                                BcId = bcId,
                                BcRegId = bcRegId,
                                FileId = fileId,
                                WorkflowId = workflowId,
                                CreateRequestXml = createRequestXml,
                                CreateResponseXml = createResponseXml,
                                UploadRequestXml = uploadRequestXml,
                                UploadResponseXml = uploadResponseXml,
                                StartWorkflowRequestXml = startWorkflowRequestXml,
                                StartWorkflowResponseXml = startWorkflowResponseXml,
                                CreatedBy = "System",
                                CreatedDate = DateTime.UtcNow,
                                ModifiedBy = "System",
                                LastModDate = DateTime.UtcNow,
                            };

                            if (
                                eloResultEntity.Status != DocumentEloStatusEnum.Success
                                && eloResultEntity.Status != DocumentEloStatusEnum.Pending
                            )
                            {
                                eloResultEntity.SetErrorMessage("Simulated ELO processing error for testing purposes");
                            }

                            await _context.DocumentEloResults.AddAsync(eloResultEntity);
                        }

                        await _context.SaveChangesAsync();
                    }
                }

                createdDocuments.Add(
                    new
                    {
                        documentUpload.Id,
                        Status = documentUpload.Status.ToString(),
                        Type = documentUpload.Type.ToString(),
                        AttachmentCount = documentUpload.AttachmentDocumentTypes.Count,
                        ProcessingSourcesCreated = processingSourcesCreated.Count,
                        SpIdentifiers = documentUpload
                            .AttachmentDocumentTypes.Where(adt => !string.IsNullOrEmpty(adt.SpIdentifier))
                            .Select(adt => adt.SpIdentifier)
                            .ToList(),
                    }
                );

                _logger.LogInformation(
                    "Created document upload {Id} with status {Status} and {ProcessingResultCount} processing results",
                    documentUpload.Id,
                    documentUpload.Status,
                    processingSourcesCreated.Count
                );
            }

            return Ok(
                new { Message = $"Successfully generated {count} fake partner documents", Documents = createdDocuments }
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating fake partner documents");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    private static string GenerateFakeValidationJson(DocumentType documentType)
    {
        return documentType switch
        {
            DocumentType.Invoice => """
                [
                    {
                        "EloColumnName": "CustomerTaxId",
                        "Status": 1,
                        "BcColumnValue": "********-2-44",
                        "DocumentAiColumnValue": "********-2-44",
                        "ErrorMessage": ""
                    },
                    {
                        "EloColumnName": "CompanyId",
                        "Status": 1,
                        "BcColumnValue": "MARK",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "CustomerName",
                        "Status": 1,
                        "BcColumnValue": "Market Építő Zrt.",
                        "DocumentAiColumnValue": "Market Építő Zrt.",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "PostalCode",
                        "Status": 1,
                        "BcColumnValue": "1037",
                        "DocumentAiColumnValue": "1037",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "City",
                        "Status": 1,
                        "BcColumnValue": "Budapest",
                        "DocumentAiColumnValue": "Budapest",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "StreetAddress",
                        "Status": 1,
                        "BcColumnValue": "Bojtár utca 51.",
                        "DocumentAiColumnValue": "51 Bojtár utca",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "FulfilmentDate",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "********",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "InvoiceDate",
                        "Status": 2,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "20250528",
                        "ErrorMessage": "Invoice date is before fulfilment date"
                    },
                    {
                        "EloColumnName": "TaxRate",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "Mentes",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "Currency",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "Items",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "[{\"Description\":\"Biztosítási szolgáltatás\",\"TaxRate\":\"Mentes\",\"CurrencyCode\":\"HUF\"}]",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "PaymentTerm",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "Banki átutalás",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "VendorTaxId",
                        "Status": 1,
                        "BcColumnValue": "********-4-44",
                        "DocumentAiColumnValue": "********-4-44",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "PartnerId",
                        "Status": 1,
                        "BcColumnValue": "10277",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "BankAccountNo",
                        "Status": 1,
                        "BcColumnValue": "********-********-********",
                        "DocumentAiColumnValue": "********-********-********",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "BankAccountId",
                        "Status": 1,
                        "BcColumnValue": "BK004",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "InvoiceId",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "ACSBM-017013-2025",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "DueDate",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "********",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "InvoiceTotalAmount",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "1752801",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "InvoiceTotalCurrency",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "SubTotalAmount",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "1752801",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "SubTotalCurrency",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "TotalTaxAmount",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "0",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "TotalTaxCurrency",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "ServiceStartDate",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "********",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "ServiceEndDate",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "20260703",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "InvoiceGrossAmountToCompletionCertGrossAmount",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "[]",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "Eladó BC-ből",
                        "Status": 1,
                        "BcColumnValue": "{\"Id\":241137,\"ContactNo\":\"10277\",\"Name\":\"Alfa Vienna Insurance Group Zrt\",\"CompanyName\":\"Alfa Vienna Insurance Group Zrt\",\"CompanyCode\":\"Közös Param\",\"Email\":\"<EMAIL>\",\"VatRegistrationNo\":\"********-4-44\",\"EuVatRegistrationNo\":\"HU********\",\"Address\":\"Üllői út 1.\",\"City\":\"Budapest\",\"PostCode\":\"1091\",\"CountryRegionCode\":\"HU\",\"PhoneNo\":\"\",\"MobilePhoneNo\":\"\",\"LastUpdated\":\"2025-06-16T12:07:22.1415719\",\"ContactBanks\":[{\"BankCode\":\"BK001\",\"BankAccountNo\":\"********-********-********\"},{\"BankCode\":\"BK002\",\"BankAccountNo\":\"********-********-********\"},{\"BankCode\":\"BK003\",\"BankAccountNo\":\"********-********-********\"},{\"BankCode\":\"BK004\",\"BankAccountNo\":\"********-********-********\"}]}",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": "Only for debugging"
                    },
                    {
                        "EloColumnName": "Vevő BC-ből",
                        "Status": 1,
                        "BcColumnValue": "{\"Id\":242631,\"ContactNo\":\"13774\",\"Name\":\"Market Építő Zrt.\",\"CompanyName\":\"Market Építő Zrt.\",\"CompanyCode\":\"Közös Param\",\"Email\":\"<EMAIL>\",\"VatRegistrationNo\":\"********-2-44\",\"EuVatRegistrationNo\":\"HU********\",\"Address\":\"Bojtár utca 51.\",\"City\":\"Budapest\",\"PostCode\":\"1037\",\"CountryRegionCode\":\"HU\",\"PhoneNo\":\"+36 1 279 2727\",\"MobilePhoneNo\":\"\",\"LastUpdated\":\"2025-06-16T12:07:22.1428735\",\"ContactBanks\":[{\"BankCode\":\"BK001\",\"BankAccountNo\":\"********-********-********\"},{\"BankCode\":\"BK002\",\"BankAccountNo\":\"********-********-********\"},{\"BankCode\":\"BK003\",\"BankAccountNo\":\"********-********-********\"}]}",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": "Only for debugging"
                    },
                    {
                        "EloColumnName": "Kivont banki számlaszámok",
                        "Status": 1,
                        "BcColumnValue": "[\"********-********-********\"]",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": "Only for debugging"
                    }
                    ]
                """,
            DocumentType.CompletionCert => """
                [
                    {
                        "EloColumnName": "TigId",
                        "Status": 1,
                        "BcColumnValue": "MORA-TIG25001173",
                        "DocumentAiColumnValue": "MORA-TIG25001173",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "ExecutionDate",
                        "Status": 1,
                        "BcColumnValue": "********",
                        "DocumentAiColumnValue": "********",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "TigStatus",
                        "Status": 1,
                        "BcColumnValue": "60 Elküldve",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "currentGrossAmount",
                        "Status": 2,
                        "BcColumnValue": "10644076",
                        "DocumentAiColumnValue": "256.819.032",
                        "ErrorMessage": "A bevétel összege nem egyezik meg a dokumentumban megadott értékkel"
                    },
                    {
                        "EloColumnName": "ProjectName",
                        "Status": 2,
                        "BcColumnValue": "Bp. XI. Budapart BOM Irodaépület",
                        "DocumentAiColumnValue": "Bp. XI. Budapart BOM Irodaépület, MORA-PR00111,",
                        "ErrorMessage": "Értékek nem egyeznek"
                    },
                    {
                        "EloColumnName": "WorkNumber",
                        "Status": 3,
                        "BcColumnValue": "MORA-PR00111",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": "WorkNumber mező hiányzik a dokumentumon"
                    },
                    {
                        "EloColumnName": "ContractorName",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "MORATUS Szerkezetépítő Kft.",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "SubContractorName",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "ANDIG-BAU Kft",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "ContractTotalAmount",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "219.248.429",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "ContractTotalCurrencyCode",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "extraAndMissedWorksAmount",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "256.819.832",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "extraAndMissedWorksCurrencyCode",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "currentGrossAmountCurrCode",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "currentNetAmount",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "10.644.076",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "currentNetAmountCurrCode",
                        "Status": 1,
                        "BcColumnValue": null,
                        "DocumentAiColumnValue": "HUF",
                        "ErrorMessage": null
                    },
                    {
                        "EloColumnName": "TIG BC-ből",
                        "Status": 1,
                        "BcColumnValue": "{\"CompanyCode\":\"MORA\",\"DocumentType\":\"Részteljesítés\",\"DocumentNo\":\"MORA-TIG25001173\",\"CompletionCertAmount\":\"0\",\"TotalAmountLCY\":\"10644076\",\"CompletionCertificateDate\":\"********\",\"PartialCompletionCertNo\":8,\"ProjectNo\":\"MORA-PR00111\",\"ProjectName\":\"Bp. XI. Budapart BOM Irodaépület\",\"ApprovalStatus\":\"Jóváhagyva\",\"ELOWorkflowStatus\":\"60 Elküldve\",\"VatType\":\"FAD\",\"CompletionCertLines\":{\"CompletionCertLineEntityData\":[{\"LineNo\":10000,\"ContractDocumentNo\":\"MORA-ASZ2402137\",\"ContractLineNo\":10000,\"ContractSubType\":\"Normál\",\"ActivityCode\":\"REZSIANYAG\",\"ActivityDescription\":\"Rezsianyagok\",\"Quantity\":\"0\",\"UnitofMeasure\":\"KPL\",\"UnitPrice\":\"0\",\"VATProdPostingGroup\":\"FORDSZOLG27\",\"CurrencyCode\":\"\",\"Amount\":\"0\",\"RemainingAmountLCY\":\"0\",\"RemainingAmount\":\"0\",\"GrossAmount\":\"0\",\"VAT\":\"0\",\"VATAmount\":\"0\"},{\"LineNo\":20000,\"ContractDocumentNo\":\"MORA-ASZ2402137\",\"ContractLineNo\":20000,\"ContractSubType\":\"Normál\",\"ActivityCode\":\"ZSALUBÉRLE\",\"ActivityDescription\":\"Zsalubérlet\",\"Quantity\":\"0\",\"UnitofMeasure\":\"KPL\",\"UnitPrice\":\"0\",\"VATProdPostingGroup\":\"FORDSZOLG27\",\"CurrencyCode\":\"\",\"Amount\":\"0\",\"RemainingAmountLCY\":\"0\",\"RemainingAmount\":\"0\",\"GrossAmount\":\"0\",\"VAT\":\"0\",\"VATAmount\":\"0\"},{\"LineNo\":30000,\"ContractDocumentNo\":\"MORA-ASZ2402137\",\"ContractLineNo\":30000,\"ContractSubType\":\"Normál\",\"ActivityCode\":\"ZSALUZÁSI\",\"ActivityDescription\":\"Zsaluzási munkák, munkahézag képzés, függőleges szerk. beton\",\"Quantity\":\"1\",\"UnitofMeasure\":\"KPL\",\"UnitPrice\":\"10644076\",\"VATProdPostingGroup\":\"FORDSZOLG27\",\"CurrencyCode\":\"\",\"Amount\":\"10644076\",\"RemainingAmountLCY\":\"40326566\",\"RemainingAmount\":\"40326566\",\"GrossAmount\":\"10644076\",\"VAT\":\"0\",\"VATAmount\":\"0\"},{\"LineNo\":40000,\"ContractDocumentNo\":\"MORA-ASZ2402137\",\"ContractLineNo\":40000,\"ContractSubType\":\"Normál\",\"ActivityCode\":\"VÍZSZINTES\",\"ActivityDescription\":\"Vízszintes szerk. betonozása\",\"Quantity\":\"0\",\"UnitofMeasure\":\"KPL\",\"UnitPrice\":\"0\",\"VATProdPostingGroup\":\"FORDSZOLG27\",\"CurrencyCode\":\"\",\"Amount\":\"0\",\"RemainingAmountLCY\":\"7888913\",\"RemainingAmount\":\"7888913\",\"GrossAmount\":\"0\",\"VAT\":\"0\",\"VATAmount\":\"0\"}],\"Text\":null},\"AdvanceLines\":{\"AdvanceLineEntityData\":null,\"Text\":null},\"Retentions\":{\"RetentionsEntityData\":[{\"RetentionType\":\"JÓTELJESÍTÉSI\",\"Percentage\":\"5\"}],\"Text\":null}}",
                        "DocumentAiColumnValue": null,
                        "ErrorMessage": "Only for debugging"
                    }
                    ]
                """,
            _ => """
                {
                    "documentType": "Other",
                    "validationStatus": "Success"
                }
                """,
        };
    }

    private static string GenerateFakeEloXml(string requestType)
    {
        return $"""
            <?xml version="1.0" encoding="UTF-8"?>
            <{requestType}>
                <timestamp>{DateTime.UtcNow:yyyy-MM-ddTHH:mm:ss.fffZ}</timestamp>
                <requestId>{Guid.NewGuid()}</requestId>
                <status>Success</status>
            </{requestType}>
            """;
    }
}
#endif

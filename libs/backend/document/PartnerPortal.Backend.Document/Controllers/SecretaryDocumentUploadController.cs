using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PartnerPortal.Backend.Document.DocumentApiStub.Controllers;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Services.SecretaryDocument;
using PartnerPortal.Backend.Shared.Common;

namespace PartnerPortal.Backend.Document.Controllers;

[Authorize(Policy = "RequireSecretaryRole")]
public class SecretaryDocumentUploadController : SecretaryDocumentApiController
{
    private readonly ISecretaryDocumentUploadService _documentUploadService;
    private readonly ISecretaryManualDocumentUploadService _secretaryManualDocumentUploadService;

    public SecretaryDocumentUploadController(
        ISecretaryDocumentUploadService documentUploadService,
        ISecretaryManualDocumentUploadService secretaryManualDocumentUploadService
    )
    {
        _documentUploadService = documentUploadService;
        _secretaryManualDocumentUploadService = secretaryManualDocumentUploadService;
    }

    public override async Task<IActionResult> CreateAndSubmitManualDocument(
        [FromBody] CreateSecretaryManualDocumentUploadDraftRequest createSecretaryManualDocumentUploadDraftRequest
    )
    {
        var userId = User.GetId();
        var documentUploadDto = await _secretaryManualDocumentUploadService.CreateAndSubmitDocument(
            userId,
            createSecretaryManualDocumentUploadDraftRequest
        );
        return Ok(documentUploadDto);
    }

    public override async Task<IActionResult> CreateAndSubmitSecretaryDocument(
        CreateSecretaryDocumentUploadDraftRequest request
    )
    {
        var userId = User.GetId();
        await _documentUploadService.CreateAndSubmitSecretaryDocument(userId, request);
        return Ok();
    }

    public override async Task<IActionResult> CreateManualDocumentUploadDraft(
        [FromBody] CreateSecretaryManualDocumentUploadDraftRequest createSecretaryManualDocumentUploadDraftRequest
    )
    {
        var userId = User.GetId();
        var documentUploadDto = await _secretaryManualDocumentUploadService.CreateDocumentUploadDraft(
            userId,
            createSecretaryManualDocumentUploadDraftRequest
        );
        return Ok(documentUploadDto);
    }

    public override async Task<IActionResult> CreateSecretaryDocumentUploadDraft(
        CreateSecretaryDocumentUploadDraftRequest request
    )
    {
        var userId = User.GetId();
        var documentUploadDto = await _documentUploadService.CreateSecretaryDocumentUploadDraft(userId, request);
        return Ok(documentUploadDto);
    }

    public override async Task<IActionResult> GetPartnerDocumentDetails(
        [FromRoute(Name = "documentUploadId"), Required] Guid documentUploadId
    )
    {
        var response = await _documentUploadService.GetPartnerDocumentDetails(documentUploadId);
        return Ok(response);
    }

    public override async Task<IActionResult> GetSecretaryDocumentDateOfArrival()
    {
        var response = await _documentUploadService.GetSecretaryDocumentDateOfArrival();
        return Ok(response);
    }

    public override async Task<IActionResult> GetSecretaryDocumentUpload(Guid documentUploadId)
    {
        var response = await _documentUploadService.GetSecretaryDocumentUpload(documentUploadId);
        return Ok(response);
    }

    public override async Task<IActionResult> GetSecretaryDocumentUploadTotalCounts(
        [FromQuery(Name = "projectNo")] string? projectNo
    )
    {
        var response = await _documentUploadService.GetSecretaryDocumentUploadTotalCounts(projectNo);
        return Ok(response);
    }

    public override async Task<IActionResult> GetSecretaryManualDocumentUpload(
        [FromRoute(Name = "documentUploadId"), Required] Guid documentUploadId
    )
    {
        var userId = User.GetId();
        var response = await _secretaryManualDocumentUploadService.GetDocumentUpload(userId, documentUploadId);
        return Ok(response);
    }

    public override async Task<IActionResult> ListPartnerDocuments(TableRequest tableRequest)
    {
        var response = await _documentUploadService.ListPartnerDocuments(tableRequest);
        return Ok(response);
    }

    public override async Task<IActionResult> ListSecretaryDocuments(TableRequest tableRequest)
    {
        var response = await _documentUploadService.ListSecretaryDocuments(tableRequest);
        return Ok(response);
    }

    public override async Task<IActionResult> RemoveSecretaryDocumentUpload(Guid documentUploadId)
    {
        await _documentUploadService.RemoveSecretaryDocumentUpload(documentUploadId);
        return NoContent();
    }

    public override async Task<IActionResult> SubmitManualDocumentUpload(
        [FromRoute(Name = "documentUploadId"), Required] Guid documentUploadId,
        [FromBody] UpdateSecretaryManualDocumentUploadDraftRequest updateSecretaryManualDocumentUploadDraftRequest
    )
    {
        var userId = User.GetId();
        var documentUploadDto = await _secretaryManualDocumentUploadService.SubmitDocumentUpload(
            userId,
            documentUploadId,
            updateSecretaryManualDocumentUploadDraftRequest
        );
        return Ok(documentUploadDto);
    }

    public override async Task<IActionResult> SubmitSecretaryDocumentUpload(
        Guid documentUploadId,
        UpdateSecretaryDocumentUploadDraftRequest request
    )
    {
        await _documentUploadService.SubmitSecretaryDocumentUpload(documentUploadId, request);
        return Ok();
    }

    public override async Task<IActionResult> UpdateManualDocumentUploadDraft(
        [FromRoute(Name = "documentUploadId"), Required] Guid documentUploadId,
        [FromBody] UpdateSecretaryManualDocumentUploadDraftRequest updateSecretaryManualDocumentUploadDraftRequest
    )
    {
        var userId = User.GetId();
        var documentUploadDto = await _secretaryManualDocumentUploadService.UpdateDocumentUploadDraft(
            userId,
            documentUploadId,
            updateSecretaryManualDocumentUploadDraftRequest
        );
        return Ok(documentUploadDto);
    }

    public override async Task<IActionResult> UpdateSecretaryDocumentUploadDraft(
        Guid documentUploadId,
        UpdateSecretaryDocumentUploadDraftRequest request
    )
    {
        var response = await _documentUploadService.UpdateSecretaryDocumentUploadDraft(documentUploadId, request);
        return Ok(response);
    }
}

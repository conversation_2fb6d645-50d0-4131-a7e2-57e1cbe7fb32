using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services;
using PartnerPortal.Backend.Document.Services.ArrivalDateCalculation;
using PartnerPortal.Backend.Document.Services.BcService;
using PartnerPortal.Backend.Document.Services.CalendarManagement;
using PartnerPortal.Backend.Document.Services.DocumentProcessing;
using PartnerPortal.Backend.Document.Services.DocumentProcessing.Validation;
using PartnerPortal.Backend.Document.Services.Elo;
using PartnerPortal.Backend.Document.Services.Extractors;
using PartnerPortal.Backend.Document.Services.NavService;
using PartnerPortal.Backend.Document.Services.PartnerDocument;
using PartnerPortal.Backend.Document.Services.SecretaryDocument;
using PartnerPortal.Backend.Document.Services.SpIdentifier;
using PartnerPortal.Backend.Shared.MarketBcApiClient;
using PartnerPortal.Backend.Shared.MarketEloApiClient;

namespace PartnerPortal.Backend.Document.Extensions;

public static class AppServiceExtension
{
    public static IServiceCollection AddDocumentAzureServices(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services.AddAutoMapper(typeof(AppServiceExtension).Assembly);
        services.AddScoped<IPartnerDocumentDataTableService, PartnerDocumentDataTableService>();
        services.AddScoped<ISecretaryDocumentDataTableService, SecretaryDocumentDataTableService>();
        services.AddScoped<IAzureApiService, AzureApiService>();
        services.AddScoped<IPartnerDocumentUploadService, PartnerDocumentUploadService>();
        services.AddScoped<ISecretaryDocumentUploadService, SecretaryDocumentUploadService>();
        services.AddScoped<ISecretaryManualDocumentUploadService, SecretaryManualDocumentUploadService>();
        services.AddScoped<ICalendarManagementService, CalendarManagementService>();
        services.AddScoped<IInScopeRetrieverService, InScopeRetrieverService>();
        services.AddScoped<IDocumentMergePlannerService, DocumentMergePlannerService>();
        services.AddScoped<ISpIdentifierService, SpIdentifierService>();
        services.AddScoped<IArrivalDateCalculationService, ArrivalDateCalculationService>();
        services.AddScoped<TiffAttachmentConvertService>();
        services.AddScoped<IBatchCoordinatorService, BatchCoordinatorService>();
        services.AddScoped<IProcessingManagerService, ProcessingManagerService>();
        services.AddScoped<IDocumentAnalysisService, DocumentAnalysisService>();
        services.AddScoped<IPerCaseClassificationService, PerCaseClassificationService>();
        services.AddScoped<IBatchClassificationService, BatchClassificationService>();
        services.AddScoped<IDocumentValidationService, DocumentValidationService>();
        services.AddScoped<IDocumentProcessingStatusService, DocumentProcessingStatusService>();
        services.AddScoped<ISystemUserAccessHelper, SystemUserAccessHelper>();
        services.Configure<MarketCustomersConfig>(
            configuration.GetSection("MarketCustomers")
                ?? throw new Exception("MarketCustomers section not found in configuration")
        );
        services.Configure<NavConfig>(
            configuration.GetSection("NavConfig") ?? throw new Exception("NavConfig section not found in configuration")
        );
        services.AddScoped<DocumentStatusReportService>();
        services.AddScoped<IBcService, BcService>();
        services.AddScoped<IDocumentValidatorFactory, DocumentValidatorFactory>();
        services.AddScoped<InvoiceValidator>();
        services.AddScoped<CompletionCertValidator>();
        services.AddMarketEloClient();
        services.AddScoped<IEloService, EloService>();
        services.AddScoped<IEloInvoicePreparationService, EloInvoicePreparationService>();
        services.AddScoped<IAnalyzeResultExtractor<CompletionCertificateAiData>, CompletionCertExtractor>();
        services.AddScoped<IAnalyzeResultExtractor<InvoiceAiData>, InvoiceExtractor>();
        services.AddScoped<INavService, NavService>();
        services.AddScoped<IPatternMatchingService, PatternMatchingService>();
        return services;
    }
}

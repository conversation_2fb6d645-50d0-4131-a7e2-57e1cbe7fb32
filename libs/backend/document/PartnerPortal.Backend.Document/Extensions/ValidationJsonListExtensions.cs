using PartnerPortal.Backend.Document.Models;

namespace PartnerPortal.Backend.Document.Extensions;

public static class ValidationJsonListExtensions
{
    /// <summary>
    /// Gets the AI-extracted value for a specific field name
    /// </summary>
    /// <param name="validationData">The validation data list</param>
    /// <param name="fieldName">The ELO column name to search for</param>
    /// <returns>The DocumentAiColumnValue or null if not found</returns>
    public static string? GetFieldAiValue(this List<ValidationJson> validationData, string fieldName)
    {
        return validationData.FirstOrDefault(x => x.EloColumnName == fieldName)?.DocumentAiColumnValue;
    }

    /// <summary>
    /// Gets the Business Central value for a specific field name
    /// </summary>
    /// <param name="validationData">The validation data list</param>
    /// <param name="fieldName">The ELO column name to search for</param>
    /// <returns>The BcColumnValue or null if not found</returns>
    public static string? GetFieldBcValue(this List<ValidationJson> validationData, string fieldName)
    {
        return validationData.FirstOrDefault(x => x.EloColumnName == fieldName)?.BcColumnValue;
    }

    /// <summary>
    /// Gets the NAV value for a specific field name
    /// </summary>
    /// <param name="validationData">The validation data list</param>
    /// <param name="fieldName">The ELO column name to search for</param>
    /// <returns>The NavColumnValue or null if not found</returns>
    public static string? GetFieldNavValue(this List<ValidationJson> validationData, string fieldName)
    {
        return validationData.FirstOrDefault(x => x.EloColumnName == fieldName)?.NavColumnValue;
    }

    /// <summary>
    /// Gets the value-only data for a specific field name
    /// </summary>
    /// <param name="validationData">The validation data list</param>
    /// <param name="fieldName">The ELO column name to search for</param>
    /// <returns>The ValueOnly or null if not found</returns>
    public static string? GetFieldValueOnly(this List<ValidationJson> validationData, string fieldName)
    {
        return validationData.FirstOrDefault(x => x.EloColumnName == fieldName)?.ValueOnly;
    }

    /// <summary>
    /// Gets the validation status for a specific field name
    /// </summary>
    /// <param name="validationData">The validation data list</param>
    /// <param name="fieldName">The ELO column name to search for</param>
    /// <returns>The Status as string or null if not found</returns>
    public static string? GetFieldStatus(this List<ValidationJson> validationData, string fieldName)
    {
        return validationData.FirstOrDefault(x => x.EloColumnName == fieldName)?.Status.ToString();
    }

    /// <summary>
    /// Gets the complete ValidationJson object for a specific field name
    /// </summary>
    /// <param name="validationData">The validation data list</param>
    /// <param name="fieldName">The ELO column name to search for</param>
    /// <returns>The ValidationJson object or null if not found</returns>
    public static ValidationJson? GetValidationField(this List<ValidationJson> validationData, string fieldName)
    {
        return validationData.FirstOrDefault(x => x.EloColumnName == fieldName);
    }

    public static string GetFieldOneOfValues(this List<ValidationJson> validationData, string fieldName)
    {
        var field = validationData.FirstOrDefault(x => x.EloColumnName == fieldName);
        if (field != null)
        {
            // Prefer BC value if it exists
            if (!string.IsNullOrEmpty(field.BcColumnValue))
            {
                return field.BcColumnValue;
            }
            // Otherwise use AI extracted value
            if (!string.IsNullOrEmpty(field.DocumentAiColumnValue))
            {
                return field.DocumentAiColumnValue;
            }
            // Otherwise use NAV value
            if (!string.IsNullOrEmpty(field.NavColumnValue))
            {
                return field.NavColumnValue;
            }

            // Otherwise use ValueOnly
            if (!string.IsNullOrEmpty(field.ValueOnly))
            {
                return field.ValueOnly;
            }
        }
        return string.Empty;
    }
}

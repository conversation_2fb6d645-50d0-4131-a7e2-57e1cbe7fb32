using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Document.Services.SpIdentifier;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;

namespace PartnerPortal.Backend.Document.Extensions;

public static class DocumentUploadExtensions
{
    public static async Task UpdateManualDocumentAttachments(
        this DocumentUpload documentUpload,
        ISpIdentifierService spIdentifierService,
        List<AttachmentDocumentTypeDto> requestAttachments
    )
    {
        var existingAttachments = documentUpload.AttachmentDocumentTypes.ToDictionary(a => a.AttachmentId);
        var requestedAttachments = requestAttachments.ToDictionary(a => a.Attachment.FileId);

        var attachmentsToRemove = documentUpload
            .AttachmentDocumentTypes.Where(a => !requestedAttachments.ContainsKey(a.AttachmentId))
            .ToList();

        foreach (var attachment in attachmentsToRemove)
        {
            documentUpload.AttachmentDocumentTypes.Remove(attachment);
        }

        foreach (var requestAttachment in requestAttachments)
        {
            var attachmentId = requestAttachment.Attachment.FileId;

            if (existingAttachments.TryGetValue(attachmentId, out var existingAttachment))
            {
                existingAttachment.DocumentType = requestAttachment.DocumentType;
            }
            else
            {
                var newAttachment = new AttachmentDocumentType
                {
                    DocumentUploadId = documentUpload.Id,
                    AttachmentId = attachmentId,
                    DocumentType = requestAttachment.DocumentType,
                };

                if (newAttachment.IsInvoiceType)
                {
                    newAttachment.SpIdentifier = await spIdentifierService.GetNewSpIdentifierAsync();
                }

                documentUpload.AttachmentDocumentTypes.Add(newAttachment);
            }
        }
    }

    public static async Task AddAttachmentDocumentTypes(
        this DocumentUpload documentUpload,
        ISpIdentifierService spIdentifierService,
        List<AttachmentDocumentTypeDto> attachmentTypes
    )
    {
        foreach (var x in attachmentTypes)
        {
            var newAttachment = new AttachmentDocumentType
            {
                DocumentUploadId = documentUpload.Id,
                AttachmentId = x.Attachment.FileId,
                DocumentType = x.DocumentType,
            };

            if (newAttachment.IsInvoiceType)
            {
                newAttachment.SpIdentifier = await spIdentifierService.GetNewSpIdentifierAsync();
            }

            documentUpload.AttachmentDocumentTypes.Add(newAttachment);
        }
    }
}

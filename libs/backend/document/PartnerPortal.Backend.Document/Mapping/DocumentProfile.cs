using AutoMapper;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;
using PartnerPortal.Backend.Shared.PartnerPortalDatabase.Entities.Document;
using ContractsFileInfo = PartnerPortal.Backend.Contracts.ContractsApiStub.Models.FileInfo;
using DocumentFileInfo = PartnerPortal.Backend.Document.DocumentApiStub.Models.FileInfo;

namespace PartnerPortal.Backend.Document.Mapping;

public class DocumentProfile : Profile
{
    public DocumentProfile()
    {
        CreateMap<ContractsFileInfo, DocumentFileInfo>()
            .ForMember(dest => dest.FileId, opt => opt.MapFrom(src => src.FileId))
            .ForMember(dest => dest.FileName, opt => opt.MapFrom(src => src.FileName))
            .ForMember(dest => dest.MimeType, opt => opt.MapFrom(src => src.MimeType));

        CreateMap<PartnerDocumentUpload, DocumentUploadDto>()
            .ForMember(dest => dest.ProcessingStatus, opt => opt.Ignore());

        CreateMap<SecretaryDocumentUpload, SecretaryDocumentUploadDto>()
            .ForMember(dest => dest.ProfileName, opt => opt.MapFrom(src => src.User.ProfileName));

        CreateMap<SecretaryDocumentUpload, SecretaryDocumentListDto>()
            .ForMember(dest => dest.ContactName, opt => opt.MapFrom(src => src.ContactName ?? string.Empty))
            .ForMember(dest => dest.Uploader, opt => opt.MapFrom(src => src.User.ProfileName))
            .ForMember(
                dest => dest.DateOfArrival,
                opt => opt.MapFrom(src => src.SecretaryDateOfArrival ?? src.DateOfArrival)
            )
            .ForMember(dest => dest.SpIdentifiers, opt => opt.Ignore())
            .ForMember(dest => dest.ProcessingStatus, opt => opt.Ignore());

        CreateMap<SecretaryDocumentUpload, SecretaryManualDocumentUploadDto>()
            .ForMember(dest => dest.ProfileName, opt => opt.MapFrom(src => src.User.ProfileName));

        CreateMap<AttachmentDocumentType, AttachmentDocumentTypeDto>()
            .ForMember(
                dest => dest.Attachment,
                opt =>
                    opt.MapFrom(src => new DocumentFileInfo
                    {
                        FileId = src.AttachmentId,
                        FileName = src.Attachment.UploadName,
                        MimeType = src.Attachment.MimeType,
                    })
            );

        CreateMap<AttachmentDocumentType, SecretaryAttachmentDocumentTypeDto>()
            .ForMember(
                dest => dest.Attachment,
                opt =>
                    opt.MapFrom(src => new DocumentFileInfo
                    {
                        FileId = src.AttachmentId,
                        FileName = src.Attachment.UploadName,
                        MimeType = src.Attachment.MimeType,
                    })
            );

        CreateMap<AttachmentDocumentType, SecretaryManualAttachmentDocumentTypeDto>()
            .ForMember(
                dest => dest.Attachment,
                opt =>
                    opt.MapFrom(src => new DocumentFileInfo
                    {
                        FileId = src.AttachmentId,
                        FileName = src.Attachment.UploadName,
                        MimeType = src.Attachment.MimeType,
                    })
            );

        CreateMap<PartnerDocumentUpload, SecretaryPartnerDocumentDetailDto>()
            .ForMember(
                dest => dest.Partner,
                opt => opt.MapFrom(src => src.User.ProfileName + " " + src.User.VatRegistrationNo)
            )
            .ForMember(dest => dest.PartnerContactNo, opt => opt.MapFrom(src => src.User.BcUserId));

        CreateMap<AttachmentDocumentType, AttachmentDocumentTypeDto>()
            .ForMember(
                dest => dest.Attachment,
                opt =>
                    opt.MapFrom(src => new DocumentFileInfo
                    {
                        FileId = src.AttachmentId,
                        FileName = src.Attachment.UploadName,
                        MimeType = src.Attachment.MimeType,
                    })
            );

        CreateMap<PartnerDocumentUpload, SecretaryPartnerDocumentListDto>()
            .ForMember(
                dest => dest.SpIdentifiers,
                opt =>
                    opt.MapFrom(src =>
                        src.AttachmentDocumentTypes.Select(x => x.SpIdentifier)
                            .Where(s => !string.IsNullOrEmpty(s))
                            .Distinct()
                    )
            )
            .ForMember(
                dest => dest.Partner,
                opt => opt.MapFrom(src => $"{src.User.ProfileName} ({src.User.VatRegistrationNo})")
            )
            .ForMember(dest => dest.PartnerContactNo, opt => opt.MapFrom(src => src.User.BcUserId))
            .ForMember(dest => dest.ProcessingStatus, opt => opt.Ignore());
    }
}

﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="Extensions/" />
    <Folder Include="Controllers/" />
    <Folder Include="Services/" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.AI.DocumentIntelligence" />
    <PackageReference Include="EFCore.BulkExtensions" />
    <PackageReference Include="Magick.NET-Q8-AnyCPU" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.RateLimiting " />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="Quickenshtein" />
    <PackageReference Include="PdfPig" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\shared\Attachment\PartnerPortal.Backend.Shared.Attachment\PartnerPortal.Backend.Shared.Attachment.csproj" />
    <ProjectReference Include="..\..\shared\Common\PartnerPortal.Backend.Shared.Common.csproj" />
    <ProjectReference Include="..\DocumentApiStub\src\src\PartnerPortal.Backend.Document.DocumentApiStub\PartnerPortal.Backend.Document.DocumentApiStub.csproj" />
    <ProjectReference Include="..\..\shared\MarketBcApiClient\PartnerPortal.Backend.Shared.MarketBcApiClient.csproj" />
    <ProjectReference Include="..\..\shared\PartnerPortal.Backend.Shared.MarketEloApiClient\PartnerPortal.Backend.Shared.MarketEloApiClient.csproj" />
    <ProjectReference Include="..\..\..\nav\Nav.Invoice.Client\Nav.Invoice.Client.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TestResources\sample-invoice-response.json" />
    <None Include="TestResources\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>
</Project>

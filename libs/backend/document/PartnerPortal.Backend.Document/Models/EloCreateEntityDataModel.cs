using PartnerPortal.Backend.Shared.MarketEloApiClient;

namespace PartnerPortal.Backend.Document.Models;

public class EloCreateEntityDataModel
{
    // Primary invoice data
    public required string CompanyId { get; set; }
    public required string RelatedProjectId { get; set; }
    public bool IsRelatedProjectIdMatchingBc { get; set; } = false;
    public required string BankAccountId { get; set; }
    public required string BankAccountNo { get; set; }
    public required string PartnerId { get; set; }
    public required string InvoiceId { get; set; }
    public required string InvoiceDate { get; set; }
    public required string FulfilmentDate { get; set; }
    public required string DueDate { get; set; }
    public required string ArrivalDate { get; set; }
    public required string Currency { get; set; }
    public required string GrossAmount { get; set; }
    public required string ServiceStartDate { get; set; }
    public required string ServiceEndDate { get; set; }
    public required string IsAdvanceInvoice { get; set; }
    public required string SpIdentifier { get; set; }
    public required string Comment { get; set; }
    public required string ExtId { get; set; }
    public required bool IsFoundInNav { get; set; }
    public required string VendorTaxNumber { get; set; }
    public required string Source { get; set; }

    // Related data
    public List<EloCompletionCertData> RelatedCompletionCerts { get; set; } = [];
    public entityDataField[] ValidationFields { get; set; } = [];
    public entityDataField[] CompCertValidationFields { get; set; } = [];
}

using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Document.Models;

public class ValidationJson
{
    public string EloColumnName { get; set; } = string.Empty;
    public ColumnStatus Status { get; set; } = ColumnStatus.Ok;
    public string? BcColumnValue { get; set; }
    public string? DocumentAiColumnValue { get; set; }
    public string? NavColumnValue { get; set; }
    public string? ValueOnly { get; set; }
}

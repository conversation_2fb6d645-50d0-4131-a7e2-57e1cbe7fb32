using System.Text.RegularExpressions;

namespace PartnerPortal.Backend.Document.Models;

public record PatternGroup(string Name, List<PatternRule> Patterns);

public record PatternRule(
    string Name,
    string Pattern,
    RegexOptions Options = RegexOptions.IgnoreCase | RegexOptions.Compiled,
    bool RequireContextExtraction = true,
    ContextExtractionType ContextType = ContextExtractionType.MatchedText,
    int? ContextWordCount = null
);

public enum ContextExtractionType
{
    None,
    MatchedText,
    ContainingSentence,
    SurroundingWords,
}

public record PatternMatchResult(string GroupName, List<PatternMatch> Matches, int TotalMatchCount);

public record PatternMatch(
    string PatternName,
    string MatchedText,
    int Position,
    string? ExtractedContext = null,
    ContextExtractionType ContextType = ContextExtractionType.None
);

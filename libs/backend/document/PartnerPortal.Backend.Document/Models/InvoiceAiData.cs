using PartnerPortal.Backend.Document.Utils;

namespace PartnerPortal.Backend.Document.Models;

/// <summary>
/// Represents the extracted data from a invoice document
/// </summary>
public class InvoiceAiData
{
    public string? CustomerName { get; set; }
    public (VatNumberType Type, string CleanedTaxNumber)? CustomerTaxNumber { get; set; }
    public CustomerAddress? CustomerAddress { get; set; }
    public (VatNumberType Type, string CleanedTaxNumber)? VendorTaxId { get; set; }
    public string? TaxRate { get; set; }
    public string? Currency { get; set; }
    public string? PaymentTerm { get; set; }
    public string? InvoiceId { get; set; }
    public (decimal? amount, string? currency) InvoiceTotal { get; set; }
    public (decimal? amount, string? currency) SubTotal { get; set; }
    public (decimal? amount, string? currency) TotalTax { get; set; }
    public List<string>? BankAccounts { get; set; }
    public List<string>? BankAccountVariations { get; set; }
    public DateTime? InvoiceDate { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? FulfilmentDate { get; set; }
    public DateTime? ServiceStartDate { get; set; }
    public DateTime? ServiceEndDate { get; set; }
    public bool IsCollectionInvoice { get; set; }
    public List<PatternMatchResult>? PatternMatches { get; set; }

    /// <summary>
    /// Confidence scores for each extracted field (0.0 to 1.0)
    /// </summary>
    public Dictionary<string, double> FieldConfidences { get; set; } = [];
}

public class CustomerAddress
{
    public string? Full { get; set; }
    public string? PostalCode { get; set; }
    public string? City { get; set; }
    public string? StreetAddress { get; set; }
    public string? Road { get; set; }
    public string? HouseNumber { get; set; }
    public string? FieldContent { get; set; }
}

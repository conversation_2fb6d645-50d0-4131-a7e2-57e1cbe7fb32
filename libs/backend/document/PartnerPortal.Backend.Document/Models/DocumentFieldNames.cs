namespace PartnerPortal.Backend.Document.Models;

/// <summary>
/// Contains constants for ELO document field names
/// these fields are used to extract data from the invoice document and to create the ELO document
/// It does not directly comes from the invoice document, but it is calculated from the invoice document
/// </summary>
public static class ELOFieldNames
{
    public const string CompanyId = "CompanyId";
    public const string PartnerId = "PartnerId";
    public const string InvoiceTotalCurrency = "InvoiceTotalCurrency";
    public const string InvoiceTotalAmount = "InvoiceTotalAmount";
    public const string BankAccountNo = "BankAccountNo";
    public const string BankAccountId = "BankAccountId";
    public const string PostalCode = "PostalCode";
    public const string City = "City";
    public const string StreetAddress = "StreetAddress";

    public const string ServiceStartDate = "ServiceStartDate";
    public const string ServiceEndDate = "ServiceEndDate";
    public const string CustomerTaxId = "CustomerTaxId";
    public const string CustomerName = "CustomerName";
    public const string VendorTaxId = "VendorTaxId";
    public const string PaymentTerm = "PaymentTerm";
    public const string InvoiceId = "InvoiceId";
    public const string DueDate = "DueDate";
    public const string TaxRate = "TaxRate";
    public const string InvoiceDate = "InvoiceDate";
    public const string Currency = "Currency";
    public const string FulfilmentDate = "FulfilmentDate";

    public const string InvoiceGrossAmountToCompletionCertGrossAmount = "InvoiceGrossAmountToCompletionCertGrossAmount";
    public const string InvoiceFulfilmentDateToCompletionCertExecutionDate =
        "InvoiceFulfilmentDateToCompletionCertExecutionDate";
    public const string CustomerNotFoundInCustomerCodes = "CustomerNotFoundInCustomerCodes";
    public const string FulfilmentDateAfterIssueDate = "FulfilmentDateAfterIssueDate";
    public const string CalculatedPerformanceDateMatch = "CalculatedPerformanceDateMatch";
    public const string FoundInNav = "FoundInNav";
    public const string NotWireTransferWarning = "NotWireTransferWarning";
    public const string CollectionInvoice = "CollectionInvoice";
    public const string TigInvoiceVatRateAgainstContractNotMatch = "TigInvoiceVatRateAgainstContractNotMatch";
    public const string LegalInfo = "LegalInfo";
}

/// <summary>
/// Contains constants for completion certificate document field names used in document validation
/// </summary>
public static class CompletionCertAiFieldNames
{
    public const string ExecutionDate = "ExecutionDate";
    public const string TigId = "TigId";
    public const string TigStatus = "TigStatus";
    public const string ProjectName = "ProjectName";
    public const string TigUniqueId = "TigUniqueId";
    public const string ContractorName = "ContractorName";
    public const string SubContractorName = "SubContractorName";
    public const string WorkNumber = "WorkNumber";
    public const string ContractTotalAmount = "ContractTotalAmount";
    public const string ContractTotalCurrencyCode = "ContractTotalCurrencyCode";
    public const string ExtraAndMissedWorksAmount = "extraAndMissedWorksAmount";
    public const string ExtraAndMissedWorksCurrencyCode = "extraAndMissedWorksCurrencyCode";
    public const string CurrentGrossAmount = "currentGrossAmount";
    public const string CurrentGrossAmountCurrCode = "currentGrossAmountCurrCode";
    public const string CurrentNetAmount = "currentNetAmount";
    public const string CurrentNetAmountCurrCode = "currentNetAmountCurrCode";
}

namespace PartnerPortal.Backend.Document.Models;

public sealed record NavInvoiceData
{
    public DateTime? FulfillmentDate { get; set; }
    public DateTime? InvoiceDate { get; set; }
    public string? PaymentTerm { get; set; }
    public string? CurrencyCode { get; set; }
    public required string InvoiceId { get; set; }
    public decimal? InvoiceTotal { get; set; }
    public DateTime? ServiceStartDate { get; set; }
    public DateTime? ServiceEndDate { get; set; }
}

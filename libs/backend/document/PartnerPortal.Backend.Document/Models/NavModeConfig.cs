using Nav.Invoice.Client.Models;

namespace PartnerPortal.Backend.Document.Models;

/// <summary>
/// NAV mode configuration
/// </summary>
public class NavConfig
{
    public NavMode NavMode { get; set; }
    public required SoftwareType SoftwareType { get; set; }
}

/// <summary>
/// NAV mode configuration enum
/// </summary>
public enum NavMode
{
    Test,
    Production,
}

/// <summary>
/// Software type configuration
/// </summary>
public class SoftwareType
{
    public string SoftwareId { get; set; } = string.Empty;
    public string SoftwareName { get; set; } = string.Empty;
    public SoftwareOperationType SoftwareOperation { get; set; } = SoftwareOperationType.LocalSoftware;
    public string SoftwareMainVersion { get; set; } = string.Empty;
    public string SoftwareDevName { get; set; } = string.Empty;
    public string SoftwareDevContact { get; set; } = string.Empty;
}

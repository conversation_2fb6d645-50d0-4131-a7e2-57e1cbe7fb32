.openapi-generator-ignore
PartnerPortal.Backend.Document.DocumentApiStub.sln
README.md
build.bat
build.sh
src/PartnerPortal.Backend.Document.DocumentApiStub/.gitignore
src/PartnerPortal.Backend.Document.DocumentApiStub/Attributes/ValidateModelStateAttribute.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Authentication/ApiAuthentication.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Controllers/AttachmentApi.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Controllers/CalendarManagementApi.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Controllers/DocumentReportApi.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Controllers/PartnerDocumentApi.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Controllers/PartnersApi.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Controllers/SecretaryDocumentApi.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Converters/CustomEnumConverter.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Formatters/InputFormatterStream.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/AccountTypeEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/AttachmentDocumentTypeDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ColumnFilter.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ColumnStatus.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/CommitFilesRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/Contact.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ContactBank.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/CreateDocumentUploadDraftRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/CreateSecretaryDocumentUploadDraftRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/CreateSecretaryManualDocumentUploadDraftRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentAnalysisDetailDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentAnalysisStatusEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentClassificationDetailDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentClassificationStatusEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentEloDetailDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentEloStatusEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentStatusReportDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentType.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentUploadDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentUploadQueryRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentValidationDetailDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DocumentValidationStatusEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/DownloadBasedOnSpRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/EloProcessingStatusEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/FileInfo.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/GetAttachmentConfigsResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/GetDocumentStatusReports200Response.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/GetDocumentUploadTotalCountsResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/GetDocumentsResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/GetPaginatedDocumentsResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/GetPartnersResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/GetSecretaryDocumentUploadTotalCountsResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ListFilesResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ListPartnerDocuments200Response.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ListSecretaryDocuments200Response.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/Partner.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/PostUploadFileResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/Problem.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ProcessingStatusEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryAttachmentDocumentTypeDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryClassificationDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryDateOfArrivalDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryDocumentListDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryDocumentUploadDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryDocumentUploadQueryRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryManualAttachmentDocumentTypeDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryManualDocumentUploadDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryPartnerDocumentDetailDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryPartnerDocumentListDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SecretaryUploadTypeEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SimpleDocumentStatusReportDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SortByEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SortField.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/SortOrderEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/TableRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/TableResponse.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/UnusualWorkdayDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/UpdateDocumentUploadDraftRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/UpdateSecretaryDocumentUploadDraftRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/UpdateSecretaryManualDocumentUploadDraftRequest.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/UploadStatusEnum.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/ValidationJsonDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/Models/YearlyCalendarConfigDto.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/OpenApi/TypeExtensions.cs
src/PartnerPortal.Backend.Document.DocumentApiStub/PartnerPortal.Backend.Document.DocumentApiStub.csproj
src/PartnerPortal.Backend.Document.DocumentApiStub/PartnerPortal.Backend.Document.DocumentApiStub.nuspec

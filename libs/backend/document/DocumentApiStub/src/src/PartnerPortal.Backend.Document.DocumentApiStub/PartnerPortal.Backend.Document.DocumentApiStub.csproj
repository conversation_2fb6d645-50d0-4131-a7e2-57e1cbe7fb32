<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Description>A library generated from a OpenAPI doc</Description>
    <Copyright>No Copyright</Copyright>
    <Authors>OpenAPI</Authors>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <Version>1.0.0</Version>
    <OutputType>Library</OutputType>
    <AssemblyName>PartnerPortal.Backend.Document.DocumentApiStub</AssemblyName>
    <PackageId>PartnerPortal.Backend.Document.DocumentApiStub</PackageId>
    <UserSecretsId>7af33179-278e-407c-8fba-4b42f9ed033c</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="JsonSubTypes" />
  </ItemGroup>
  <ItemGroup>
    <!--<DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.0" />-->
  </ItemGroup>
</Project>

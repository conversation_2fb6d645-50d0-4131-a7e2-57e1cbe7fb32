/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON>/felület irány<PERSON>ér<PERSON>, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PartnerPortal.Backend.Document.DocumentApiStub.Attributes;
using PartnerPortal.Backend.Document.DocumentApiStub.Models;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Controllers
{
    /// <summary>
    ///
    /// </summary>
    [ApiController]
    public abstract class SecretaryDocumentApiController : ControllerBase
    {
        /// <summary>
        /// Create and submit manual document in a single operation
        /// </summary>
        /// <remarks>Creates a new document upload and immediately submits it in a single operation. This combines the functionality of creating a draft and submitting it. </remarks>
        /// <param name="createSecretaryManualDocumentUploadDraftRequest">Document details for creation and submission</param>
        /// <response code="200">Document created and submitted successfully</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/manual-document/create-and-submit")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryManualDocumentUploadDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> CreateAndSubmitManualDocument(
            [FromBody] CreateSecretaryManualDocumentUploadDraftRequest createSecretaryManualDocumentUploadDraftRequest
        );

        /// <summary>
        /// Create and submit document in a single operation for secretary
        /// </summary>
        /// <remarks>Creates a new document upload and immediately submits it in a single operation for secretary user. This combines the functionality of creating a draft and submitting it. </remarks>
        /// <param name="createSecretaryDocumentUploadDraftRequest">Document details for creation and submission</param>
        /// <response code="200">Document created and submitted successfully</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/document/create-and-submit")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> CreateAndSubmitSecretaryDocument(
            [FromBody] CreateSecretaryDocumentUploadDraftRequest createSecretaryDocumentUploadDraftRequest
        );

        /// <summary>
        /// Create new manual document upload draft
        /// </summary>
        /// <remarks>Creates an document upload draft record. The draft will associate the uncommitted attachments (uploaded earlier) using the provided document upload ID. </remarks>
        /// <param name="createSecretaryManualDocumentUploadDraftRequest">Document upload draft details. The client supplies the ID.</param>
        /// <response code="200">Document upload draft created successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/manual-document/draft")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryManualDocumentUploadDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> CreateManualDocumentUploadDraft(
            [FromBody] CreateSecretaryManualDocumentUploadDraftRequest createSecretaryManualDocumentUploadDraftRequest
        );

        /// <summary>
        /// Create new document upload draft for secretary
        /// </summary>
        /// <remarks>Creates an document upload draft record for secretary user. </remarks>
        /// <param name="createSecretaryDocumentUploadDraftRequest">Document upload draft details. The client supplies the ID.</param>
        /// <response code="204">Document upload draft created successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/document/draft")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> CreateSecretaryDocumentUploadDraft(
            [FromBody] CreateSecretaryDocumentUploadDraftRequest createSecretaryDocumentUploadDraftRequest
        );

        /// <summary>
        /// Get partner document details
        /// </summary>
        /// <remarks>Retrieves the details of a specific partner document.</remarks>
        /// <param name="documentUploadId">The document upload ID.</param>
        /// <response code="200">Successful response</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpGet]
        [Route("/contracts/documents/secretary/partner-documents/{documentUploadId}")]
        [Authorize]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryPartnerDocumentDetailDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> GetPartnerDocumentDetails(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId
        );

        /// <summary>
        /// Get date of arrival for secretary
        /// </summary>
        /// <remarks>Retrieves the date of arrival for secretary user.</remarks>
        /// <response code="200">Date of arrival retrieved successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpGet]
        [Route("/contracts/documents/secretary/document/date-of-arrival")]
        [Authorize]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryDateOfArrivalDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> GetSecretaryDocumentDateOfArrival();

        /// <summary>
        /// Retrieve document upload details for secretary
        /// </summary>
        /// <param name="documentUploadId">The document upload ID.</param>
        /// <response code="200">Document upload details retrieved successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpGet]
        [Route("/contracts/documents/secretary/document/{documentUploadId}")]
        [Authorize]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryDocumentUploadDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> GetSecretaryDocumentUpload(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId
        );

        /// <summary>
        /// Get document upload total counts for secretary
        /// </summary>
        /// <remarks>Retrieves the counts of document uploads for Draft, Submitted and Total for secretary user.</remarks>
        /// <param name="projectNo">Optional filter to retrieve counts for a specific project.</param>
        /// <response code="200">List of document uploads retrieved successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/document/counts")]
        [Authorize]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(GetDocumentUploadTotalCountsResponse))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> GetSecretaryDocumentUploadTotalCounts(
            [FromQuery(Name = "projectNo")] string projectNo
        );

        /// <summary>
        /// Retrieve document upload details
        /// </summary>
        /// <param name="documentUploadId">The document upload ID.</param>
        /// <response code="200">Document upload details retrieved successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpGet]
        [Route("/contracts/documents/secretary/manual-document/{documentUploadId}")]
        [Authorize]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryManualDocumentUploadDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> GetSecretaryManualDocumentUpload(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId
        );

        /// <summary>
        /// List partner documents
        /// </summary>
        /// <remarks>Retrieves a paginated list of partner documents with optional filtering and sorting.</remarks>
        /// <param name="tableRequest"></param>
        /// <response code="200">Successful response</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/partner-documents")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(ListPartnerDocuments200Response))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> ListPartnerDocuments([FromBody] TableRequest tableRequest);

        /// <summary>
        /// List secretary documents
        /// </summary>
        /// <remarks>Retrieves a paginated list of secretary documents with optional filtering and sorting.</remarks>
        /// <param name="tableRequest"></param>
        /// <response code="200">Successful response</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/document")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(ListSecretaryDocuments200Response))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> ListSecretaryDocuments([FromBody] TableRequest tableRequest);

        /// <summary>
        /// Remove document upload for secretary
        /// </summary>
        /// <param name="documentUploadId">The document upload ID.</param>
        /// <response code="204">Document upload removed successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpDelete]
        [Route("/contracts/documents/secretary/document/{documentUploadId}")]
        [Authorize]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> RemoveSecretaryDocumentUpload(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId
        );

        /// <summary>
        /// Submit manual document upload
        /// </summary>
        /// <remarks>Submits the document upload, performing necessary validations (e.g. for a TIG document at least two attachments with one of each required document types) and commits the associated attachments. </remarks>
        /// <param name="documentUploadId">The ID of the document upload to be submitted.</param>
        /// <param name="updateSecretaryManualDocumentUploadDraftRequest">Document details for creation and submission</param>
        /// <response code="200">Document upload submitted successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/manual-document/submit/{documentUploadId}")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryManualDocumentUploadDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> SubmitManualDocumentUpload(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId,
            [FromBody] UpdateSecretaryManualDocumentUploadDraftRequest updateSecretaryManualDocumentUploadDraftRequest
        );

        /// <summary>
        /// Submit document upload for secretary
        /// </summary>
        /// <remarks>Submits the document upload and commits the associated attachments. For secretary user. </remarks>
        /// <param name="documentUploadId">The ID of the document upload to be submitted.</param>
        /// <param name="updateSecretaryDocumentUploadDraftRequest">Document details for creation and submission</param>
        /// <response code="200">Document upload submitted successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPost]
        [Route("/contracts/documents/secretary/document/submit/{documentUploadId}")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> SubmitSecretaryDocumentUpload(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId,
            [FromBody] UpdateSecretaryDocumentUploadDraftRequest updateSecretaryDocumentUploadDraftRequest
        );

        /// <summary>
        /// Update manual document upload draft
        /// </summary>
        /// <remarks>Update the details of an existing document upload draft.</remarks>
        /// <param name="documentUploadId">The document upload draft ID.</param>
        /// <param name="updateSecretaryManualDocumentUploadDraftRequest">Updated draft details.</param>
        /// <response code="200">Document upload draft updated successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPut]
        [Route("/contracts/documents/secretary/manual-document/draft/{documentUploadId}")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(SecretaryManualDocumentUploadDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> UpdateManualDocumentUploadDraft(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId,
            [FromBody] UpdateSecretaryManualDocumentUploadDraftRequest updateSecretaryManualDocumentUploadDraftRequest
        );

        /// <summary>
        /// Update document upload draft for secretary
        /// </summary>
        /// <remarks>Update the details of an existing document upload draft for secretary user.</remarks>
        /// <param name="documentUploadId">The document upload draft ID.</param>
        /// <param name="updateSecretaryDocumentUploadDraftRequest">Updated draft details.</param>
        /// <response code="200">Document upload draft updated successfully.</response>
        /// <response code="0">error occurred - see status code and problem object for more information.</response>
        [HttpPut]
        [Route("/contracts/documents/secretary/document/draft/{documentUploadId}")]
        [Authorize]
        [Consumes("application/json")]
        [ValidateModelState]
        [ProducesResponseType(statusCode: 200, type: typeof(DocumentUploadDto))]
        [ProducesResponseType(statusCode: 0, type: typeof(Problem))]
        public abstract Task<IActionResult> UpdateSecretaryDocumentUploadDraft(
            [FromRoute(Name = "documentUploadId")] [Required] Guid documentUploadId,
            [FromBody] UpdateSecretaryDocumentUploadDraftRequest updateSecretaryDocumentUploadDraftRequest
        );
    }
}

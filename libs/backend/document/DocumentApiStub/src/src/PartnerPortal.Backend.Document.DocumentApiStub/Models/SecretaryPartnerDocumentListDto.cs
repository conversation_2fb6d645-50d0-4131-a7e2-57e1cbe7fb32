/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON> f<PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class SecretaryPartnerDocumentListDto : IEquatable<SecretaryPartnerDocumentListDto>
    {
        /// <summary>
        /// Document upload identifier (generated on the frontend).
        /// </summary>
        /// <value>Document upload identifier (generated on the frontend).</value>
        [DataMember(Name = "id", EmitDefaultValue = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// S/P azonosítók.
        /// </summary>
        /// <value>S/P azonosítók.</value>
        [DataMember(Name = "spIdentifiers", EmitDefaultValue = true)]
        public List<string> SpIdentifiers { get; set; }

        /// <summary>
        /// Computed - ProfileName + VatNumber.
        /// </summary>
        /// <value>Computed - ProfileName + VatNumber.</value>
        [DataMember(Name = "partner", EmitDefaultValue = true)]
        public string Partner { get; set; }

        /// <summary>
        /// Partner contact number.
        /// </summary>
        /// <value>Partner contact number.</value>
        [DataMember(Name = "partnerContactNo", EmitDefaultValue = true)]
        public string PartnerContactNo { get; set; }

        /// <summary>
        /// The date and time when the document upload was created.
        /// </summary>
        /// <value>The date and time when the document upload was created.</value>
        [DataMember(Name = "createdDate", EmitDefaultValue = true)]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// The date of arrival of the document.
        /// </summary>
        /// <value>The date of arrival of the document.</value>
        [DataMember(Name = "dateOfArrival", EmitDefaultValue = true)]
        public DateTime? DateOfArrival { get; set; }

        /// <summary>
        /// Gets or Sets ProcessingStatus
        /// </summary>
        [DataMember(Name = "processingStatus", EmitDefaultValue = true)]
        public ProcessingStatusEnum ProcessingStatus { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class SecretaryPartnerDocumentListDto {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  SpIdentifiers: ").Append(SpIdentifiers).Append("\n");
            sb.Append("  Partner: ").Append(Partner).Append("\n");
            sb.Append("  PartnerContactNo: ").Append(PartnerContactNo).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  DateOfArrival: ").Append(DateOfArrival).Append("\n");
            sb.Append("  ProcessingStatus: ").Append(ProcessingStatus).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((SecretaryPartnerDocumentListDto)obj);
        }

        /// <summary>
        /// Returns true if SecretaryPartnerDocumentListDto instances are equal
        /// </summary>
        /// <param name="other">Instance of SecretaryPartnerDocumentListDto to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(SecretaryPartnerDocumentListDto other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (Id == other.Id || Id.Equals(other.Id))
                && (
                    SpIdentifiers == other.SpIdentifiers
                    || SpIdentifiers != null
                        && other.SpIdentifiers != null
                        && SpIdentifiers.SequenceEqual(other.SpIdentifiers)
                )
                && (Partner == other.Partner || Partner != null && Partner.Equals(other.Partner))
                && (
                    PartnerContactNo == other.PartnerContactNo
                    || PartnerContactNo != null && PartnerContactNo.Equals(other.PartnerContactNo)
                )
                && (CreatedDate == other.CreatedDate || CreatedDate.Equals(other.CreatedDate))
                && (
                    DateOfArrival == other.DateOfArrival
                    || DateOfArrival != null && DateOfArrival.Equals(other.DateOfArrival)
                )
                && (ProcessingStatus == other.ProcessingStatus || ProcessingStatus.Equals(other.ProcessingStatus));
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)

                hashCode = hashCode * 59 + Id.GetHashCode();
                if (SpIdentifiers != null)
                    hashCode = hashCode * 59 + SpIdentifiers.GetHashCode();
                if (Partner != null)
                    hashCode = hashCode * 59 + Partner.GetHashCode();
                if (PartnerContactNo != null)
                    hashCode = hashCode * 59 + PartnerContactNo.GetHashCode();

                hashCode = hashCode * 59 + CreatedDate.GetHashCode();
                if (DateOfArrival != null)
                    hashCode = hashCode * 59 + DateOfArrival.GetHashCode();

                hashCode = hashCode * 59 + ProcessingStatus.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(SecretaryPartnerDocumentListDto left, SecretaryPartnerDocumentListDto right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(SecretaryPartnerDocumentListDto left, SecretaryPartnerDocumentListDto right)
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

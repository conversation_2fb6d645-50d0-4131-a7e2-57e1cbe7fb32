/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON>l f<PERSON>ó/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    /// The status of document classification.
    /// </summary>
    /// <value>The status of document classification.</value>
    [TypeConverter(typeof(CustomEnumConverter<DocumentClassificationStatusEnum>))]
    [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
    public enum DocumentClassificationStatusEnum
    {
        /// <summary>
        /// Enum Pending for Pending
        /// </summary>
        [EnumMember(Value = "Pending")]
        Pending = 1,

        /// <summary>
        /// Enum Processing for Processing
        /// </summary>
        [EnumMember(Value = "Processing")]
        Processing = 2,

        /// <summary>
        /// Enum Success for Success
        /// </summary>
        [EnumMember(Value = "Success")]
        Success = 3,

        /// <summary>
        /// Enum Failed for Failed
        /// </summary>
        [EnumMember(Value = "Failed")]
        Failed = 4,

        /// <summary>
        /// Enum Timeout for Timeout
        /// </summary>
        [EnumMember(Value = "Timeout")]
        Timeout = 5,
    }
}

/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON>/felület irányából elérhet<PERSON>, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class ListSecretaryDocuments200Response : IEquatable<ListSecretaryDocuments200Response>
    {
        /// <summary>
        /// Gets or Sets Data
        /// </summary>
        [Required]
        [DataMember(Name = "data", EmitDefaultValue = false)]
        public List<SecretaryDocumentListDto> Data { get; set; }

        /// <summary>
        /// Total number of records matching the filters
        /// </summary>
        /// <value>Total number of records matching the filters</value>
        /* <example>247</example> */
        [Required]
        [DataMember(Name = "totalRecords", EmitDefaultValue = true)]
        public int TotalRecords { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        /// <value>Current page number</value>
        /* <example>1</example> */
        [Required]
        [DataMember(Name = "page", EmitDefaultValue = true)]
        public int Page { get; set; }

        /// <summary>
        /// Number of records per page
        /// </summary>
        /// <value>Number of records per page</value>
        /* <example>10</example> */
        [Required]
        [DataMember(Name = "pageSize", EmitDefaultValue = true)]
        public int PageSize { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class ListSecretaryDocuments200Response {\n");
            sb.Append("  Data: ").Append(Data).Append("\n");
            sb.Append("  TotalRecords: ").Append(TotalRecords).Append("\n");
            sb.Append("  Page: ").Append(Page).Append("\n");
            sb.Append("  PageSize: ").Append(PageSize).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((ListSecretaryDocuments200Response)obj);
        }

        /// <summary>
        /// Returns true if ListSecretaryDocuments200Response instances are equal
        /// </summary>
        /// <param name="other">Instance of ListSecretaryDocuments200Response to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(ListSecretaryDocuments200Response other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (Data == other.Data || Data != null && other.Data != null && Data.SequenceEqual(other.Data))
                && (TotalRecords == other.TotalRecords || TotalRecords.Equals(other.TotalRecords))
                && (Page == other.Page || Page.Equals(other.Page))
                && (PageSize == other.PageSize || PageSize.Equals(other.PageSize));
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)
                if (Data != null)
                    hashCode = hashCode * 59 + Data.GetHashCode();

                hashCode = hashCode * 59 + TotalRecords.GetHashCode();

                hashCode = hashCode * 59 + Page.GetHashCode();

                hashCode = hashCode * 59 + PageSize.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(ListSecretaryDocuments200Response left, ListSecretaryDocuments200Response right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(ListSecretaryDocuments200Response left, ListSecretaryDocuments200Response right)
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

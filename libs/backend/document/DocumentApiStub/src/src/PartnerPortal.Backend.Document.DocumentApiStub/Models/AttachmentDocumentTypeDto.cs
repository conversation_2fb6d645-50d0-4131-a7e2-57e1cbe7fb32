/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON> f<PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class AttachmentDocumentTypeDto : IEquatable<AttachmentDocumentTypeDto>
    {
        /// <summary>
        /// Gets or Sets Attachment
        /// </summary>
        [Required]
        [DataMember(Name = "attachment", EmitDefaultValue = false)]
        public FileInfo Attachment { get; set; }

        /// <summary>
        /// S/P azonosító. A számla egyedi generált azonosítója mentéskor és beküldés után generáljuk.
        /// </summary>
        /// <value>S/P azonosító. A számla egyedi generált azonosítója mentéskor és beküldés után generáljuk.</value>
        [DataMember(Name = "spIdentifier", EmitDefaultValue = true)]
        public string SpIdentifier { get; set; }

        /// <summary>
        /// Gets or Sets DocumentType
        /// </summary>
        [DataMember(Name = "documentType", EmitDefaultValue = true)]
        public DocumentType DocumentType { get; set; }

        /// <summary>
        /// Gets or Sets ProcessingStatus
        /// </summary>
        [DataMember(Name = "processingStatus", EmitDefaultValue = true)]
        public EloProcessingStatusEnum ProcessingStatus { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class AttachmentDocumentTypeDto {\n");
            sb.Append("  Attachment: ").Append(Attachment).Append("\n");
            sb.Append("  SpIdentifier: ").Append(SpIdentifier).Append("\n");
            sb.Append("  DocumentType: ").Append(DocumentType).Append("\n");
            sb.Append("  ProcessingStatus: ").Append(ProcessingStatus).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((AttachmentDocumentTypeDto)obj);
        }

        /// <summary>
        /// Returns true if AttachmentDocumentTypeDto instances are equal
        /// </summary>
        /// <param name="other">Instance of AttachmentDocumentTypeDto to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(AttachmentDocumentTypeDto other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (Attachment == other.Attachment || Attachment != null && Attachment.Equals(other.Attachment))
                && (
                    SpIdentifier == other.SpIdentifier
                    || SpIdentifier != null && SpIdentifier.Equals(other.SpIdentifier)
                )
                && (DocumentType == other.DocumentType || DocumentType.Equals(other.DocumentType))
                && (ProcessingStatus == other.ProcessingStatus || ProcessingStatus.Equals(other.ProcessingStatus));
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)
                if (Attachment != null)
                    hashCode = hashCode * 59 + Attachment.GetHashCode();
                if (SpIdentifier != null)
                    hashCode = hashCode * 59 + SpIdentifier.GetHashCode();

                hashCode = hashCode * 59 + DocumentType.GetHashCode();

                hashCode = hashCode * 59 + ProcessingStatus.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(AttachmentDocumentTypeDto left, AttachmentDocumentTypeDto right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(AttachmentDocumentTypeDto left, AttachmentDocumentTypeDto right)
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

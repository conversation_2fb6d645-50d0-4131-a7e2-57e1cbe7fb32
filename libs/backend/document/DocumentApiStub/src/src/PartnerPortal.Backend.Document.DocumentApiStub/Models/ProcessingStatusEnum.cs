/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON> f<PERSON>ló/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    /// The status of the document processing.
    /// </summary>
    /// <value>The status of the document processing.</value>
    [TypeConverter(typeof(CustomEnumConverter<ProcessingStatusEnum>))]
    [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
    public enum ProcessingStatusEnum
    {
        /// <summary>
        /// Enum Success for Success
        /// </summary>
        [EnumMember(Value = "Success")]
        Success = 1,

        /// <summary>
        /// Enum Default for Default
        /// </summary>
        [EnumMember(Value = "Default")]
        Default = 2,

        /// <summary>
        /// Enum Failed for Failed
        /// </summary>
        [EnumMember(Value = "Failed")]
        Failed = 3,

        /// <summary>
        /// Enum Processing for Processing
        /// </summary>
        [EnumMember(Value = "Processing")]
        Processing = 4,
    }
}

/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner portál f<PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    /// Status of column validation: * `Ok` - The value is correct * `Discrepancy` - There is a discrepancy between the two values * `Missing` - Value not found in document or no matching value in data source * `Warning` - There is a warning * `Info` - There is an info * `NavDiscrepancy` - There is a discrepancy between the two values from NAV * `Navsource` - The value is from NAV
    /// </summary>
    /// <value>Status of column validation: * `Ok` - The value is correct * `Discrepancy` - There is a discrepancy between the two values * `Missing` - Value not found in document or no matching value in data source * `Warning` - There is a warning * `Info` - There is an info * `NavDiscrepancy` - There is a discrepancy between the two values from NAV * `Navsource` - The value is from NAV </value>
    [TypeConverter(typeof(CustomEnumConverter<ColumnStatus>))]
    [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
    public enum ColumnStatus
    {
        /// <summary>
        /// Enum Ok for Ok
        /// </summary>
        [EnumMember(Value = "Ok")]
        Ok = 1,

        /// <summary>
        /// Enum Discrepancy for Discrepancy
        /// </summary>
        [EnumMember(Value = "Discrepancy")]
        Discrepancy = 2,

        /// <summary>
        /// Enum Missing for Missing
        /// </summary>
        [EnumMember(Value = "Missing")]
        Missing = 3,

        /// <summary>
        /// Enum Warning for Warning
        /// </summary>
        [EnumMember(Value = "Warning")]
        Warning = 4,

        /// <summary>
        /// Enum Info for Info
        /// </summary>
        [EnumMember(Value = "Info")]
        Info = 5,

        /// <summary>
        /// Enum NavDiscrepancy for NavDiscrepancy
        /// </summary>
        [EnumMember(Value = "NavDiscrepancy")]
        NavDiscrepancy = 6,

        /// <summary>
        /// Enum Navsource for Navsource
        /// </summary>
        [EnumMember(Value = "Navsource")]
        Navsource = 7,
    }
}

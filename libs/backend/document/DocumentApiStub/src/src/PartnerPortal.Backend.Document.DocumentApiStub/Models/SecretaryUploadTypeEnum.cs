/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON> f<PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    /// The type of document upload.
    /// </summary>
    /// <value>The type of document upload.</value>
    [TypeConverter(typeof(CustomEnumConverter<SecretaryUploadTypeEnum>))]
    [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
    public enum SecretaryUploadTypeEnum
    {
        /// <summary>
        /// Enum PerCase for PerCase
        /// </summary>
        [EnumMember(Value = "PerCase")]
        PerCase = 1,

        /// <summary>
        /// Enum Batch for Batch
        /// </summary>
        [EnumMember(Value = "Batch")]
        Batch = 2,

        /// <summary>
        /// Enum Manual for Manual
        /// </summary>
        [EnumMember(Value = "Manual")]
        Manual = 3,
    }
}

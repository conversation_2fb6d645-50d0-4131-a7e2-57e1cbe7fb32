/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON>/felület irányá<PERSON>ól elérhe<PERSON>, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class CreateSecretaryManualDocumentUploadDraftRequest
        : IEquatable<CreateSecretaryManualDocumentUploadDraftRequest>
    {
        /// <summary>
        /// Document upload identifier generated on the frontend.
        /// </summary>
        /// <value>Document upload identifier generated on the frontend.</value>
        [Required]
        [DataMember(Name = "contextId", EmitDefaultValue = true)]
        public Guid ContextId { get; set; }

        /// <summary>
        /// Identifier of the associated partner.
        /// </summary>
        /// <value>Identifier of the associated partner.</value>
        [DataMember(Name = "contactNo", EmitDefaultValue = true)]
        public string ContactNo { get; set; }

        /// <summary>
        /// Name of the associated partner.
        /// </summary>
        /// <value>Name of the associated partner.</value>
        [DataMember(Name = "contactName", EmitDefaultValue = true)]
        public string ContactName { get; set; }

        /// <summary>
        /// Identifier of the associated project.
        /// </summary>
        /// <value>Identifier of the associated project.</value>
        [DataMember(Name = "projectNo", EmitDefaultValue = false)]
        public string ProjectNo { get; set; }

        /// <summary>
        /// Description of the associated project.
        /// </summary>
        /// <value>Description of the associated project.</value>
        [DataMember(Name = "projectDescription", EmitDefaultValue = true)]
        public string ProjectDescription { get; set; }

        /// <summary>
        /// Identifier of the associated contract.
        /// </summary>
        /// <value>Identifier of the associated contract.</value>
        [DataMember(Name = "contractNo", EmitDefaultValue = false)]
        public string ContractNo { get; set; }

        /// <summary>
        /// Gets or Sets Type
        /// </summary>
        [Required]
        [DataMember(Name = "type", EmitDefaultValue = true)]
        public AccountTypeEnum Type { get; set; }

        /// <summary>
        /// List of attachments and their associated document types.
        /// </summary>
        /// <value>List of attachments and their associated document types.</value>
        [Required]
        [DataMember(Name = "attachmentDocumentTypes", EmitDefaultValue = false)]
        public List<AttachmentDocumentTypeDto> AttachmentDocumentTypes { get; set; }

        /// <summary>
        /// Arrival date of the document.
        /// </summary>
        /// <value>Arrival date of the document.</value>
        [DataMember(Name = "secretaryDateOfArrival", EmitDefaultValue = true)]
        public DateTime? SecretaryDateOfArrival { get; set; }

        /// <summary>
        /// Arrival date of the document.
        /// </summary>
        /// <value>Arrival date of the document.</value>
        [DataMember(Name = "dateOfArrival", EmitDefaultValue = true)]
        public DateTime? DateOfArrival { get; set; }

        /// <summary>
        /// Optional comment.
        /// </summary>
        /// <value>Optional comment.</value>
        [DataMember(Name = "comment", EmitDefaultValue = false)]
        public string Comment { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class CreateSecretaryManualDocumentUploadDraftRequest {\n");
            sb.Append("  ContextId: ").Append(ContextId).Append("\n");
            sb.Append("  ContactNo: ").Append(ContactNo).Append("\n");
            sb.Append("  ContactName: ").Append(ContactName).Append("\n");
            sb.Append("  ProjectNo: ").Append(ProjectNo).Append("\n");
            sb.Append("  ProjectDescription: ").Append(ProjectDescription).Append("\n");
            sb.Append("  ContractNo: ").Append(ContractNo).Append("\n");
            sb.Append("  Type: ").Append(Type).Append("\n");
            sb.Append("  AttachmentDocumentTypes: ").Append(AttachmentDocumentTypes).Append("\n");
            sb.Append("  SecretaryDateOfArrival: ").Append(SecretaryDateOfArrival).Append("\n");
            sb.Append("  DateOfArrival: ").Append(DateOfArrival).Append("\n");
            sb.Append("  Comment: ").Append(Comment).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((CreateSecretaryManualDocumentUploadDraftRequest)obj);
        }

        /// <summary>
        /// Returns true if CreateSecretaryManualDocumentUploadDraftRequest instances are equal
        /// </summary>
        /// <param name="other">Instance of CreateSecretaryManualDocumentUploadDraftRequest to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(CreateSecretaryManualDocumentUploadDraftRequest other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (ContextId == other.ContextId || ContextId.Equals(other.ContextId))
                && (ContactNo == other.ContactNo || ContactNo != null && ContactNo.Equals(other.ContactNo))
                && (ContactName == other.ContactName || ContactName != null && ContactName.Equals(other.ContactName))
                && (ProjectNo == other.ProjectNo || ProjectNo != null && ProjectNo.Equals(other.ProjectNo))
                && (
                    ProjectDescription == other.ProjectDescription
                    || ProjectDescription != null && ProjectDescription.Equals(other.ProjectDescription)
                )
                && (ContractNo == other.ContractNo || ContractNo != null && ContractNo.Equals(other.ContractNo))
                && (Type == other.Type || Type.Equals(other.Type))
                && (
                    AttachmentDocumentTypes == other.AttachmentDocumentTypes
                    || AttachmentDocumentTypes != null
                        && other.AttachmentDocumentTypes != null
                        && AttachmentDocumentTypes.SequenceEqual(other.AttachmentDocumentTypes)
                )
                && (
                    SecretaryDateOfArrival == other.SecretaryDateOfArrival
                    || SecretaryDateOfArrival != null && SecretaryDateOfArrival.Equals(other.SecretaryDateOfArrival)
                )
                && (
                    DateOfArrival == other.DateOfArrival
                    || DateOfArrival != null && DateOfArrival.Equals(other.DateOfArrival)
                )
                && (Comment == other.Comment || Comment != null && Comment.Equals(other.Comment));
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)

                hashCode = hashCode * 59 + ContextId.GetHashCode();
                if (ContactNo != null)
                    hashCode = hashCode * 59 + ContactNo.GetHashCode();
                if (ContactName != null)
                    hashCode = hashCode * 59 + ContactName.GetHashCode();
                if (ProjectNo != null)
                    hashCode = hashCode * 59 + ProjectNo.GetHashCode();
                if (ProjectDescription != null)
                    hashCode = hashCode * 59 + ProjectDescription.GetHashCode();
                if (ContractNo != null)
                    hashCode = hashCode * 59 + ContractNo.GetHashCode();

                hashCode = hashCode * 59 + Type.GetHashCode();
                if (AttachmentDocumentTypes != null)
                    hashCode = hashCode * 59 + AttachmentDocumentTypes.GetHashCode();
                if (SecretaryDateOfArrival != null)
                    hashCode = hashCode * 59 + SecretaryDateOfArrival.GetHashCode();
                if (DateOfArrival != null)
                    hashCode = hashCode * 59 + DateOfArrival.GetHashCode();
                if (Comment != null)
                    hashCode = hashCode * 59 + Comment.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(
            CreateSecretaryManualDocumentUploadDraftRequest left,
            CreateSecretaryManualDocumentUploadDraftRequest right
        )
        {
            return Equals(left, right);
        }

        public static bool operator !=(
            CreateSecretaryManualDocumentUploadDraftRequest left,
            CreateSecretaryManualDocumentUploadDraftRequest right
        )
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

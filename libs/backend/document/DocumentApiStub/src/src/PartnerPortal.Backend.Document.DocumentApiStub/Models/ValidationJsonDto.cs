/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON> f<PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class ValidationJsonDto : IEquatable<ValidationJsonDto>
    {
        /// <summary>
        /// Name of the column being validated
        /// </summary>
        /// <value>Name of the column being validated</value>
        /* <example>VendorName</example> */
        [Required]
        [DataMember(Name = "eloColumnName", EmitDefaultValue = false)]
        public string EloColumnName { get; set; }

        /// <summary>
        /// Gets or Sets Status
        /// </summary>
        [Required]
        [DataMember(Name = "status", EmitDefaultValue = true)]
        public ColumnStatus Status { get; set; }

        /// <summary>
        /// Value from the Business Central system
        /// </summary>
        /// <value>Value from the Business Central system</value>
        [DataMember(Name = "bcColumnValue", EmitDefaultValue = true)]
        public string BcColumnValue { get; set; }

        /// <summary>
        /// Value extracted from the document by AI
        /// </summary>
        /// <value>Value extracted from the document by AI</value>
        [DataMember(Name = "documentAiColumnValue", EmitDefaultValue = true)]
        public string DocumentAiColumnValue { get; set; }

        /// <summary>
        /// Value from the NAV system
        /// </summary>
        /// <value>Value from the NAV system</value>
        [DataMember(Name = "navColumnValue", EmitDefaultValue = true)]
        public string NavColumnValue { get; set; }

        /// <summary>
        /// Value only
        /// </summary>
        /// <value>Value only</value>
        [DataMember(Name = "valueOnly", EmitDefaultValue = true)]
        public string ValueOnly { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class ValidationJsonDto {\n");
            sb.Append("  EloColumnName: ").Append(EloColumnName).Append("\n");
            sb.Append("  Status: ").Append(Status).Append("\n");
            sb.Append("  BcColumnValue: ").Append(BcColumnValue).Append("\n");
            sb.Append("  DocumentAiColumnValue: ").Append(DocumentAiColumnValue).Append("\n");
            sb.Append("  NavColumnValue: ").Append(NavColumnValue).Append("\n");
            sb.Append("  ValueOnly: ").Append(ValueOnly).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((ValidationJsonDto)obj);
        }

        /// <summary>
        /// Returns true if ValidationJsonDto instances are equal
        /// </summary>
        /// <param name="other">Instance of ValidationJsonDto to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(ValidationJsonDto other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (
                    EloColumnName == other.EloColumnName
                    || EloColumnName != null && EloColumnName.Equals(other.EloColumnName)
                )
                && (Status == other.Status || Status.Equals(other.Status))
                && (
                    BcColumnValue == other.BcColumnValue
                    || BcColumnValue != null && BcColumnValue.Equals(other.BcColumnValue)
                )
                && (
                    DocumentAiColumnValue == other.DocumentAiColumnValue
                    || DocumentAiColumnValue != null && DocumentAiColumnValue.Equals(other.DocumentAiColumnValue)
                )
                && (
                    NavColumnValue == other.NavColumnValue
                    || NavColumnValue != null && NavColumnValue.Equals(other.NavColumnValue)
                )
                && (ValueOnly == other.ValueOnly || ValueOnly != null && ValueOnly.Equals(other.ValueOnly));
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)
                if (EloColumnName != null)
                    hashCode = hashCode * 59 + EloColumnName.GetHashCode();

                hashCode = hashCode * 59 + Status.GetHashCode();
                if (BcColumnValue != null)
                    hashCode = hashCode * 59 + BcColumnValue.GetHashCode();
                if (DocumentAiColumnValue != null)
                    hashCode = hashCode * 59 + DocumentAiColumnValue.GetHashCode();
                if (NavColumnValue != null)
                    hashCode = hashCode * 59 + NavColumnValue.GetHashCode();
                if (ValueOnly != null)
                    hashCode = hashCode * 59 + ValueOnly.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(ValidationJsonDto left, ValidationJsonDto right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(ValidationJsonDto left, ValidationJsonDto right)
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON>/felület irányából elérhe<PERSON>, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class DocumentStatusReportDto : IEquatable<DocumentStatusReportDto>
    {
        /// <summary>
        /// Document upload identifier.
        /// </summary>
        /// <value>Document upload identifier.</value>
        [Required]
        [DataMember(Name = "id", EmitDefaultValue = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// Whether the document is a secretary document.
        /// </summary>
        /// <value>Whether the document is a secretary document.</value>
        [DataMember(Name = "isSecretaryDocument", EmitDefaultValue = true)]
        public bool IsSecretaryDocument { get; set; }

        /// <summary>
        /// Type of secretary document upload.
        /// </summary>
        /// <value>Type of secretary document upload.</value>
        [DataMember(Name = "secretaryUploadType", EmitDefaultValue = false)]
        public string SecretaryUploadType { get; set; }

        /// <summary>
        /// Identifier of the associated partner.
        /// </summary>
        /// <value>Identifier of the associated partner.</value>
        [DataMember(Name = "contactNo", EmitDefaultValue = true)]
        public string ContactNo { get; set; }

        /// <summary>
        /// Identifier of the associated project.
        /// </summary>
        /// <value>Identifier of the associated project.</value>
        [DataMember(Name = "projectNo", EmitDefaultValue = true)]
        public string ProjectNo { get; set; }

        /// <summary>
        /// Description of the associated project.
        /// </summary>
        /// <value>Description of the associated project.</value>
        [DataMember(Name = "projectDescription", EmitDefaultValue = true)]
        public string ProjectDescription { get; set; }

        /// <summary>
        /// Identifier of the associated contract.
        /// </summary>
        /// <value>Identifier of the associated contract.</value>
        [DataMember(Name = "contractNo", EmitDefaultValue = true)]
        public string ContractNo { get; set; }

        /// <summary>
        /// The date and time when the document upload was created.
        /// </summary>
        /// <value>The date and time when the document upload was created.</value>
        [DataMember(Name = "createdDate", EmitDefaultValue = true)]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Érkeztetés dátuma.
        /// </summary>
        /// <value>Érkeztetés dátuma.</value>
        [DataMember(Name = "dateOfArrival", EmitDefaultValue = true)]
        public DateTime? DateOfArrival { get; set; }

        /// <summary>
        /// Titkár által beállított érkeztetés dátuma.
        /// </summary>
        /// <value>Titkár által beállított érkeztetés dátuma.</value>
        [DataMember(Name = "secretaryDateOfArrival", EmitDefaultValue = true)]
        public DateTime? SecretaryDateOfArrival { get; set; }

        /// <summary>
        /// Gets or Sets UploadStatus
        /// </summary>
        [Required]
        [DataMember(Name = "uploadStatus", EmitDefaultValue = true)]
        public UploadStatusEnum UploadStatus { get; set; }

        /// <summary>
        /// Total number of attachments in the document.
        /// </summary>
        /// <value>Total number of attachments in the document.</value>
        [DataMember(Name = "totalAttachments", EmitDefaultValue = true)]
        public int TotalAttachments { get; set; }

        /// <summary>
        /// Number of documents that have been successfully analyzed.
        /// </summary>
        /// <value>Number of documents that have been successfully analyzed.</value>
        [DataMember(Name = "analyzedDocuments", EmitDefaultValue = true)]
        public int AnalyzedDocuments { get; set; }

        /// <summary>
        /// Number of documents pending analysis.
        /// </summary>
        /// <value>Number of documents pending analysis.</value>
        [DataMember(Name = "pendingAnalysisDocuments", EmitDefaultValue = true)]
        public int PendingAnalysisDocuments { get; set; }

        /// <summary>
        /// Number of documents where analysis failed.
        /// </summary>
        /// <value>Number of documents where analysis failed.</value>
        [DataMember(Name = "failedAnalysisDocuments", EmitDefaultValue = true)]
        public int FailedAnalysisDocuments { get; set; }

        /// <summary>
        /// Number of documents that have been successfully validated.
        /// </summary>
        /// <value>Number of documents that have been successfully validated.</value>
        [DataMember(Name = "validatedDocuments", EmitDefaultValue = true)]
        public int ValidatedDocuments { get; set; }

        /// <summary>
        /// Number of documents pending validation.
        /// </summary>
        /// <value>Number of documents pending validation.</value>
        [DataMember(Name = "pendingValidationDocuments", EmitDefaultValue = true)]
        public int PendingValidationDocuments { get; set; }

        /// <summary>
        /// Number of documents where validation failed.
        /// </summary>
        /// <value>Number of documents where validation failed.</value>
        [DataMember(Name = "failedValidationDocuments", EmitDefaultValue = true)]
        public int FailedValidationDocuments { get; set; }

        /// <summary>
        /// Number of documents where ELO has been done successfully.
        /// </summary>
        /// <value>Number of documents where ELO has been done successfully.</value>
        [DataMember(Name = "successEloDocuments", EmitDefaultValue = true)]
        public int SuccessEloDocuments { get; set; }

        /// <summary>
        /// Number of documents where ELO is pending.
        /// </summary>
        /// <value>Number of documents where ELO is pending.</value>
        [DataMember(Name = "pendingEloDocuments", EmitDefaultValue = true)]
        public int PendingEloDocuments { get; set; }

        /// <summary>
        /// Number of documents where ELO has failed.
        /// </summary>
        /// <value>Number of documents where ELO has failed.</value>
        [DataMember(Name = "failedEloDocuments", EmitDefaultValue = true)]
        public int FailedEloDocuments { get; set; }

        /// <summary>
        /// Detailed analysis status for each attachment.
        /// </summary>
        /// <value>Detailed analysis status for each attachment.</value>
        [DataMember(Name = "analysisDetails", EmitDefaultValue = false)]
        public List<DocumentAnalysisDetailDto> AnalysisDetails { get; set; }

        /// <summary>
        /// Detailed validation status for each attachment.
        /// </summary>
        /// <value>Detailed validation status for each attachment.</value>
        [DataMember(Name = "validationDetails", EmitDefaultValue = false)]
        public List<DocumentValidationDetailDto> ValidationDetails { get; set; }

        /// <summary>
        /// Detailed ELO status for each attachment.
        /// </summary>
        /// <value>Detailed ELO status for each attachment.</value>
        [DataMember(Name = "eloDetails", EmitDefaultValue = false)]
        public List<DocumentEloDetailDto> EloDetails { get; set; }

        /// <summary>
        /// Detailed classification status for each attachment.
        /// </summary>
        /// <value>Detailed classification status for each attachment.</value>
        [DataMember(Name = "classificationDetails", EmitDefaultValue = false)]
        public List<DocumentClassificationDetailDto> ClassificationDetails { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class DocumentStatusReportDto {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  IsSecretaryDocument: ").Append(IsSecretaryDocument).Append("\n");
            sb.Append("  SecretaryUploadType: ").Append(SecretaryUploadType).Append("\n");
            sb.Append("  ContactNo: ").Append(ContactNo).Append("\n");
            sb.Append("  ProjectNo: ").Append(ProjectNo).Append("\n");
            sb.Append("  ProjectDescription: ").Append(ProjectDescription).Append("\n");
            sb.Append("  ContractNo: ").Append(ContractNo).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  DateOfArrival: ").Append(DateOfArrival).Append("\n");
            sb.Append("  SecretaryDateOfArrival: ").Append(SecretaryDateOfArrival).Append("\n");
            sb.Append("  UploadStatus: ").Append(UploadStatus).Append("\n");
            sb.Append("  TotalAttachments: ").Append(TotalAttachments).Append("\n");
            sb.Append("  AnalyzedDocuments: ").Append(AnalyzedDocuments).Append("\n");
            sb.Append("  PendingAnalysisDocuments: ").Append(PendingAnalysisDocuments).Append("\n");
            sb.Append("  FailedAnalysisDocuments: ").Append(FailedAnalysisDocuments).Append("\n");
            sb.Append("  ValidatedDocuments: ").Append(ValidatedDocuments).Append("\n");
            sb.Append("  PendingValidationDocuments: ").Append(PendingValidationDocuments).Append("\n");
            sb.Append("  FailedValidationDocuments: ").Append(FailedValidationDocuments).Append("\n");
            sb.Append("  SuccessEloDocuments: ").Append(SuccessEloDocuments).Append("\n");
            sb.Append("  PendingEloDocuments: ").Append(PendingEloDocuments).Append("\n");
            sb.Append("  FailedEloDocuments: ").Append(FailedEloDocuments).Append("\n");
            sb.Append("  AnalysisDetails: ").Append(AnalysisDetails).Append("\n");
            sb.Append("  ValidationDetails: ").Append(ValidationDetails).Append("\n");
            sb.Append("  EloDetails: ").Append(EloDetails).Append("\n");
            sb.Append("  ClassificationDetails: ").Append(ClassificationDetails).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((DocumentStatusReportDto)obj);
        }

        /// <summary>
        /// Returns true if DocumentStatusReportDto instances are equal
        /// </summary>
        /// <param name="other">Instance of DocumentStatusReportDto to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(DocumentStatusReportDto other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (Id == other.Id || Id.Equals(other.Id))
                && (
                    IsSecretaryDocument == other.IsSecretaryDocument
                    || IsSecretaryDocument.Equals(other.IsSecretaryDocument)
                )
                && (
                    SecretaryUploadType == other.SecretaryUploadType
                    || SecretaryUploadType != null && SecretaryUploadType.Equals(other.SecretaryUploadType)
                )
                && (ContactNo == other.ContactNo || ContactNo != null && ContactNo.Equals(other.ContactNo))
                && (ProjectNo == other.ProjectNo || ProjectNo != null && ProjectNo.Equals(other.ProjectNo))
                && (
                    ProjectDescription == other.ProjectDescription
                    || ProjectDescription != null && ProjectDescription.Equals(other.ProjectDescription)
                )
                && (ContractNo == other.ContractNo || ContractNo != null && ContractNo.Equals(other.ContractNo))
                && (CreatedDate == other.CreatedDate || CreatedDate.Equals(other.CreatedDate))
                && (
                    DateOfArrival == other.DateOfArrival
                    || DateOfArrival != null && DateOfArrival.Equals(other.DateOfArrival)
                )
                && (
                    SecretaryDateOfArrival == other.SecretaryDateOfArrival
                    || SecretaryDateOfArrival != null && SecretaryDateOfArrival.Equals(other.SecretaryDateOfArrival)
                )
                && (UploadStatus == other.UploadStatus || UploadStatus.Equals(other.UploadStatus))
                && (TotalAttachments == other.TotalAttachments || TotalAttachments.Equals(other.TotalAttachments))
                && (AnalyzedDocuments == other.AnalyzedDocuments || AnalyzedDocuments.Equals(other.AnalyzedDocuments))
                && (
                    PendingAnalysisDocuments == other.PendingAnalysisDocuments
                    || PendingAnalysisDocuments.Equals(other.PendingAnalysisDocuments)
                )
                && (
                    FailedAnalysisDocuments == other.FailedAnalysisDocuments
                    || FailedAnalysisDocuments.Equals(other.FailedAnalysisDocuments)
                )
                && (
                    ValidatedDocuments == other.ValidatedDocuments
                    || ValidatedDocuments.Equals(other.ValidatedDocuments)
                )
                && (
                    PendingValidationDocuments == other.PendingValidationDocuments
                    || PendingValidationDocuments.Equals(other.PendingValidationDocuments)
                )
                && (
                    FailedValidationDocuments == other.FailedValidationDocuments
                    || FailedValidationDocuments.Equals(other.FailedValidationDocuments)
                )
                && (
                    SuccessEloDocuments == other.SuccessEloDocuments
                    || SuccessEloDocuments.Equals(other.SuccessEloDocuments)
                )
                && (
                    PendingEloDocuments == other.PendingEloDocuments
                    || PendingEloDocuments.Equals(other.PendingEloDocuments)
                )
                && (
                    FailedEloDocuments == other.FailedEloDocuments
                    || FailedEloDocuments.Equals(other.FailedEloDocuments)
                )
                && (
                    AnalysisDetails == other.AnalysisDetails
                    || AnalysisDetails != null
                        && other.AnalysisDetails != null
                        && AnalysisDetails.SequenceEqual(other.AnalysisDetails)
                )
                && (
                    ValidationDetails == other.ValidationDetails
                    || ValidationDetails != null
                        && other.ValidationDetails != null
                        && ValidationDetails.SequenceEqual(other.ValidationDetails)
                )
                && (
                    EloDetails == other.EloDetails
                    || EloDetails != null && other.EloDetails != null && EloDetails.SequenceEqual(other.EloDetails)
                )
                && (
                    ClassificationDetails == other.ClassificationDetails
                    || ClassificationDetails != null
                        && other.ClassificationDetails != null
                        && ClassificationDetails.SequenceEqual(other.ClassificationDetails)
                );
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)

                hashCode = hashCode * 59 + Id.GetHashCode();

                hashCode = hashCode * 59 + IsSecretaryDocument.GetHashCode();
                if (SecretaryUploadType != null)
                    hashCode = hashCode * 59 + SecretaryUploadType.GetHashCode();
                if (ContactNo != null)
                    hashCode = hashCode * 59 + ContactNo.GetHashCode();
                if (ProjectNo != null)
                    hashCode = hashCode * 59 + ProjectNo.GetHashCode();
                if (ProjectDescription != null)
                    hashCode = hashCode * 59 + ProjectDescription.GetHashCode();
                if (ContractNo != null)
                    hashCode = hashCode * 59 + ContractNo.GetHashCode();

                hashCode = hashCode * 59 + CreatedDate.GetHashCode();
                if (DateOfArrival != null)
                    hashCode = hashCode * 59 + DateOfArrival.GetHashCode();
                if (SecretaryDateOfArrival != null)
                    hashCode = hashCode * 59 + SecretaryDateOfArrival.GetHashCode();

                hashCode = hashCode * 59 + UploadStatus.GetHashCode();

                hashCode = hashCode * 59 + TotalAttachments.GetHashCode();

                hashCode = hashCode * 59 + AnalyzedDocuments.GetHashCode();

                hashCode = hashCode * 59 + PendingAnalysisDocuments.GetHashCode();

                hashCode = hashCode * 59 + FailedAnalysisDocuments.GetHashCode();

                hashCode = hashCode * 59 + ValidatedDocuments.GetHashCode();

                hashCode = hashCode * 59 + PendingValidationDocuments.GetHashCode();

                hashCode = hashCode * 59 + FailedValidationDocuments.GetHashCode();

                hashCode = hashCode * 59 + SuccessEloDocuments.GetHashCode();

                hashCode = hashCode * 59 + PendingEloDocuments.GetHashCode();

                hashCode = hashCode * 59 + FailedEloDocuments.GetHashCode();
                if (AnalysisDetails != null)
                    hashCode = hashCode * 59 + AnalysisDetails.GetHashCode();
                if (ValidationDetails != null)
                    hashCode = hashCode * 59 + ValidationDetails.GetHashCode();
                if (EloDetails != null)
                    hashCode = hashCode * 59 + EloDetails.GetHashCode();
                if (ClassificationDetails != null)
                    hashCode = hashCode * 59 + ClassificationDetails.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(DocumentStatusReportDto left, DocumentStatusReportDto right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(DocumentStatusReportDto left, DocumentStatusReportDto right)
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON>/felület irányából elérhe<PERSON>, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class SecretaryManualDocumentUploadDto : IEquatable<SecretaryManualDocumentUploadDto>
    {
        /// <summary>
        /// Document upload identifier (generated on the frontend).
        /// </summary>
        /// <value>Document upload identifier (generated on the frontend).</value>
        [Required]
        [DataMember(Name = "id", EmitDefaultValue = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// Identifier of the uploading user.
        /// </summary>
        /// <value>Identifier of the uploading user.</value>
        [Required]
        [DataMember(Name = "userId", EmitDefaultValue = true)]
        public int UserId { get; set; }

        /// <summary>
        /// Name of the uploading user.
        /// </summary>
        /// <value>Name of the uploading user.</value>
        [DataMember(Name = "profileName", EmitDefaultValue = true)]
        public string ProfileName { get; set; }

        /// <summary>
        /// Gets or Sets Type
        /// </summary>
        [DataMember(Name = "type", EmitDefaultValue = true)]
        public AccountTypeEnum Type { get; set; }

        /// <summary>
        /// Identifier of the associated partner.
        /// </summary>
        /// <value>Identifier of the associated partner.</value>
        [DataMember(Name = "contactNo", EmitDefaultValue = true)]
        public string ContactNo { get; set; }

        /// <summary>
        /// Name of the associated partner.
        /// </summary>
        /// <value>Name of the associated partner.</value>
        [DataMember(Name = "contactName", EmitDefaultValue = true)]
        public string ContactName { get; set; }

        /// <summary>
        /// Identifier of the associated project.
        /// </summary>
        /// <value>Identifier of the associated project.</value>
        [DataMember(Name = "projectNo", EmitDefaultValue = true)]
        public string ProjectNo { get; set; }

        /// <summary>
        /// Description of the associated project.
        /// </summary>
        /// <value>Description of the associated project.</value>
        [DataMember(Name = "projectDescription", EmitDefaultValue = true)]
        public string ProjectDescription { get; set; }

        /// <summary>
        /// Identifier of the associated contract.
        /// </summary>
        /// <value>Identifier of the associated contract.</value>
        [DataMember(Name = "contractNo", EmitDefaultValue = true)]
        public string ContractNo { get; set; }

        /// <summary>
        /// Optional comment.
        /// </summary>
        /// <value>Optional comment.</value>
        [DataMember(Name = "comment", EmitDefaultValue = false)]
        public string Comment { get; set; }

        /// <summary>
        /// Gets or Sets Status
        /// </summary>
        [Required]
        [DataMember(Name = "status", EmitDefaultValue = true)]
        public UploadStatusEnum Status { get; set; }

        /// <summary>
        /// Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla
        /// </summary>
        /// <value>Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla</value>
        [DataMember(Name = "dateOfArrival", EmitDefaultValue = true)]
        public DateTime? DateOfArrival { get; set; }

        /// <summary>
        /// Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla
        /// </summary>
        /// <value>Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla</value>
        [DataMember(Name = "secretaryDateOfArrival", EmitDefaultValue = true)]
        public DateTime? SecretaryDateOfArrival { get; set; }

        /// <summary>
        /// List of attachments and their associated document types.
        /// </summary>
        /// <value>List of attachments and their associated document types.</value>
        [DataMember(Name = "attachmentDocumentTypes", EmitDefaultValue = false)]
        public List<SecretaryManualAttachmentDocumentTypeDto> AttachmentDocumentTypes { get; set; }

        /// <summary>
        /// The date and time when the document upload was created.
        /// </summary>
        /// <value>The date and time when the document upload was created.</value>
        [DataMember(Name = "createdDate", EmitDefaultValue = true)]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// The date and time when the document upload was last updated.
        /// </summary>
        /// <value>The date and time when the document upload was last updated.</value>
        [DataMember(Name = "lastModDate", EmitDefaultValue = true)]
        public DateTime LastModDate { get; set; }

        /// <summary>
        /// The row version of the document upload.
        /// </summary>
        /// <value>The row version of the document upload.</value>
        [DataMember(Name = "rowVersion", EmitDefaultValue = false)]
        public byte[] RowVersion { get; set; }

        /// <summary>
        /// Gets or Sets ProcessingStatus
        /// </summary>
        [DataMember(Name = "processingStatus", EmitDefaultValue = true)]
        public ProcessingStatusEnum ProcessingStatus { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class SecretaryManualDocumentUploadDto {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  UserId: ").Append(UserId).Append("\n");
            sb.Append("  ProfileName: ").Append(ProfileName).Append("\n");
            sb.Append("  Type: ").Append(Type).Append("\n");
            sb.Append("  ContactNo: ").Append(ContactNo).Append("\n");
            sb.Append("  ContactName: ").Append(ContactName).Append("\n");
            sb.Append("  ProjectNo: ").Append(ProjectNo).Append("\n");
            sb.Append("  ProjectDescription: ").Append(ProjectDescription).Append("\n");
            sb.Append("  ContractNo: ").Append(ContractNo).Append("\n");
            sb.Append("  Comment: ").Append(Comment).Append("\n");
            sb.Append("  Status: ").Append(Status).Append("\n");
            sb.Append("  DateOfArrival: ").Append(DateOfArrival).Append("\n");
            sb.Append("  SecretaryDateOfArrival: ").Append(SecretaryDateOfArrival).Append("\n");
            sb.Append("  AttachmentDocumentTypes: ").Append(AttachmentDocumentTypes).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  LastModDate: ").Append(LastModDate).Append("\n");
            sb.Append("  RowVersion: ").Append(RowVersion).Append("\n");
            sb.Append("  ProcessingStatus: ").Append(ProcessingStatus).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((SecretaryManualDocumentUploadDto)obj);
        }

        /// <summary>
        /// Returns true if SecretaryManualDocumentUploadDto instances are equal
        /// </summary>
        /// <param name="other">Instance of SecretaryManualDocumentUploadDto to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(SecretaryManualDocumentUploadDto other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (Id == other.Id || Id.Equals(other.Id))
                && (UserId == other.UserId || UserId.Equals(other.UserId))
                && (ProfileName == other.ProfileName || ProfileName != null && ProfileName.Equals(other.ProfileName))
                && (Type == other.Type || Type.Equals(other.Type))
                && (ContactNo == other.ContactNo || ContactNo != null && ContactNo.Equals(other.ContactNo))
                && (ContactName == other.ContactName || ContactName != null && ContactName.Equals(other.ContactName))
                && (ProjectNo == other.ProjectNo || ProjectNo != null && ProjectNo.Equals(other.ProjectNo))
                && (
                    ProjectDescription == other.ProjectDescription
                    || ProjectDescription != null && ProjectDescription.Equals(other.ProjectDescription)
                )
                && (ContractNo == other.ContractNo || ContractNo != null && ContractNo.Equals(other.ContractNo))
                && (Comment == other.Comment || Comment != null && Comment.Equals(other.Comment))
                && (Status == other.Status || Status.Equals(other.Status))
                && (
                    DateOfArrival == other.DateOfArrival
                    || DateOfArrival != null && DateOfArrival.Equals(other.DateOfArrival)
                )
                && (
                    SecretaryDateOfArrival == other.SecretaryDateOfArrival
                    || SecretaryDateOfArrival != null && SecretaryDateOfArrival.Equals(other.SecretaryDateOfArrival)
                )
                && (
                    AttachmentDocumentTypes == other.AttachmentDocumentTypes
                    || AttachmentDocumentTypes != null
                        && other.AttachmentDocumentTypes != null
                        && AttachmentDocumentTypes.SequenceEqual(other.AttachmentDocumentTypes)
                )
                && (CreatedDate == other.CreatedDate || CreatedDate.Equals(other.CreatedDate))
                && (LastModDate == other.LastModDate || LastModDate.Equals(other.LastModDate))
                && (RowVersion == other.RowVersion || RowVersion != null && RowVersion.Equals(other.RowVersion))
                && (ProcessingStatus == other.ProcessingStatus || ProcessingStatus.Equals(other.ProcessingStatus));
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)

                hashCode = hashCode * 59 + Id.GetHashCode();

                hashCode = hashCode * 59 + UserId.GetHashCode();
                if (ProfileName != null)
                    hashCode = hashCode * 59 + ProfileName.GetHashCode();

                hashCode = hashCode * 59 + Type.GetHashCode();
                if (ContactNo != null)
                    hashCode = hashCode * 59 + ContactNo.GetHashCode();
                if (ContactName != null)
                    hashCode = hashCode * 59 + ContactName.GetHashCode();
                if (ProjectNo != null)
                    hashCode = hashCode * 59 + ProjectNo.GetHashCode();
                if (ProjectDescription != null)
                    hashCode = hashCode * 59 + ProjectDescription.GetHashCode();
                if (ContractNo != null)
                    hashCode = hashCode * 59 + ContractNo.GetHashCode();
                if (Comment != null)
                    hashCode = hashCode * 59 + Comment.GetHashCode();

                hashCode = hashCode * 59 + Status.GetHashCode();
                if (DateOfArrival != null)
                    hashCode = hashCode * 59 + DateOfArrival.GetHashCode();
                if (SecretaryDateOfArrival != null)
                    hashCode = hashCode * 59 + SecretaryDateOfArrival.GetHashCode();
                if (AttachmentDocumentTypes != null)
                    hashCode = hashCode * 59 + AttachmentDocumentTypes.GetHashCode();

                hashCode = hashCode * 59 + CreatedDate.GetHashCode();

                hashCode = hashCode * 59 + LastModDate.GetHashCode();
                if (RowVersion != null)
                    hashCode = hashCode * 59 + RowVersion.GetHashCode();

                hashCode = hashCode * 59 + ProcessingStatus.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(SecretaryManualDocumentUploadDto left, SecretaryManualDocumentUploadDto right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(SecretaryManualDocumentUploadDto left, SecretaryManualDocumentUploadDto right)
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

/*
 * Market Partner Portal - documents - public/frontend REST API
 *
 * Partner port<PERSON><PERSON>/felület irányából elérhe<PERSON>ő, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 *
 * Generated by: https://openapi-generator.tech
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Newtonsoft.Json;
using PartnerPortal.Backend.Document.DocumentApiStub.Converters;

namespace PartnerPortal.Backend.Document.DocumentApiStub.Models
{
    /// <summary>
    ///
    /// </summary>
    [DataContract]
    public class SecretaryPartnerDocumentDetailDto : IEquatable<SecretaryPartnerDocumentDetailDto>
    {
        /// <summary>
        /// Document upload identifier (generated on the frontend).
        /// </summary>
        /// <value>Document upload identifier (generated on the frontend).</value>
        [DataMember(Name = "id", EmitDefaultValue = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// Identifier of the uploading user.
        /// </summary>
        /// <value>Identifier of the uploading user.</value>
        [DataMember(Name = "userId", EmitDefaultValue = true)]
        public int UserId { get; set; }

        /// <summary>
        /// Identifier of the associated project.
        /// </summary>
        /// <value>Identifier of the associated project.</value>
        [DataMember(Name = "projectNo", EmitDefaultValue = true)]
        public string ProjectNo { get; set; }

        /// <summary>
        /// Description of the associated project.
        /// </summary>
        /// <value>Description of the associated project.</value>
        [DataMember(Name = "projectDescription", EmitDefaultValue = true)]
        public string ProjectDescription { get; set; }

        /// <summary>
        /// Identifier of the associated contract.
        /// </summary>
        /// <value>Identifier of the associated contract.</value>
        [DataMember(Name = "contractNo", EmitDefaultValue = true)]
        public string ContractNo { get; set; }

        /// <summary>
        /// Computed - ProfileName + VatNumber.
        /// </summary>
        /// <value>Computed - ProfileName + VatNumber.</value>
        [DataMember(Name = "partner", EmitDefaultValue = true)]
        public string Partner { get; set; }

        /// <summary>
        /// Partner contact number.
        /// </summary>
        /// <value>Partner contact number.</value>
        [DataMember(Name = "partnerContactNo", EmitDefaultValue = false)]
        public string PartnerContactNo { get; set; }

        /// <summary>
        /// Gets or Sets Type
        /// </summary>
        [DataMember(Name = "type", EmitDefaultValue = true)]
        public AccountTypeEnum Type { get; set; }

        /// <summary>
        /// Optional comment.
        /// </summary>
        /// <value>Optional comment.</value>
        [DataMember(Name = "comment", EmitDefaultValue = false)]
        public string Comment { get; set; }

        /// <summary>
        /// Gets or Sets Status
        /// </summary>
        [DataMember(Name = "status", EmitDefaultValue = true)]
        public UploadStatusEnum Status { get; set; }

        /// <summary>
        /// Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla,
        /// </summary>
        /// <value>Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla,</value>
        [DataMember(Name = "dateOfArrival", EmitDefaultValue = true)]
        public DateTime? DateOfArrival { get; set; }

        /// <summary>
        /// List of attachments and their associated document types.
        /// </summary>
        /// <value>List of attachments and their associated document types.</value>
        [DataMember(Name = "attachmentDocumentTypes", EmitDefaultValue = false)]
        public List<SecretaryManualAttachmentDocumentTypeDto> AttachmentDocumentTypes { get; set; }

        /// <summary>
        /// The date and time when the document upload was created.
        /// </summary>
        /// <value>The date and time when the document upload was created.</value>
        [DataMember(Name = "createdDate", EmitDefaultValue = true)]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// The date and time when the document upload was last updated.
        /// </summary>
        /// <value>The date and time when the document upload was last updated.</value>
        [DataMember(Name = "lastModDate", EmitDefaultValue = true)]
        public DateTime LastModDate { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class SecretaryPartnerDocumentDetailDto {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  UserId: ").Append(UserId).Append("\n");
            sb.Append("  ProjectNo: ").Append(ProjectNo).Append("\n");
            sb.Append("  ProjectDescription: ").Append(ProjectDescription).Append("\n");
            sb.Append("  ContractNo: ").Append(ContractNo).Append("\n");
            sb.Append("  Partner: ").Append(Partner).Append("\n");
            sb.Append("  PartnerContactNo: ").Append(PartnerContactNo).Append("\n");
            sb.Append("  Type: ").Append(Type).Append("\n");
            sb.Append("  Comment: ").Append(Comment).Append("\n");
            sb.Append("  Status: ").Append(Status).Append("\n");
            sb.Append("  DateOfArrival: ").Append(DateOfArrival).Append("\n");
            sb.Append("  AttachmentDocumentTypes: ").Append(AttachmentDocumentTypes).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  LastModDate: ").Append(LastModDate).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object obj)
        {
            if (obj is null)
                return false;
            if (ReferenceEquals(this, obj))
                return true;
            return obj.GetType() == GetType() && Equals((SecretaryPartnerDocumentDetailDto)obj);
        }

        /// <summary>
        /// Returns true if SecretaryPartnerDocumentDetailDto instances are equal
        /// </summary>
        /// <param name="other">Instance of SecretaryPartnerDocumentDetailDto to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(SecretaryPartnerDocumentDetailDto other)
        {
            if (other is null)
                return false;
            if (ReferenceEquals(this, other))
                return true;

            return (Id == other.Id || Id.Equals(other.Id))
                && (UserId == other.UserId || UserId.Equals(other.UserId))
                && (ProjectNo == other.ProjectNo || ProjectNo != null && ProjectNo.Equals(other.ProjectNo))
                && (
                    ProjectDescription == other.ProjectDescription
                    || ProjectDescription != null && ProjectDescription.Equals(other.ProjectDescription)
                )
                && (ContractNo == other.ContractNo || ContractNo != null && ContractNo.Equals(other.ContractNo))
                && (Partner == other.Partner || Partner != null && Partner.Equals(other.Partner))
                && (
                    PartnerContactNo == other.PartnerContactNo
                    || PartnerContactNo != null && PartnerContactNo.Equals(other.PartnerContactNo)
                )
                && (Type == other.Type || Type.Equals(other.Type))
                && (Comment == other.Comment || Comment != null && Comment.Equals(other.Comment))
                && (Status == other.Status || Status.Equals(other.Status))
                && (
                    DateOfArrival == other.DateOfArrival
                    || DateOfArrival != null && DateOfArrival.Equals(other.DateOfArrival)
                )
                && (
                    AttachmentDocumentTypes == other.AttachmentDocumentTypes
                    || AttachmentDocumentTypes != null
                        && other.AttachmentDocumentTypes != null
                        && AttachmentDocumentTypes.SequenceEqual(other.AttachmentDocumentTypes)
                )
                && (CreatedDate == other.CreatedDate || CreatedDate.Equals(other.CreatedDate))
                && (LastModDate == other.LastModDate || LastModDate.Equals(other.LastModDate));
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                var hashCode = 41;
                // Suitable nullity checks etc, of course :)

                hashCode = hashCode * 59 + Id.GetHashCode();

                hashCode = hashCode * 59 + UserId.GetHashCode();
                if (ProjectNo != null)
                    hashCode = hashCode * 59 + ProjectNo.GetHashCode();
                if (ProjectDescription != null)
                    hashCode = hashCode * 59 + ProjectDescription.GetHashCode();
                if (ContractNo != null)
                    hashCode = hashCode * 59 + ContractNo.GetHashCode();
                if (Partner != null)
                    hashCode = hashCode * 59 + Partner.GetHashCode();
                if (PartnerContactNo != null)
                    hashCode = hashCode * 59 + PartnerContactNo.GetHashCode();

                hashCode = hashCode * 59 + Type.GetHashCode();
                if (Comment != null)
                    hashCode = hashCode * 59 + Comment.GetHashCode();

                hashCode = hashCode * 59 + Status.GetHashCode();
                if (DateOfArrival != null)
                    hashCode = hashCode * 59 + DateOfArrival.GetHashCode();
                if (AttachmentDocumentTypes != null)
                    hashCode = hashCode * 59 + AttachmentDocumentTypes.GetHashCode();

                hashCode = hashCode * 59 + CreatedDate.GetHashCode();

                hashCode = hashCode * 59 + LastModDate.GetHashCode();
                return hashCode;
            }
        }

        #region Operators
#pragma warning disable 1591

        public static bool operator ==(SecretaryPartnerDocumentDetailDto left, SecretaryPartnerDocumentDetailDto right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(SecretaryPartnerDocumentDetailDto left, SecretaryPartnerDocumentDetailDto right)
        {
            return !Equals(left, right);
        }

#pragma warning restore 1591
        #endregion Operators
    }
}

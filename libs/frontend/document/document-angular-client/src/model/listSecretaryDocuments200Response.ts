/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON> fel<PERSON>ló/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SecretaryDocumentListDto } from './secretaryDocumentListDto';


export interface ListSecretaryDocuments200Response { 
    data: Array<SecretaryDocumentListDto>;
    /**
     * Total number of records matching the filters
     */
    totalRecords: number;
    /**
     * Current page number
     */
    page: number;
    /**
     * Number of records per page
     */
    pageSize: number;
}


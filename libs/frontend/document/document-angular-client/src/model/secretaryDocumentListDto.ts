/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON> fel<PERSON>ló/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UploadStatusEnum } from './uploadStatusEnum';
import { SecretaryUploadTypeEnum } from './secretaryUploadTypeEnum';
import { ProcessingStatusEnum } from './processingStatusEnum';


export interface SecretaryDocumentListDto { 
    /**
     * Document upload identifier (generated on the frontend).
     */
    id?: string;
    /**
     * S/P azonosítók.
     */
    spIdentifiers?: Array<string> | null;
    /**
     * Computed - ProfileName + VatNumber.
     */
    contactName?: string | null;
    /**
     * Name of the uploader.
     */
    uploader?: string | null;
    /**
     * The date and time when the document upload was created.
     */
    createdDate?: string;
    /**
     * The date of arrival of the document.
     */
    dateOfArrival?: string | null;
    status?: UploadStatusEnum;
    uploadType?: SecretaryUploadTypeEnum;
    processingStatus?: ProcessingStatusEnum;
}


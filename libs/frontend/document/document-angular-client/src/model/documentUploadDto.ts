/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UploadStatusEnum } from './uploadStatusEnum';
import { ProcessingStatusEnum } from './processingStatusEnum';
import { AttachmentDocumentTypeDto } from './attachmentDocumentTypeDto';
import { AccountTypeEnum } from './accountTypeEnum';


export interface DocumentUploadDto { 
    /**
     * Document upload identifier (generated on the frontend).
     */
    id: string;
    /**
     * Identifier of the uploading user.
     */
    userId: number;
    /**
     * Identifier of the associated project.
     */
    projectNo?: string | null;
    /**
     * Description of the associated project.
     */
    projectDescription?: string | null;
    /**
     * Identifier of the associated contract.
     */
    contractNo?: string | null;
    type: AccountTypeEnum;
    /**
     * Optional comment.
     */
    comment?: string;
    status: UploadStatusEnum;
    processingStatus?: ProcessingStatusEnum;
    /**
     * List of attachments and their associated document types.
     */
    attachmentDocumentTypes?: Array<AttachmentDocumentTypeDto>;
    /**
     * The date and time when the document upload was created.
     */
    createdDate?: string;
    /**
     * The row version of the document upload.
     */
    rowVersion?: string;
}


/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UploadStatusEnum } from './uploadStatusEnum';
import { DocumentClassificationDetailDto } from './documentClassificationDetailDto';
import { DocumentValidationDetailDto } from './documentValidationDetailDto';
import { DocumentAnalysisDetailDto } from './documentAnalysisDetailDto';
import { DocumentEloDetailDto } from './documentEloDetailDto';


export interface DocumentStatusReportDto { 
    /**
     * Document upload identifier.
     */
    id: string;
    /**
     * Whether the document is a secretary document.
     */
    isSecretaryDocument?: boolean;
    /**
     * Type of secretary document upload.
     */
    secretaryUploadType?: string;
    /**
     * Identifier of the associated partner.
     */
    contactNo?: string | null;
    /**
     * Identifier of the associated project.
     */
    projectNo?: string | null;
    /**
     * Description of the associated project.
     */
    projectDescription?: string | null;
    /**
     * Identifier of the associated contract.
     */
    contractNo?: string | null;
    /**
     * The date and time when the document upload was created.
     */
    createdDate?: string;
    /**
     * Érkeztetés dátuma.
     */
    dateOfArrival?: string | null;
    /**
     * Titkár által beállított érkeztetés dátuma.
     */
    secretaryDateOfArrival?: string | null;
    uploadStatus: UploadStatusEnum;
    /**
     * Total number of attachments in the document.
     */
    totalAttachments?: number;
    /**
     * Number of documents that have been successfully analyzed.
     */
    analyzedDocuments?: number;
    /**
     * Number of documents pending analysis.
     */
    pendingAnalysisDocuments?: number;
    /**
     * Number of documents where analysis failed.
     */
    failedAnalysisDocuments?: number;
    /**
     * Number of documents that have been successfully validated.
     */
    validatedDocuments?: number;
    /**
     * Number of documents pending validation.
     */
    pendingValidationDocuments?: number;
    /**
     * Number of documents where validation failed.
     */
    failedValidationDocuments?: number;
    /**
     * Number of documents where ELO has been done successfully.
     */
    successEloDocuments?: number;
    /**
     * Number of documents where ELO is pending.
     */
    pendingEloDocuments?: number;
    /**
     * Number of documents where ELO has failed.
     */
    failedEloDocuments?: number;
    /**
     * Detailed analysis status for each attachment.
     */
    analysisDetails?: Array<DocumentAnalysisDetailDto>;
    /**
     * Detailed validation status for each attachment.
     */
    validationDetails?: Array<DocumentValidationDetailDto>;
    /**
     * Detailed ELO status for each attachment.
     */
    eloDetails?: Array<DocumentEloDetailDto>;
    /**
     * Detailed classification status for each attachment.
     */
    classificationDetails?: Array<DocumentClassificationDetailDto>;
}


/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON>/felület irán<PERSON> elérhe<PERSON>, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SecretaryAttachmentDocumentTypeDto } from './secretaryAttachmentDocumentTypeDto';
import { SecretaryUploadTypeEnum } from './secretaryUploadTypeEnum';


export interface CreateSecretaryDocumentUploadDraftRequest { 
    /**
     * Document upload identifier generated on the frontend.
     */
    contextId: string;
    /**
     * Identifier of the associated partner.
     */
    contactNo?: string | null;
    /**
     * Name of the associated partner.
     */
    contactName?: string | null;
    /**
     * Identifier of the associated project.
     */
    projectNo?: string | null;
    /**
     * Description of the associated project.
     */
    projectDescription?: string | null;
    /**
     * Identifier of the associated contract.
     */
    contractNo?: string | null;
    /**
     * Arrival date of the document.
     */
    dateOfArrival?: string | null;
    /**
     * Arrival date of the document.
     */
    secretaryDateOfArrival?: string | null;
    /**
     * List of attachments and their associated document types.
     */
    attachmentDocumentTypes: Array<SecretaryAttachmentDocumentTypeDto>;
    secretaryUploadType?: SecretaryUploadTypeEnum;
    /**
     * Optional comment.
     */
    comment?: string | null;
}


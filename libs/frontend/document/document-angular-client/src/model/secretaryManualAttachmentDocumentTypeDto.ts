/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON> fel<PERSON>ló/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { FileInfo } from './fileInfo';
import { EloProcessingStatusEnum } from './eloProcessingStatusEnum';
import { DocumentType } from './documentType';


export interface SecretaryManualAttachmentDocumentTypeDto { 
    attachment: FileInfo;
    /**
     * S/P azonosító. A számla egyedi generált azonosítója mentéskor és beküldés után generáljuk.
     */
    spIdentifier?: string | null;
    documentType?: DocumentType;
    /**
     * The reason of the processing failure.
     */
    failureReason?: string | null;
    processingStatus?: EloProcessingStatusEnum;
}


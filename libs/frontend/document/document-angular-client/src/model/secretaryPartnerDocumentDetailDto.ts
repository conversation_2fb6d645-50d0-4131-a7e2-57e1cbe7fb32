/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UploadStatusEnum } from './uploadStatusEnum';
import { SecretaryManualAttachmentDocumentTypeDto } from './secretaryManualAttachmentDocumentTypeDto';
import { AccountTypeEnum } from './accountTypeEnum';


export interface SecretaryPartnerDocumentDetailDto { 
    /**
     * Document upload identifier (generated on the frontend).
     */
    id?: string;
    /**
     * Identifier of the uploading user.
     */
    userId?: number;
    /**
     * Identifier of the associated project.
     */
    projectNo?: string | null;
    /**
     * Description of the associated project.
     */
    projectDescription?: string | null;
    /**
     * Identifier of the associated contract.
     */
    contractNo?: string | null;
    /**
     * Computed - ProfileName + VatNumber.
     */
    partner?: string | null;
    /**
     * Partner contact number.
     */
    partnerContactNo?: string;
    type?: AccountTypeEnum;
    /**
     * Optional comment.
     */
    comment?: string;
    status?: UploadStatusEnum;
    /**
     * Érkeztetés dátuma. Ami azt mondja meg hogy hivatalosan mikor került be a számla,
     */
    dateOfArrival?: string | null;
    /**
     * List of attachments and their associated document types.
     */
    attachmentDocumentTypes?: Array<SecretaryManualAttachmentDocumentTypeDto>;
    /**
     * The date and time when the document upload was created.
     */
    createdDate?: string;
    /**
     * The date and time when the document upload was last updated.
     */
    lastModDate?: string;
}


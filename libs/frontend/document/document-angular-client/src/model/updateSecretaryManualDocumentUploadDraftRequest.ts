/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AttachmentDocumentTypeDto } from './attachmentDocumentTypeDto';
import { AccountTypeEnum } from './accountTypeEnum';


export interface UpdateSecretaryManualDocumentUploadDraftRequest { 
    projectNo?: string;
    /**
     * Description of the associated project.
     */
    projectDescription?: string;
    contractNo?: string;
    type: AccountTypeEnum;
    /**
     * List of attachments and their associated document types.
     */
    attachmentDocumentTypes: Array<AttachmentDocumentTypeDto>;
    /**
     * Arrival date of the document.
     */
    secretaryDateOfArrival?: string | null;
    /**
     * Identifier of the associated partner.
     */
    contactNo?: string | null;
    /**
     * Name of the associated partner.
     */
    contactName?: string | null;
    /**
     * Optional comment.
     */
    comment?: string;
    /**
     * The row version of the document upload.
     */
    rowVersion?: string;
}


/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AttachmentDocumentTypeDto } from './attachmentDocumentTypeDto';
import { AccountTypeEnum } from './accountTypeEnum';


export interface CreateSecretaryManualDocumentUploadDraftRequest { 
    /**
     * Document upload identifier generated on the frontend.
     */
    contextId: string;
    /**
     * Identifier of the associated partner.
     */
    contactNo?: string | null;
    /**
     * Name of the associated partner.
     */
    contactName?: string | null;
    /**
     * Identifier of the associated project.
     */
    projectNo?: string;
    /**
     * Description of the associated project.
     */
    projectDescription?: string | null;
    /**
     * Identifier of the associated contract.
     */
    contractNo?: string;
    type: AccountTypeEnum;
    /**
     * List of attachments and their associated document types.
     */
    attachmentDocumentTypes: Array<AttachmentDocumentTypeDto>;
    /**
     * Arrival date of the document.
     */
    secretaryDateOfArrival?: string | null;
    /**
     * Arrival date of the document.
     */
    dateOfArrival?: string | null;
    /**
     * Optional comment.
     */
    comment?: string;
}


/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON> f<PERSON>l<PERSON>/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ProcessingStatusEnum } from './processingStatusEnum';


export interface SecretaryPartnerDocumentListDto { 
    /**
     * Document upload identifier (generated on the frontend).
     */
    id?: string;
    /**
     * S/P azonosítók.
     */
    spIdentifiers?: Array<string> | null;
    /**
     * Computed - ProfileName + VatNumber.
     */
    partner?: string | null;
    /**
     * Partner contact number.
     */
    partnerContactNo?: string | null;
    /**
     * The date and time when the document upload was created.
     */
    createdDate?: string;
    /**
     * The date of arrival of the document.
     */
    dateOfArrival?: string | null;
    processingStatus?: ProcessingStatusEnum;
}


/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON> fel<PERSON>ló/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Status of column validation: * `Ok` - The value is correct * `Discrepancy` - There is a discrepancy between the two values * `Missing` - Value not found in document or no matching value in data source * `Warning` - There is a warning * `Info` - There is an info * `NavDiscrepancy` - There is a discrepancy between the two values from NAV * `Navsource` - The value is from NAV 
 */
export enum ColumnStatus {
    Ok = 'Ok',
    Discrepancy = 'Discrepancy',
    Missing = 'Missing',
    Warning = 'Warning',
    Info = 'Info',
    NavDiscrepancy = 'NavDiscrepancy',
    Navsource = 'Navsource'
}


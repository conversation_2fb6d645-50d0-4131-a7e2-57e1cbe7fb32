/**
 * Market Partner Portal - documents - public/frontend REST API
 * Partner port<PERSON><PERSON> felhasz<PERSON>ló/felület irányából elérhető, dokumentumok kezelésére szolgáló interfész.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ColumnStatus } from './columnStatus';


export interface ValidationJsonDto { 
    /**
     * Name of the column being validated
     */
    eloColumnName: string;
    status: ColumnStatus;
    /**
     * Value from the Business Central system
     */
    bcColumnValue?: string | null;
    /**
     * Value extracted from the document by AI
     */
    documentAiColumnValue?: string | null;
    /**
     * Value from the NAV system
     */
    navColumnValue?: string | null;
    /**
     * Value only
     */
    valueOnly?: string | null;
}


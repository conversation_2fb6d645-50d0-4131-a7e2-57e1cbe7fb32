{"ADMIN_PAGE": {"BC_STATUS": "BC status", "BC_USER_ID": "BC ID", "COMPANY_NAME": "Company name", "CURRENT_EMAIL": "Current e-mail address", "EMAIL_CHANGE_MESSAGE_SUMMARY": "An email has been sent to initiate a change of e-mail address", "EMAIL_CHANGE_MESSAGE_SUMMARY_FOR_NON_REGISTERED_USER": "The email address has changed", "ERROR_EMAIL_FORMAT": "Wrong e-mail!", "FOR_USER": "For user: {{userName}}", "GLOBAL_SEARCH": "Global search", "LAST_LOGIN_DATE": "Last login", "NEW_EMAIL": "New e-mail", "REGISTERED": "Registered", "USERNAME": "Username", "REREGISTER": "Reset register", "REREGISTER_MESSAGE_SUMMARY": "An email for retrying the incomplete registration has been sent", "RESEND_EMAIL": "Resending email"}, "ADDITIONAL_WORK_LIST": {"ACTIVITY_DESCRIPTION": "Activity description", "ADDITIONAL_WORK_NUMBER": "Number of additional works: {{numberOfObjects}}", "AMOUNT_SUM": "Amount", "APPROVER": "Approver: {{profileName}}", "ASSIGNED_TO": "Approver", "COMPANY_NAME": "Company name", "CONTRACT_NUMBER": "Contract ID", "CONTRACT_DATE": "Request date", "DELETE_DATE": "Delete date", "DOCUMENT_NO": "Certificate number", "JUDGE_DATE": "Assessment date", "PROJECT_NAME": "Project name", "SUBSTITUTE": "(substitute)", "ENGINEER_TABS_HEADERS": {"ALL": "All", "ORDER_BY": "Order by", "RECEIVED_ADDITIONAL_WORK": "Received additional works", "SEARCH_BOX_TEXT": "Additional work/Technical scheduling engineer contracting search", "SEARCH_BOX_TEXT_ENGINEER": "search", "SENT_TO_BC": "<PERSON><PERSON> to BC", "SENT_TO_BC_FAILED": "Sent to BC failed", "TITLE": "Additional work list", "WAITING_FOR_APPROVAL": "Waiting for approval"}}, "ADDITIONAL_WORK_DELETED_LIST": {"CONTRACT_DATE": "Claim date"}, "ADDITIONAL_WORK_DELETED_DETAILS": {"CLAIM_STATUS": "Claim status", "COMMENT": "Comment", "CREATOR_COMPANY_NAME": "Létrehozó cég neve", "CREATED_DATE": "Created date", "CREATOR_STATUS": "Creator status", "DELETE_DATE": "Delete date", "LINE_NO": "Line number"}, "ADDITIONAL_WORK_DETAILS": {"FOR_ENGINEERS": {"DECLINED": "Additional work declined", "DECLINE": "DECLINE", "DELETED": "Additional work deleted", "BACK": "Back", "BACK_TO_REQUEST": "Back to incoming request", "BACK_TO_REQUEST_LIST": "Back to incoming request list", "JUSTIFICATION_SENT": "Justification sent", "APPROVE": "APPROVE", "APPROVE_DIALOG": "Successful additional work submission to BC", "DECLINE_TITLE": "Reason for rejecting the additional work", "DECLINE_DESCRIPTION": "The additional work request will not be forwarded to BC, and it will be deleted from the MarketON portal."}}, "ATTACHMENTS": {"ATTACHMENTS": "Attachments", "BACK_TO": "Back", "BACK_TO_AW": "Back to additional work", "BACK_TO_CC": "Back to completion certificate", "DOWNLOAD_ALL": "Download all attachments", "NO_ATTACHMENT_AW": "No attachments have been uploaded to this additional work claim", "NO_ATTACHMENT_CC": "No attachments have been uploaded to this completion certificate claim", "UNSUCCESSFUL_DOWNLOAD": "Download failed", "UPLOADED_DOCUMENTS": "Uploaded documents"}, "BADGE_LABEL": {"AKTÍV": "Active", "JÓVÁHAGYVA": "Approved", "LEZÁRT": "Closed", "SZERZŐDÉSMÓDOSÍTÁS": "Contract amendment", "ELBÍRÁLT": "Decided", "VISSZAUTASÍTVA": "Declined", "TÖRÖLVE": "Deleted", "VÁLLALKOZÁS": "Enterprise", "KIVITELEZÉS": "Implementation", "ÁTADÁS KIVITELEZÉSNEK": "Handover for implementation", "FOLYAMATBAN": "In progress", "NYITOTT": "Submitted", "BEKÜLDVE": "Received", "KÜLDÉS SIKERTELEN": "Send failed", "ALÁÍRT": "Signed", "ELBÍRÁLANDÓ": "Waiting for approval", "GARANCIÁLIS": "Warranty", "ÁTADÁS GARANCIÁLIS IDŐSZAKRA": "Handover for warranty period", "SZERKESZTÉS ALATT": "Currently editing", "ELŐFELTÉTELEK JÓVÁHAGYÁSA FOLYAMATBAN": "Prerequisites approval in progress"}, "COMMON": {"ALL_ITEMS": "All items", "ATTACHMENTS": "Attachments ", "BACK": "Back", "BACK_TO_DASHBOARD": "Back to dashboard", "BACK_TO_HOME_PAGE": "Back to home page", "CANCEL": "Cancel", "COMMON": "General", "CONFIRM_PASSWORD": "Confirm password", "CONFIRM_PASSWORD_PLACEHOLDER": "Confirm password", "COUNT": "Count", "EMAIL": "E-mail address", "EMAIL_PLACEHOLDER": "E-mail address", "EMPTY": "Empty", "ERROR": "Error", "ERROR_BC": "The system may have failed to connect to the BC. Please try again!", "ERROR_EXPIRES_SESSION": "The session has expired. Please log in again", "ERROR_TEMPORARY": "There was a momentary technical error", "ERROR_TEXT": "Error", "EUR": "EUR", "EXCEL_EXPORT": "Excel export", "EXCEPTION_TITLE": "MarketON developer", "HUF": "HUF", "LOGOUT": "Logout", "NO": "No", "OK": "Ok", "ORDER_BY": "Order by", "PASSWORD": "Password", "PASSWORD_PLACEHOLDER": "Password", "PLEASE_WAIT": "Please wait...", "PROJECT": "Project", "SEARCH": "Search", "SEARCH_HAS_NO_RESULT": "Search has no result.", "SEND": "Send", "SERVER_ERROR": "The server is currently unavailable", "SERVER_ERROR_DESCRIPTION": "Please check your internet access", "SIGN_IN": "Sign in", "SIGN_UP": "Sign up", "SITENAME": "MarketON", "SUBMIT": "Send", "TIG_CLAIM_DETAIL": "Completion certificate claim detail ", "TRY_AGAIN": "Try again", "TUTORIAL": "Tutorial video", "UNEXPECTED_ERROR": "An unexpected error occurred. Our customer service team is ready to help you! Please contact them for further assistance.", "VALIDATION_MESSAGES": {"DATUM_INTERVAL": "The date range must contain exactly 2 dates", "LOWER_CASE_ERROR": "The password must contain a lowercase letter!", "MIN_LENGTH_ERROR": "The password must be at least 8 characters long!", "NOT_MATCH": "The entered passwords do not match!", "NUMBER_ERROR": "The password must contain a number!", "REQUIRED": "This field is mandatory!", "UPPER_CASE_ERROR": "The password must contain an uppercase letter!"}, "YES": "Yes", "CONFIRMATION": "Confirmation", "SAVE": "Save", "SUCCESSFUL_SAVE": "Successful save", "HOME": "Home", "PRIME_NG": {"MATCH_MODES": {"CONTAINS": "Contains", "STARTS_WITH": "Starts with", "ENDS_WITH": "Ends with", "EQUALS": "Equals"}}}, "COMPLETION_CERT_DETAILS": {"APPROVAL_STATUS": {"APPROVAL_STATUS": "Approval status", "ELO_CONTRACT_WORKFLOW_STATUS": "Completion certificate status"}, "COMPLETION_CERT_LINES": {"ACTIVITY_CODE": "Activity", "ACTIVITY_DESCRIPTION": "Activity description", "ADVANCE_AMOUNT": "Advance payment amount", "ADVANCE_PERCENTAGE": "Advance payment %", "AMOUNT": "Amount", "CONTRACT_DOCUMENT_NO": "Contract document number", "CONTRACT_LINE_NO": "Contract serial number", "CONTRACT_SUBTYPE": "Contract subtype", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON>", "GROSS_AMOUNT": "Gross amount", "QUANTITY": "Quantity", "REMAINING_AMOUNT": "Remaining amount", "UNIT_OF_MEASURE": "Unit of measure", "UNIT_PRICE": "Unit price", "VAT": "VAT %", "VAT_AMOUNT": "VAT amount", "VAT_GROUP": "VAT product accounting group"}, "DIVS": {"APPROVAL_STATUS": "Claim status", "ATTACHMENTS": "Attachments ", "COMMENT": "Comment", "COMPLETION_CERT_LINES": "Contract lines", "GENERAL": "General", "PARTIAL_DEADLINES": "Partial deadlines", "PROJECT": "Project", "RETENTIONS": "Retentions", "TIG_DETAILS_FOR_ENGINEER": "Completion certificate claim detail", "WITHDRAWN_ADVANCE": "Included advance"}, "END_TIG_REQUEST": {"APPROVED": "Approved", "CREATED": "In progress", "PRIMARY_SENT": "In progress", "SECONDARY_SENT": "In progress", "IN_PROGRESS": "In progress"}, "GENERAL": {"DELETE_DATE": "Delete date", "DOCUMENT_NUMBER": "Certificate number", "DOCUMENT_TYPE": "Document type", "JUDGE_DATE": "Assessment date", "JUDGE_EMAIL": "<PERSON><PERSON><PERSON>'s e-mail address", "JUDGE_NAME": "<PERSON><PERSON><PERSON>'s name", "JUDGE_TELEPHONE": "<PERSON><PERSON><PERSON>'s phone number", "PARTIAL_DELIVERY_NUMBER": "Serial number of partial completion", "PREPARATOR_EMAIL": "Technical scheduling engineer contracting e-mail address", "PREPARATOR_NAME": "Technical scheduling engineer contracting name", "PREPARATOR_TELEPHONE": "Technical scheduling engineer contracting phone number", "REJECTION_REASON": "Rejection reason", "SUBSTITUTE": "(substitute)", "VAT_TYPE": "VAT"}, "PARTIAL_DEADLINES": {"PARTIAL_DEADLINE_CONTENT": "Partial deadline content", "PARTIAL_DEADLINE_DATE": "Partial deadline date"}, "PROJECT": {"PROJECT_NO": "Project code", "PROJECT_SUBNAME": "Project subname"}, "RETENTIONS": {"PERCENTAGE": "Percentage", "RETENTION_TYPE": "Retention type"}, "TIG_DETAILS_FOR_ENGINEERS": {"ACTIVITY_DESCRIPTION": "Contract activity description", "ACTIVITY_DESCRIPTION_NA": "Contract activity description", "APPROVAL_STATUS": "Approval status", "APPROVE_DIALOG": "Successful completion certificate submission to BC", "APPROVE_DIALOG_RESEND": "Successful resend to BC", "COMPANY_NAME": "Supplier name", "CONTRACT_NUMBER": "Contract ID", "CALCULATED_AMOUNT": "Requested amount", "DAY_OF": "Day of", "DAYS_PASSED": "Days passed", "DECLINE_DESCRIPTION": "The completion certificate request will not be forwarded to BC, and it will be deleted from the MarketON portal.", "DECLINE_DIALOG": {"COMPLETION_CERT_DECLINED": "Completion certificate declined", "COMPLETION_CERT_DELETED": "Completion certificate deleted"}, "DECLINE_FLOATING_LABEL": "Reasoning (required field)", "DECLINE_TITLE": "Reason for rejecting the completion certificate", "DELETE_DESCRIPTION": "Description", "DELETE_TITLE": "Reason for deleting completion certificate", "DOCUMENT_NUMBER": "Certificate number", "DOCUMENT_TYPE": "Document type", "ENGINEER": "Technical scheduling engineer contracting", "END_CERT_APPROVE_INFO_MESSAGE": "Before sending the end-completion cert to BC, the end-completion cert conditions must be approved!", "PROJECT_NAME": "Project name", "PROJECT_NUMBER": "Project number", "TIG_AMOUNT": "Completion certificate amount", "TIG_CREATE_DATE": "Completion certifcate creation date", "TIG_DATE": "Completion certificate date", "VAT": "VAT"}, "TIGBUTTONS": {"END_TIG": "End completion certificate claim", "ENGINEER_APPROVE": "Approval", "ENGINEER_DECLINE": "Decline", "ENGINEER_DELETE": "Delete", "ENGINEER_RESEND": "Resend", "EXTRA_TIG": "Supplemental completion certificate claim", "PART_TIG": "Partial completion certificate claim"}, "WITHDRAWN_ADVANCE": {"ADVANCE_INVOICE_NO": "Advance payment row id", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON>", "DELIVERY_DATE": "Payment date of advance invoice", "DOCUMENT_NO": "Certificate number", "INCLUDED_PAYMENT_AMOUNT": "Included advance payment amount", "LINE_NO": "Number of supplier advance invoice lines", "PROJECT_NO": "Project code", "PROJECT_TASK_NO": "Project task number"}}, "COMPLETION_CERT_IN_PROGRESS_LIST": {"TIG_NUMBER": "Number of completion certificates: {{numberOfObjects}}"}, "COMPLETION_CERT_LIST": {"APPROVER": "Approver: {{profileName}}", "APPROVER_BASE": "Approver", "CALCULATED_AMOUNT": "Requested amount", "COMPANY_NAME": "Company name", "CONTRACT_NO": "Contract ID", "DAY_OF": "Day of ", "DATE_OF_ASSESSMENT": "Date of assessment", "DELETE_DATE": "Delete date", "EMPTY_COMPLETION_CERTS": "You do not have a completion certificate to be assessed.", "END_TIG": "End completion certificate claim", "ENGINEER": "Technical scheduling engineer contracting", "ENGINEER_TABS_HEADERS": {"ALL": "All", "ORDER_BY": "Order by", "RECEIVED_TIGS": "Received completion certificates", "SEARCH_BOX_TEXT": "Completion certfificate/Technical scheduling engineer contracting search", "SEARCH_BOX_TEXT_ENGINEER": "Search", "SENT_TO_BC": "<PERSON><PERSON> to BC", "SENT_TO_BC_FAILED": "Send to BC failed", "TITLE": "Completion certificates list", "WAITING_FOR_APPROVAL": "Waiting for approval"}, "EXTRA_TIG": "Supplemental completion certificate claim", "LIST_EMPTY": "List is empty", "NO_CERT_FOR_CONTRACT": "There is no completion certificate attached to this contract.", "SEARCH_HAS_NO_RESULT": "Search has no result.", "SUBSTITUTE": "(substitute)", "PART_TIG": "Partial completion certificate claim", "TECHNICAL_PREPERATOR": "Technical scheduling engineer contracting", "TECHNICAL_PREPERATOR_CAPITAL": "Technical scheduling engineer", "TIG_CREATE_DATE": "Completion certificate claim date", "TIG_DATE": "Completion certificate date", "VAT_TYPE": "VAT type"}, "CONTRACT_DETAILS_PAGE": {"CONTRACT_LINES": {"ACTIVITY_DESCRIPTION": "Activity description", "ADVANCE_AMOUNT": "Advance payment amount", "ADVANCE_PERCENTAGE": "Advance payment %", "AMOUNT": "Amount", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "GROSS_AMOUNT": "Gross amount", "PROJECT_TASK_NO": "Project task number", "QUANTITY": "Quantity", "UNIT_OF_MEASURE": "Unit of measure", "UNIT_PRICE": "Unit price", "VAT_GROUP": "VAT product accounting group", "VAT_PERCENTAGE": "VAT %"}, "CONTRACT_MAIN_DETAILS": {"ADVANCE_REPAYMENT": "Advance payment return", "CUSTOMER_SERVICE_PCT": "Customer service %", "END_INVOICE_PERCENT": "End invoice percent", "FINAL_DEADLINE": "Deadline", "FINALY_PAYMENT_DUE_DATE": "Finaly payment deadline", "MATERIAL_PROVIDING": "Material providing", "PARTIAL_PAYMENT_DUE_DATE": "Partial payment deadline", "SETTLEMENT_TYPE_DESCRIPTION": "Settlement type", "SITE_MANAGER": "FMV code", "SITE_MANAGER_EMAIL": "FMV e-mail", "SITE_MANAGER_NAME": "FMV name", "SITE_MANAGER_NUJ_NUMBER": "FMV name sign", "SITE_MANAGER_PHONE": "FMV telephone number", "SITE_MANAGER_REG_NUMBER": "FMV registration number", "SPOILAGE_PCT": "Spoilage %", "VAT_TYPE": "VAT type", "VENDOR_CONSTRUCTION_MANAGER": "Supplier construction manager", "VENDOR_CONSTRUCTION_MANAGER_EMAIL": "Supplier construction manager e-mail address", "VENDOR_CONSTRUCTION_MANAGER_NAME": "Supplier construction manager name", "VENDOR_CONSTRUCTION_MANAGER_PHONE": "Supplier construction manager phone number", "VENDOR_CONTACT_CODE": "Supplier representative", "VENDOR_CONTACT_EMAIL": "Supplier representative e-mail address", "VENDOR_CONTACT_NAME": "Supplier representative name", "VENDOR_CONTACT_PHONE": "Supplier representative phone number", "VENDOR_SAFETY_MANAGER": "Supplier safety manager", "VENDOR_SAFETY_MANAGER_EMAIL": "Supplier safety manager e-mail", "VENDOR_SAFETY_MANAGER_NAME": "Supplier safety manager name", "VENDOR_SAFETY_MANAGER_PHONE": "Supplier safety manager phone number", "WARRANTY_MONTHS": "Warranty months", "WORKPLACE_HANDOVER_DATE": "Workplace handover"}, "DIVS": {"ADVANCE": "Advance payment", "CONTRACT_LINES": "Contract lines", "CONTRACT_MAIN_DETAILS": "Contract main details", "GENERAL": "General", "PARTIAL_DEADLINES": "Partial deadlines", "PROJECT": "Project", "PROJECT_ADDRESS": "Project address", "RETENTIONS": "Retentions", "STATUS": "Claim status"}, "GENERAL": {"AMOUNT_SUM": "Amount", "ACTIVITY_DESCRIPTIONS": "Activity descriptions", "CONTRACT_DATE": "Contract date", "DOCUMENT_NO": "Certificate number", "DOCUMENT_SUBTYPE": "Document subtype", "PROJECT_NAME": "Project name", "SETTLEMENT_PERIOD": "Settlement period", "VERSION": "Version"}, "HEADERS": {"ADDITIONAL_WORK": "ADDITIONAL WORK CLAIM", "CONTINUE": "Continue completion certificate creation", "CONTRACT_DETAILS": "Contract details", "END_TIG": "End completion certificate claim", "END_TIG_ERROR_MESSAGE": "The end-completion cert condition list has not been set for the project, please contact your Market administrator.", "EXTRA_TIG": "Supplemental completion certificate claim", "EXTRA_WORK_TIG_ON_MAIN_CONTRACT_PLEASE": "Completion certificate for this extra work can only be started from the main contract!", "PART_TIG": "Partial completion certificate claim", "TIG_CLAIM": "Completion certificate claim", "TIGS": "Completion certificate list"}, "PARTIAL_DEADLINES": {"PARTIAL_DEADLINE_CONTENT": "Partial deadline content", "PARTIAL_DEADLINE_DATE": "Partial deadline date"}, "PROJECT": {"PROJECT_NAME": "Project name", "PROJECT_NO": "Project number", "PROJECT_PURCHASE_TYPE": "Project procurement type", "PROJECT_SUBNAME": "Project subname"}, "PROJECT_ADDRESS": {"CITY": "City", "PARCEL_NUM": "Parcel number", "PROJECT_ADDRESS": "Project address", "STREET": "Street name", "STREET_NUM": "House number", "ZIP": "Postal code"}, "RETENTIONS": {"PERCENTAGE": "Percentage", "RETENTION_TYPE": "Retentions type"}, "STATUS": {"APPROVAL_STATUS": "Approval status", "ELO_CONTRACT_WORKFLOW_STATUS": "Contract workflow status"}}, "CONTRACT_LIST": {"CONTRACT_DATE": "Contract date", "CURRENCY_TOTAL": "Amount", "DEADLINE": "Deadline date: ", "ORDER_BY": "Order by", "QUANTITY": "Count", "SEARCH": "Search", "VAT_TYPE": "VAT type: ", "NO_CONTRACT_FOR_THE_PROJECT": "No contract for the project."}, "CREATE_COMPLETION_CERTS": {"ATTACHMENTS": {"ADD_DOCUMENT": "Add attachment", "ATTACHMENTS_CAN_UPLOAD": "attachment can be uploaded", "DIALOG": {"CHOOSE_FOLDER": "Choose from folder", "DRAG_ATTACHMENT": "Drag attachment here or", "ERROR_FILE_LIMIT": "The maximum number of files that can be uploaded is {{limit}}", "ERROR_FILE_SIZE": "The file you want to upload is too large! Maximum size allowed is {{size}}", "ERROR_FILE_TYPE": "Invalid file type! Allowed file types are: {{types}}", "UPLOAD": "Upload", "UPLOAD_ATTACHMENT": "Upload attachment"}, "MAX": "Max", "UPLOADED_DOCUMENTS": "Uploaded documents"}, "COMMENT": {"INPUT_BOX_TIP": "Write comment  "}, "DIVS_HEADERS_BUTTONS": {"ATTACHMENTS": "Attachments ", "BUTTON_SEND": "Send", "BUTTON_UNDO": "Undo", "COMMENT": "Comment", "HEADER_NEW_TIG": "Submit new completion certificate", "TIG_DETAILS": "Completion certificate detail"}, "TIG_DETAILS": {"ACTIVITY_DESCRIPTION": "Activity description", "ADVANCE_AMOUNT": "Advance payment amount", "ADVANCE_PERCENTAGE": "Advance payment %", "AMOUNT": "Amount", "BACK_TO_LIST": "Back to completion certificate list", "COMPLETION_CERT_ABBR": "Completion certificate", "CONTRACT_DOCUMENT_NO": "Contract document number", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON>", "DROPDOWN_PLACEHOLDER": "Please choose", "END_CC_TITLE2": "At end completion cert the amount cannot be changed.", "ENGINEER": "Technical scheduling engineer contracting", "GROSS_AMOUNT": "Gross amount", "QUANTITY": "Quantity", "REMAINING_AMOUNT": "Remaining amount", "SUCCESSFUL_CREATE": "Successfull completion certificate creation", "TIG_DATE": "Completion certificate date", "TIG_SENT_DESCRIPTION": "Completion certificate was forwarded to the technical scheduling engineer contracting", "TITLE1": "Amounts to be confirmed per line", "TITLE2": "Please fill in the amount field per line!", "UNIT_OF_MEASURE": "Unit of measure", "UNIT_PRICE": "Unit price", "VAT": "VAT %", "VAT_AMOUNT": "VAT amount", "VAT_GROUP": "VAT product accounting group", "WARNING1": "The sum of the line sums must be greater than zero!", "WARNING2": "Our company accepts one invoice for one completion certificate", "SUM_CURRENCIES": "Summarized by currency", "SUMMA": "Summarized amount"}}, "CREATE_ADDITIONAL_WORK": {"ATTACHMENTS": {"ADD_DOCUMENT": "Add attachment", "ATTACHMENTS_CAN_UPLOAD": "attachment can be uploaded", "DIALOG": {"CHOOSE_FOLDER": "Choose from folder", "DRAG_ATTACHMENT": "Drag attachment here or", "ERROR_FILE_LIMIT": "The maximum number of files that can be uploaded is {{limit}}", "ERROR_FILE_SIZE": "The file you want to upload is too large! Maximum size allowed is {{size}}", "ERROR_FILE_TYPE": "Invalid file type! Allowed file types are: {{types}}", "UPLOAD": "Upload", "UPLOAD_ATTACHMENT": "Upload attachment"}, "MAX": "Max", "UPLOADED_DOCUMENTS": "Uploaded documents"}, "COMMENT": {"INPUT_BOX_TIP": "Write comment"}, "DIVS_HEADERS_BUTTONS": {"ADDITIONAL_WORK_DETAILS": "Additional work details", "ATTACHMENTS": "Attachments ", "BUTTON_SEND": "Send", "BUTTON_UNDO": "Undo", "COMMENT": "Comment", "HEADER_NEW_ADDITIONAL_WORK": "New additional work claim submitting"}, "ADDITIONAL_WORK_DETAILS": {"ACTIVITY_DESCRIPTION": "Activity description", "ADDITIONAL_WORK_DATE": "Additional work date", "ADDITIONAL_WORK_SENT_DESCRIPTION": "Additional work was forwarded to the technical scheduling engineer contracting", "AMOUNT": "Amount", "BACK_TO_LIST": "Back to on contract", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON>", "DROPDOWN_PLACEHOLDER": "Please select", "ENGINEER": "Technical scheduling engineer contracting", "IDENTIFIER": "Identifier", "SUCCESSFUL_CREATE": "Successful additional-work creation", "TITLE1": "Amounts to be confirmed per line", "TITLE2": "Please fill in the amount field per line!", "UNIT_PRICE": "Unit price", "WARNING1": "The sum of the line sums must be greater than zero!", "WARNING2": "Our company accepts one additional work for one contract"}, "BUTTON": {"CONTINUE": "Continue additional work creation", "INIT_NEW": "New additional work"}}, "DEPUTY_APPOINTMENT_PAGE": {"FORM": {"ACTUAL_SUBSTITUTIONS": "Actual substitutions for {{substitutedName}}", "BUTTON_SEND": "Send", "BUTTON_UNDO": "Cancel", "DEPUTY_NAME": "Substitute name", "MYSELF": "Me", "QUANTITY_SURVEYOR": "Technical scheduling engineer contracting", "QUANTITY_SURVEYOR_TO_SUBSTITUTE": "Technical scheduling engineer contracting to be substituted", "RADIOGROUP_LABEL": "Person you want to substitute", "SUBSTITUTION_PERIOD": "Date interval", "TITLE": "Substitute designation"}, "TITLE": "Substitution"}, "DEPUTY_LIST_PAGE": {"DEPUTY_NAME": "Substitute name", "SUBSTITUION_INTERVAL": "Substitution interval", "SUBSTITUTED_ENGINEER": "Substituted employee", "TITLE": "Active substitution"}, "I_AM_SUBSTITUTED_PAGE": {"NO_OWN_SUBSTITUTE": "There is no own substitute set"}, "I_SUBSTITUTE_PAGE": {"NOT_SUBSTITUTE": "You have not been set as a substitute"}, "ALL_SUBSTITUTIONS_PAGE": {"NO_SUBSTITUTIONS_SET": "There are no substitutions to display"}, "END_CERT_HANDLE": {"TITLE": "Management of end-completion certs conditions", "NEW_CONDITION_BUTTON": "New project end-completion cert condition setting"}, "END_CERT_DETAILS": {"APPROVED": "Approved", "APPROVERS_NAME": "Approvers name", "CONDITION": "Condition", "CONDITIONS_FOR_SUBMITTING": "Conditions for submitting end-completion cert claim", "E_MAIL": "E-mail address", "FAILED_APPROVE": "Error occured during approval", "FAILED_RESEND": "Error occured during email resend", "INFO_MESSAGE": "The end-completion cert claim can be submitted when all conditions are approved.", "POSITION": "Position", "SENT": "<PERSON><PERSON>", "TITLE": "End-completion cert condition details", "SUCCESSFUL_APPROVE": "Condition approved", "SUCCESSFUL_RESEND": "Approval email resent"}, "END_CERT_CONDITIONS": {"ErrorListZero": "Zero list of defects, no defect points", "WorkAreaClean": "Container returned, depot emptied, removed from work area", "KeysReturn": "Guard service certificate of key return", "DocumentationSubmitted": "Transfer documentation submitted for first review", "MaterialsHandover": "Repair material, spare material handed over", "TrainingCompleted": "Operation training has been completed", "ContractSubmitted": "Draft operating contract submitted", "LogClosed": "Workspace returned in e-log, log closed", "TestsCompleted": "Commissioning, test operation, operational test has been completed successfully", "ToolsReturned": "Machines, tools, racks have been returned", "DeductionsAgreed": "Additional work/deductions have been agreed upon"}, "END_CERT_CONDITIONS_PAGE": {"NO_END_CERT_CONDITION_IN_PROGRESS": "There are currently no end-completion cert conditions in progress.", "REQUEST_DATE": "Request date: {{date}}", "REQUEST_DATE_LABEL": "Request date", "PARTNER_PROFILE_NAME": "Partner's name", "STATUS": {"APPROVED": "Approved", "CREATED": "In progress"}}, "END_CERT_SETUP": {"ADD_CONDITION": "Add condition", "ADD_CONDITION_TITLE": "Addition of the condition for submission of the end-completion cert", "APPROVE_AS_ENGINEER": "Approve as engineer", "CONDITIONS_TO_PROJECT": "Setting conditions for the project", "CONDITION": "Conditon", "DELETE": "Delete", "MISSING_EMAIL_ADDRESS": "Missing email address", "PREPARED_CONDITIONS": "Conditions set for end-completion cert submission", "PRIMARY_APPROVER": "Primary approver", "PRIMARY_APPROVER_NAME": "Primary approver name", "RESEND": "Resend", "SECONDARY_APPROVER": "Secondary approver", "SECONDARY_APPROVER_NAME": "Secondary approver name", "SUCCESS_MOD": "You have successfully modified the end-completion cert conditions for the project", "TITLE": "End-completion cert conditions setting"}, "END_COMP_CERT_CONFIG_PARTNER": {"BUTTON_LABEL": "Starting end-completion cert request", "CANCEL": "Cancel", "INCORRECT_FLOW": "Incorrect end-completion cert flow"}, "ENUMERATIONS": {"APPROVALSTATUS": {"JÓVÁHAGYVA": "Approved", "VISSZAUTASÍTVA": "Declined", "TÖRÖLVE": "Deleted", "FOLYAMATBAN": "In progress", "NYITOTT": "Submitted", "BEKÜLDVE": "Received", "SZERKESZTÉS_ALATT": "Currently editing", "ELŐFELTÉTELEK JÓVÁHAGYÁSA FOLYAMATBAN": "Prerequisites approval in progress"}, "ELOWORKFLOWSTATUS": {"18 DOKUMENTUM KÉSZÍTÉS ALATT": "18 Document under preparation", "21 IGAZOLÁS ALATT": "21 Under verification", "30 JÓVÁHAGYÁS ALATT": "30 Under approval", "40 JÓVÁHAGYVA": "40 Approved", "60 ELKÜLDVE": "60 Sent", "80 ELUTASÍTVA": "80 Declined"}, "CONDITIONS": {"ENUM1": "Enum_EN"}}, "EXCEL_ADMIN_PAGE": {"ADMIN_LIST": "Admin list"}, "EXCEL_ADMIN_LIST_HEADER_NAMES": {"NAME": "Username", "BC_USER_ID": "BC ID", "COMPANY_NAME": "Company name", "EMAIL": "Current e-mail address", "LAST_LOGIN_DATE": "Last login date", "REGISTERED": "Registered?", "ACTIVE": "BC status"}, "EXCEL_ADVANCE_LINE_HEADER_NAMES": {"ADVANCE_INVOICE_NO": "Advance payment row id", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON>", "DELIVERY_DATE": "Payment date of advance invoice", "DOCUMENT_NO": "Certificate number", "INCLUDED_ADV_PAYMENT_AMOUNT": "Included advance payment amount", "LINE_NO": "Number of supplier advance invoice lines", "PROJECT_NO": "Project code", "PROJECT_TASK_NO": "Project task number"}, "EXCEL_AW_DETAILS": {"AMOUNT": "Amount", "APPROVAL_STATUS": "Approval status", "AW_DETAILS": "Additonal work details", "AW_LINES": "Additonal work lines", "BASE_CONTRACT_DOCUMENT_NO": "Certificate number of parent contract", "CLAIM_STATUS": "Claim status", "COMMENT": "Comment", "CREATOR_COMPANY_NAME": "Creator company name", "CREATED_DATE": "Created date", "CREATOR_STATUS": "Creator status", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON>", "DELETE_DATE": "Delete date", "DELETED_AT": "Assessment/deletion date", "DESCRIPTION": "Activity description", "DOCUMENT_NO": "Document number", "ELO_CONTRACT_WORKFLOW_STATUS": "Additonal work status", "JUDGE_EMAIL": "<PERSON><PERSON><PERSON>'s email address", "JUDGE_PROFILE_NAME": "<PERSON><PERSON><PERSON>'s name", "JUDGE_PHONE_NUMBER": "<PERSON><PERSON><PERSON>'s phone number", "LINE_NO": "Line number", "PARTIAL_DEADLINE": "Partial deadline", "PROJECT_NAME": "Project name", "PROJECT_NO": "Project number", "REJECTION_REASON": "Rejection reason", "RETENTIONS": "Retentions", "TABLENAME": "AdditonalWorkDetails"}, "EXCEL_AW_IN_PROGRESS_LIST": {"AW_LIST": "Additonal works", "ADDITIONAL_WORKS": "Additonal works", "ADDITIONAL_WORKS_TABLENAME": "Additonal works"}, "EXCEL_AW_IN_PROGRESS_LIST_HEADER_NAMES": {"ACTIVITY_DESCRIPTION": "Activity description", "APPROVAL_STATUS": "Approval status", "ASSIGNED_TO_PROFILE_NAME": "Technical scheduling engineer contracting name", "CREATED_DATE": "Additional works date", "CURRENCY_TOTAL": "Amount", "DELETED_AT": "Assessment/deletion date", "DOCUMENT_NO": "Certificate number", "ELO_WORKFLOW_STATUS": "Contract status", "JUDGE_PROFILE_NAME": "<PERSON><PERSON><PERSON>'s name", "PROJECT_NAME": "Project name"}, "EXCEL_COMPLETION_CERT_DETAIL_EXPORT": {"CERT_DETAILS": "Completion certificate details", "CERT_DETAILS_TABLE_NAME": "Completion certificate detail", "CERT_LINES": "Contract lines", "PARTIAL_DEADLINES": "Partial deadline", "RETENTIONS": "Retentions"}, "EXCEL_COMPLETION_CERT_DETAIL_HEADER_NAMES": {"APPROVAL_STATUS": "Approval status", "ASSIGNED_TO_EMAIL": "Technical scheduling engineer contracting e-mail address", "ASSIGNED_TO_PHONE_NUMBER": "Technical scheduling engineer contracting phone number", "ASSIGNED_TO_PROFILE_NAME": "Technical scheduling engineer contracting name", "COMPLETION_CERTIFICATE_DATE": "Completion certificate claim date", "CREATED_DATE": "Completion certificate creation date", "DELETED_AT": "Assessment/deletion date", "DOCUMENT_NO": "Certificate number", "DOCUMENT_TYPE": "Document type", "ELO_WORKFLOW_STATUS": "Eloworkflow status", "JUDGE_EMAIL": "<PERSON><PERSON><PERSON>'s email address", "JUDGE_PROFILE_NAME": "<PERSON><PERSON><PERSON>'s name", "JUDGE_PHONE_NUMBER": "<PERSON><PERSON><PERSON>'s phone number", "PARTIAL_COMPLETION_CERT_NO": "Partial certificate number", "PROJECT_NAME": "Project code", "PROJECT_NO": "Project code", "REJECTION_REASON": "Rejection reason", "VAT_TYPE": "VAT type"}, "EXCEL_COMPLETION_CERT_LINE_HEADER_NAMES": {"ACTIVITY_CODE": "Activity", "ACTIVITY_DESCRIPTION": "Activity description", "AMOUNT": "Amount", "CONTRACT_DOCUMENT_NO": "Amount", "CONTRACT_LINE_NO": "Contract serial number", "CONTRACT_SUB_TYPE": "Contract subtype", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON>", "GROSS_AMOUNT": "Gross amount", "QUANTITY": "Quantity", "REMAINING_AMOUNT": "Remaining amount", "UNIT_PRICE": "Unit price", "UNITOF_MEASURE": "Unit of measure", "VAT": "VAT %", "VAT_AMOUNT": "VAT amount", "VAT_PROD_POSTING_GROUP": "VAT product accounting group"}, "EXCEL_COMPLETION_CERT_LIST": {"COMPLETION_CERTS": "Completion certificates", "COMPLETION_CERTS_TABLENAME": "Completion certificates"}, "EXCEL_COMPLETION_CERT_LIST_HEADER_NAMES": {"APPROVAL_STATUS": "Approval status", "CALCULATED_AMOUNT": "Requested amount", "COMPLETION_CERTIFICATE_DATE": "Completion certificate claim date", "CREATED_DATE": "Certificate creation date", "DOCUMENT_NO": "Certificate number", "DELETED_AT": "Deleted at / Approved at", "ELO_WORKFLOW_STATUS": "Completion certificate status", "JUDGE_PROFILE_NAME": "<PERSON><PERSON><PERSON>'s name", "PROJECT_NAME": "Project code", "SUBSTITUTE_NAME": "Substitute name", "VAT_TYPE": "VAT type"}, "EXCEL_CONTRACT_DETAIL": {"CONTRACT_DETAIL": "Contract detail", "CONTRACT_LINES": "Contract lines", "PARTIAL_DEADLINE": "Partial deadline", "RETENTIONS": "Retentions", "TABLENAME": "Contract detail"}, "EXCEL_CONTRACT_DETAIL_HEADER_NAMES": {"ADVANCE_PAYMENT_RETURN": "Advance payment return", "APPROVAL_STATUS": "Approval status", "CONTRACT_DATE": "Contract date", "CONTRACT_VERSION": "Version", "CUSTOMER_SERVICE_PCT": "Customer service %", "DOCUMENT_NO": "Certificate number", "ELO_CONTRACT_WORKFLOW_STATUS": "Contract workflow status", "END_INVOICE_PERCENT": "End invoice percent", "FINAL_DEADLINE": "Deadline", "FINAL_PAYMENT_DUE_DATE": "Final payment deadline", "MATERIAL_EXCIPIENT_PROVIDING": "Material providing", "PARTIAL_PAYMENT_DUE_DATE": "Partial payment deadline", "PROJECT_ALIAS": "Project subname", "PROJECT_CITY": "City", "PROJECT_HOUSE_NUMBER": "House number", "PROJECT_NAME": "Project name", "PROJECT_NO": "Project number", "PROJECT_PARCEL_NUMBER": "Parcel number", "PROJECT_POST_CODE": "Postal code", "PROJECT_PURCHASE_TYPE": "Project procurement type", "PROJECT_STREET_SUFFIX_NAME": "Street name", "SETTLEMENT_TYPE_DESCRIPTION": "Settlement type", "SETTLEMENTPERIOD": "Settlement period", "SITE_MAN_E_MAIL": "FMV e-mail", "SITE_MAN_NUJ_NUMBER": "FMV name sign", "SITE_MAN_PHONE_NO": "FMV telephone number", "SITE_MAN_SELF_EMPL_REG_NUM": "FMV registration number", "SITE_MANAGER": "FMV code", "SITE_MANAGER_NAME": "FMV name", "SPOILAGE_PCT": "Spoilage %", "SUB_TYPE": "Document subtype", "VAT_TYPE": "VAT type", "VEND_CONST_MAN_E_MAIL": "Supplier construction manager e-mail address", "VEND_CONST_MAN_NAME": "Supplier construction manager name", "VEND_CONST_MAN_PHONE_NO": "Supplier construction manager phone number", "VEND_OCC_S_MAN_PHONE_NO": "Supplier safety manager phone number", "VEND_OCC_SAFETY_MAN_E_MAIL": "Supplier safety manager e-mail", "VEND_OCC_SAFETY_MAN_NAME": "Supplier safety manager name", "VEND_OCC_SAFETY_MANAGER": "Supplier safety manager", "VENDOR_CONSTURCTION_MANAGER": "Supplier construction manager", "VENDOR_CONTACT_CODE": "Supplier representative", "VENDOR_CONTACT_E_MAIL_ADDRESS": "Supplier representative e-mail address", "VENDOR_CONTACT_NAME": "Supplier representative name", "VENDOR_CONTACT_PHONE_NO": "Supplier representative phone number", "WARRANTY_MONTHS": "Warranty months", "WORKPLACE_HANDOVER_DATE": "Workplace handover "}, "EXCEL_CONTRACT_LINE_HEADER_NAMES": {"ACTIVITY_DESCRIPTION": "Activity description", "ADVANCE_PAYMENT_AMOUNT": "Advance payment amount", "ADVANCE_PAYMENT_PCT": "Advance payment %", "AMOUNT": "Amount", "CURRENCY_CODE": "<PERSON><PERSON><PERSON><PERSON> ", "GROSS_AMOUNT": "Gross amount", "LINE_NO": "Contract serial number", "PROJECT_TASK_NO": "Project task number", "QUANTITY": "Quantity", "UNIT_PRICE": "Unit price", "UNITOF_MEASURE_CODE": "Unit of measure", "VAT": "VAT %", "VAT_PROD_POSTING_GROUP": "VAT product accounting group"}, "EXCEL_CONTRACT_LIST": {"CONTRACT_LIST": "Contracts "}, "EXCEL_CONTRACT_LIST_HEADER_NAMES": {"ACTIVITY_DESCRIPTION": "Activity description", "APPROVAL_STATUS": "Approval status", "CREATED_DATE": "Contract date", "CURRENCY_TOTAL": "Amount", "DOCUMENT_NO": "Certificate number", "ELO_WORKFLOW_STATUS": "Contract status", "FINAL_DEADLINE": "Deadline", "PARENT_DOCUMENT_NO": "Certificate number of parent contract", "SETTLEMENT_TYPE_DESCRIPTION": "Settlement type", "SUB_TYPE": "Contract type", "VAT_TYPE": "VAT type"}, "EXCEL_PART_DEADLINE_HEADER_NAMES": {"PARTIAL_DEADLINE_CONTENT": "Partial deadline content", "PARTIAL_DEADLINE_DATE": "Partial deadline date"}, "EXCEL_PARTICIPANT_HEADER_NAMES": {"EMAIL": "e-mail", "NAME": "Name", "PHONE_NO": "Phone number ", "POSITION": "Position"}, "EXCEL_PROJECT_DETAIL": {"PARTICIPANTS": "Participants", "PROJECT_DETAIL": "Project detail"}, "EXCEL_PROJECT_DETAIL_HEADER_NAMES": {"BILLTO_CUSTOMER_NO": "Billing customer number", "BILLTO_NAME": "Name of customer / investor", "BREAM": "BREEAM ", "BUILD_PERMIT_STATUS_DESC": "Permit status", "CITY": "City", "CONTR_PLAN_STATUS_CODE": "Contracting plan status code", "CONTR_PLAN_STATUS_DESC": "Contracting plan status", "CUSTOMER_CONTRACTING_DATE": "Customer contracting date", "DESCRIPTION": "Project name", "DESCRIPTION2": "Project subname", "DGNB": "DGNB", "HOUSE_NUMBER": "House number", "INVESTOR_CONTRACTING_DATE": "Investor contracting date", "LEED": "LEED", "MAIN_CONTRACTOR_NAME": "Main contractor / General contractor", "OTHER": "Other", "PARCEL_NUMBER": "Parcel number", "POST_CODE": "Postal code", "PROJECT_CONTRACT_TYPE": "Project contract type", "PROJECT_E_MAIL": "Project e-mail address", "PROJECT_NATURE_DESC": "Project nature", "PROJECT_NO": "Project number", "PROJECT_PURCHASE_TYPE": "Project procurement type", "PROJECT_STATUS": "Project status", "STREET_SUFFIX": "Street", "STREET_SUFFIX_NAME": "Street name", "WELL": "WELL"}, "EXCEL_PROJECT_LIST": {"PROJECT_LIST": "Projects"}, "EXCEL_PROJECT_LIST_HEADER_NAMES": {"ADDRESS": "Project address", "DESCRIPTION": "Project name", "PROJECT_NO": "Project number", "PROJECT_STATUS": "Project status", "STREET": "Street name"}, "EXCEL_RETENTION_HEADER_NAMES": {"PERCENTAGE": "Percentage", "RETENTION_TYPE": "Retention type"}, "FEEDBACK_MESSAGE": {"BUTTON": {"BACK": "Back", "BACK_TO_COMPLETION_CERT": "Back to completion certificate", "BACK_TO_COMPLETION_CERT_LIST": "Back to completion certificate list", "BACK_TO_END_CERT_HANDLE": "Back to the management of end completion certs conditions"}, "DESCRIPTION": {"JUSTIFICATION_SENT": "Justification sent", "SUBSTITUTED_NAME": "Substituted name: {{substitutedName}}", "SUBSTITUTION_INTERVAL": "Substitution interval: {{fromDate}} - {{toDate}}", "SUBSTITUTOR_NAME": "Substitutor name: {{substitutorName}}"}, "FAILED_DELETE": "Delete failed", "FAILED_SUBSTITUTION_CREATE": "Failed to create substitution!", "SEND_FAILED": "Send failed", "SEND_FAILED_TO_BC": "Submitting the completion certificate to BC was unsuccessful, please check the contract details in BC.", "SUCCESS": "Success", "SUCCESSFUL_ACTION": "Successful operation!", "SUCCESSFUL_DELETE": "Deleted successfully", "SUCCESSFUL_EMAIL_CHANGE": "Your email address has been successfully changed", "SUCCESSFUL_EMAIL_VALIDATION": "Successful email validation", "SUCCESSFUL_PASSWORD_CHANGE": "Your password has been successfully changed.", "SUCCESSFUL_SUBSTITUTION_CREATE": "Successful substitution creation!"}, "FORGOT_PASSWORD": {"BACK": "Back to the login page", "BACK_ARROW": "Back arrow", "EMAIL_SENT": {"TEXT": "Please also check your spam folder if you have not received the email within a few minutes.", "TITLE": " password reset e-mail has been sent, if the given address is registered in our system!"}, "FORM": {"EMAIL_PLACEHOLDER": "E-mail address", "SUBMIT": "Send password reset", "TEXT": "Enter your email address to which we will send instructions for resetting your password.", "TITLE": "Reset forgotten password"}}, "HEADER": {"COMPLETION_CERTS": "Completion certificates", "DAMAGE_SUBMIT": "Damage report", "ENDCERT_HANDLE": "Management of end completion certs conditions", "INPROGRESS_ENDCERT_CONDITIONS": "Current end completion certs conditions", "LANGUAGE": "Language", "LANGUAGE_CHANGE": "Language changed to {{language}}", "LOG_OUT": "Log out", "NOTIFICATIONS": "Notifications", "PROFILE_SETTINGS": "Profile settings", "SUBSTITUTION": "Substitution", "VIDEO": "Tutorial video"}, "HOME": {"ACTIVE_CERTS": "Completion certificates in progress", "ACTIVE_PROJECTS": "Active projects", "ADDITIONAL_WORKS_IN_PROGRESS": "Additional works in progress", "BALANCE": "Balance", "COMING_SOON": "Coming soon", "DELETED_ADDITIONAL_WORKS": "Additional work claims recycle bin", "DELETED_COMPLETION_CERT_LIST": "Completion cert claims recycle bin", "LOADING": "Loading", "NO_ADDITIONAL_WORKS_IN_PROGRESS": "No additional works in progress", "NO_DELETED_ADDITIONAL_WORK": "Additional work claim recycle bin is empty", "NO_DELETED_COMPLETION_CERT": "Completion cert claim recycle bin is empty", "NO_IN_PROGRESS_COMPLETION_CERT": "No in progress completion certificate", "NO_PROJECTS": "No projects", "OTHER_PROJECTS": "Market Group projects", "PHONEBOOK": "Phonebook"}, "LOGIN_PAGE": {"FORM": {"CURRENT_PASSWORD": "Password", "FORGOTTEN_PASSWORD": "Forgotten password?"}, "PASSWORD_CHANGE_SUCCESS": "Your password has been successfully changed.", "TITLE": "MarketON registration"}, "NOTIFICATION_PAGE": {"ADDITIONAL_WORKS": "Additional work", "BACK": "Back", "COMPLETION_CERTS": "Completion cert", "DECLINED": "Declined", "SAVE": "Save", "SUCCESSFUL_SAVE": "Successful save", "TITLE": "Notifications", "WHICH_STATUS": "Please select statuses for which you would like to receive email notifications"}, "PAGE_NOT_FOUND": {"MESSAGE": "If you can't find what you're looking for, go back to the main page or browse using the menu items in the header.", "PAGE_NOT_FOUND": "Page not found"}, "PASSWORD_RESET_PAGE": {"MODIFY_PASSWORD": "Change password", "TEXT": "Enter new password", "TITLE": "Enter new password"}, "PRIMARY_NAVIGATION": {"COMPLETION_CERT_LIST": "Completion certificate list", "PROFORMA_INVOICE": "Invoice", "REQUEST_LIST": "Claim list", "SUBMIT_REQUEST": "Submit request", "UPLOAD_DOCUMENT": "Upload invoice", "UPLOAD_DOCUMENT_LIST": "Invoice list", "SECRETARY_UPLOAD_DOCUMENT_LIST": "Secretary uploads", "CALENDAR_HANDLING": "Calendar handling", "PARTNER_DOCUMENTS": "Partner uploads"}, "PROFILE_PAGE": {"E_MAIL_SETTING_TAB": {"CURRENT_EMAIL_ADDRESS": "Current e-mail address", "CURRENT_PASSWORD": "Enter current password", "ERROR": {"EMAIL_FORMAT": "Wrong e-mail format", "EMAIL_MATCH": "The e-mails do not match", "SOMETHING_WENT_WRONG": "Something went wrong: {{message}}"}, "FEEDBACK": {"SUCCESS": "To finalize the email address, we have sent an email to the new email address you provided."}, "NEW_EMAIL": "New e-mail", "NEW_EMAIL_CONFIRM": "Confirm new e-mail", "SAVE_CHANGES": "Save changes", "TOOLTIP": "After entering the new e-mail address, the old one will be overwritten in the MarketON database. Subsequent e-mails sent by MarketON will be forwarded to the newly entered e-mail address. You must also use this email address to log in."}, "LANGUAGE_TAB": {"LANGUAGE_CHANGE": "Select language", "LANGUAGE_SELECT": "Select language", "SAVE_CHANGES": "Save changes"}, "PARTNER_DATA_TAB": {"CARTONS": {"CHOOSE_CARTON": "Choose a personal card", "EMAIL": "E-mail", "FAX": "FAX number", "FMV": "FMV number", "MKIK": "MKIK number", "MOBILE_NUMBER": "Mobile number", "NUJ": "NÜJ number", "PHONE_NUMBER": "Phone number ", "TYPE": "Availability type", "WEBSITE": "Website"}, "COMMUNICATION": {"ADDRESS": "Address", "ADDRESS2": "Address2", "CITY": "City", "COMMUNICATION": "Communication", "COUNTRY_REGION": "Country/Region", "EMAIL": "E-mail", "FAX": "FAX number", "MOBILE_NUMBER": "Mobile number", "PHONE_NUMBER": "Phone number ", "WEBSITE": "Website", "ZIP": "Postal code"}, "GENERAL": {"NAME": "Name", "PARTNER_ID": "Partner id"}, "INVOICE": {"COMMUNITY_TAX_NUMBER": "Community tax number", "COMPANY_REG_NUMBER": "Company registration number", "INVOICE": "Invoicing", "PRIVATE_CONTRACTOR_ID": "Private contractor registration number", "TAX_NUMBER": "Tax number"}, "TITLES": {"CARTONS": "Company personal cards", "COMMUNICATION": "Communication", "GENERAL": "General", "INVOICE": "Invoicing"}}, "PASSWORD_TAB": {"CONFIRM_PASSWORD": "Confirm new password", "CURRENT_PASSWORD": "Current password", "NEW_PASSWORD": "New password", "PASSWORD_CHANGE": "Change password", "SAVE_PASSWORD": "Save password"}, "PROFILE_MENU_HEADER": {"EMAIL_SETTINGS": "E-mail settings", "LANGUAGE": "Set language", "PARTNER_DATA": "Partner data", "PASSWORD": "Password"}}, "PROJECT_DETAILS_PAGE": {"DETAILS": {"BILL_COSTUMER_NO": "Billing customer number", "BILL_CUSTOMER_NAME": "Name of customer / investor", "BREEAM": "BREEAM ", "CITY": "City", "CONTRACT_DATE": "Investor contracting date", "CUSTOMER_CONTRACTING_DATE": " Customer contracting date", "DGNB": "DGNB", "LEED": "LEED", "MAIN_CONTRACTOR": "Main contractor / General contractor", "OTHER": "Other", "PARCEL_NUM": "Parcel number", "PERMIT_STATUS": "Permit status", "PLAN_STATUS": "Contracting plan status", "PROJECT_ADDRESS": "Project address", "PROJECT_CONTRACT_TYPE": "Project contract type", "PROJECT_EMAIL": "Project e-mail address", "PROJECT_NAME": "Project name", "PROJECT_NATURE": "Project nature", "PROJECT_NO": "Project number", "PROJECT_NOT_FOUND": "Project not found", "PROJECT_PURCHASE_TYPE": "Project procurement type", "PROJECT_STATUS": "Project status", "PROJECT_SUBNAME": "Project subname", "STREET": "Street name", "STREET_NUM": "House number", "WELL": "WELL", "ZIP": "Postal code"}, "DIVS": {"DETAILS": "Project details", "PARTICIPANTS": "Participants"}, "PARTICIPANTS": {"EMAIL": "E-mail", "NAME": "Name", "PHONE_NUMBER": "Phone number ", "POSITION": "Position"}, "PROJECT_DETAILS_MENUS": {"CONTRACT_LIST": "Contract list ({{count}})", "PROJECT_DETAILS": "Project details"}}, "PROJECT_LIST_PAGE": {"EMPTY_PROJECTS": "You do not have a project", "ORDER_BY": "Order by", "PROJECT_ADDRESS": "Projekt address", "PROJECT_DESCRIPTION": "Project description", "PROJECT_NO": "Project number", "PROJECT_NUMBER": "Number of projects: {{numberOfObjects}}", "SEARCH": "Search", "TITLE": "Project list"}, "REGISTRATION_PAGE": {"FORM": {"CURRENT_PASSWORD": "Password", "FORGOTTEN_PASSWORD": "Forgotten password?"}, "TEXT": "You can change your e-mail address after logging in.", "TITLE": "MarketON registration"}, "REGISTRATION_SUCCESS_PAGE": {"TEXT": "We will redirect you to the MarketON portal within a few seconds.", "TITLE": "Registration successful"}, "SHOW_MORE_TOGGLE": {"HIDEALL": "Hide all ", "SHOWALL": "Show all"}, "SUB_HEADER_TITLE": {"ADMIN_USER_LIST": "User list ", "ARRIVED_COMPLETION_CERTS": "Received completion certificates", "ARRIVED_ADDITIONAL_WORKS": "Received additonal works", "DELETED_ADDITIONAL_WORK_DETAIL": "Deleted additonal work claim", "DELETED_ADDITIONAL_WORK_LIST": "Additional work claims recycle bin", "DELETED_COMPLETION_CERT_LIST": "Completion cert claims recycle bin", "ENDCERT_HANDLE": "Handling of end-completion cert", "IN_PROGRESS_COMPLETION_CERT_LIST": "List of ongoing completion certificates", "IN_PROGRESS_ADDITIONAL_WORK_DETAILS": "Ongoing additional work details", "IN_PROGRESS_ADDITIONAL_WORK_LIST": "Ongoing additional works", "PROJECTS_WITHOUT_ENDCERT_CONDITIONS": "Projects without end-completion cert conditions", "SUBSTITUTION": "Substitution"}, "SUBSTITUTION_TAB_LABEL": {"ALL_SUBSTITUTION": "All substitution", "I_AM_SUBSTITUTED": "My substitutes", "I_SUBSTITUTE": "Set as substitute", "SUBSTITUTION": "Substitution"}, "TECHNICAL_PREPARATION_LIST": {"TIG_CLAIMS": "Completion cert claims", "ADDITIONAL_WORK_CLAIMS": "Additonal work claims", "REQUEST_CONSIDERATION": "Received Requests", "NO_ATTACHMENT": "There are no attached uploads for this request"}, "UPLOAD_DOCUMENT_PAGE": {"TITLE": "Upload Invoice", "SUBMITTED_TITLE": "Uploaded invoice", "DETAILS": {"PERCASE_TITLE": "Percase upload details", "BATCH_TITLE": "Batch upload details", "MANUAL_TITLE": "Manual upload details", "TITLE": "Upload invoice details", "PROJECT_SELECTION": "Select project", "CONTRACT_SELECTION": "Select contract", "PARTNER_SELECTION": "Search partner", "INVOICE_TYPE": "Invoice Type", "INVOICE_SELECT": {"INVOICE_TYPE_1": "TIG Invoice", "INVOICE_TYPE_2": "Non-TIG Invoice", "INVOICE_TYPE_3": "Correction Submission", "INVOICE_TYPE_4": "Advance Invoice Submission"}, "ARRIVAL_DATE": "Arrival date"}, "MENUS": {"DOCUMENT_UPLOADS": "Document uploads list ({{count}})", "DRAFT_DOCUMENT_UPLOADS": "Draft document uploads ({{count}})"}, "ATTACHMENTS": {"TITLE": "Attachments", "MAX": "Maximum number of attachments that can be uploaded: {{max}}", "MAX_FILE_SIZE": "Maximum file size that can be uploaded: {{maxFileSize}} MB", "UPLOAD": "Add Attachments", "DOCUMENT_TYPE": "Document Type", "ADD_DOCUMENT": "Add Attachment", "MERGE_WARNING_HTML": "<strong>Important note!</strong><br/>For fast and smooth processing, please upload the invoice and its attachments to our system as <strong>separate files</strong>.<br/>If the documents arrive in one file, they <strong>will not be processed</strong> and the invoice will be returned.", "DOCUMENT_TYPE_SELECT": {"DOCUMENT_TYPE_1": "Invoice", "DOCUMENT_TYPE_2": "Certificate of Completion", "DOCUMENT_TYPE_3": "Delivery Note", "DOCUMENT_TYPE_4": "Waybill", "DOCUMENT_TYPE_5": "Order", "DOCUMENT_TYPE_6": "Contract", "DOCUMENT_TYPE_7": "Correction Invoice", "DOCUMENT_TYPE_8": "Advance Invoice / Final Invoice", "DOCUMENT_TYPE_9": "Receipt Confirmation", "DOCUMENT_TYPE_10": "Other"}, "UPLOAD_FILE": "Upload File", "TIG_INVOICE_REQUIREMENTS": "For the TIG invoice type, at least one invoice and one certificate of completion are required", "SP_NUMBER": "Ref. num.: {{spNumber}}"}, "COMMENTS": {"TITLE": "Comment", "PLACEHOLDER": "Enter your comments here"}, "BUTTONS": {"SEND": "Submit", "DISCARD": "Discard", "SAVE_WITHOUT_SENDING": "Save without submitting"}, "VALIDATION": {"NO_ATTACHMENTS": "No attachments", "MISSING_INVOICE": "Invoice is missing", "MISSING_CERTIFICATE": "Certificate of completion is missing", "MISSING_BOTH_REQUIRED_DOCUMENTS": "Invoice and certificate of completion are missing"}, "CONFIRMATION": {"DISCARD_CONFIRMATION": "Are you sure you want to discard the invoice?", "SAVE_CONFIRMATION_MESSAGE": "Are you sure you want to save the invoice?", "SAVE_CONFIRMATION_DESCRIPTION": "The submitted data will be saved as a draft and does not constitute an invoice submission.", "SUBMIT_CONFIRMATION_MESSAGE": "Are you sure you want to submit the invoice?"}, "FEEDBACK": {"SAVE_TITLE": "Save Invoice", "SAVE_DESCRIPTION": "The invoice has been successfully saved", "SAVE_BUTTON": "Upload New Invoice", "SAVE_FAILED_TITLE": "Invoice save failed", "UPDATE_TITLE": "Update Invoice", "UPDATE_DESCRIPTION": "The invoice has been successfully updated", "UPDATE_BUTTON": "Back", "UPDATE_FAILED_TITLE": "Invoice update failed", "DISCARD_TITLE": "Discard Invoice", "DISCARD_DESCRIPTION": "The invoice has been successfully discarded.", "DISCARD_BUTTON": "Back to the invoice list", "DISCARD_FAILED_TITLE": "Invoice deletion failed", "SUBMIT_FAILED_TITLE": "Invoice submission failed", "SUBMIT_TITLE": "Submit Invoice", "SUBMIT_DESCRIPTION": "The invoice has been successfully submitted", "SUBMIT_BUTTON": "Upload New Invoice", "BUTTON_2": "Back to the invoice list"}, "SECRETARY_INVOICE": {"NO_ATTACHMENTS": "Upload at least one attachment", "UPLOAD_IDENTIFIER": "Upload identifier: {{contextId}}"}, "PROCESSING_ERROR": "<strong>Dear Partner!</strong><br/>The invoice submission on the MarketON interface has failed. Please send your invoice and attachments to the <a href='mailto:<EMAIL>'><EMAIL></a> email address!<br/>Thank you!"}, "SECRETARY_PARTNER_DOCUMENTS": {"TITLE": "Partner Documents", "COLUMNS": {"SP_IDENTIFIERS": "SP Identifiers", "PARTNER": "Partner", "PARTNER_CONTACT_NO": "Partner Contact No", "UPLOADER": "Uploader", "STATUS": "Status", "CREATED_DATE": "Created Date", "DATE_OF_ARRIVAL": "Date of Arrival"}, "FILTERS": {"SEARCH_PLACEHOLDER": "Search documents...", "STATUS_PLACEHOLDER": "Any"}, "BUTTONS": {"CLEAR": "Clear", "REFRESH": "Refresh"}, "STATUS": {"SUCCESS": "Success", "PROCESSING": "Processing", "FAILED": "Failed"}, "MODE": {"DRAFT": "Draft uploads", "SUBMITTED": "Submitted uploads"}, "NO_DATA": "No documents found.", "TABLE_INFO": "Showing {first} to {last} of {totalRecords} entries", "UPLOAD_MODE": {"PER_CASE": "Per case upload", "BATCH": "Batch upload", "MANUAL": "Manual upload"}, "DETAILS": {"TITLE": "Invoice Upload Details", "DIALOG_TITLE": "Partner Upload", "PROJECT": "Project", "CONTRACT": "Contract", "PARTNER": "Partner", "INVOICE_TYPE": "Invoice Type", "CONTACT_NO": "Partner Contact No", "INVOICE_SELECT": {"INVOICE_TYPE_1": "TIG Invoice", "INVOICE_TYPE_2": "Non-TIG Invoice", "INVOICE_TYPE_3": "Correction Submission", "INVOICE_TYPE_4": "Advance Invoice Submission"}, "ARRIVAL_DATE": "Arrival date", "DRAFT": "Draft", "SUBMITTED": "Submitted", "DOCUMENT_STATUS": "Upload status:", "SP_NUMBER": "Ref. num.:"}}, "DOCUMENT_LIST_PAGE": {"TITLE": "Uploads list", "NO_DOCUMENTS": "No documents found.", "SUBMITTED_INVOICES_TITLE": "Successful uploads list", "DRAFT_INVOICES_TITLE": "Unsubmitted uploads list", "BADGE_STATUS_DRAFT": "Saved", "BADGE_STATUS_SUBMITTED": "Submitted", "CALENDAR_PLACEHOLDER": "Upload period", "UPLOADER": "Uploader", "UPLOAD_TIME": "Upload time", "UPLOADED_FILES": "Uploaded files: {{count}}"}, "CALENDAR_HANDLING_PAGE": {"TITLE": "Calendar handling", "SUB_HEADER_TITLE": "Calendar exception setting", "WORKDAY_SETTINGS": "Workday settings", "UNUSUAL_WORKDAYS": "Unusual workdays", "DAY": "Day", "STATUS": "Day type", "START_TIME": "Start time", "END_TIME": "End time", "WORKDAY": "Workday", "DAY_OFF": "Day off", "MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday", "SUNDAY": "Sunday", "COMMENT": "Comment", "DATE": "Date", "DELETE": "Delete", "NO_UNUSUAL_WORKDAYS": "No unusual workdays"}}
import { NgModule } from '@angular/core';
import { AccordionModule } from 'primeng/accordion';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { DynamicDialogModule } from 'primeng/dynamicdialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MessageModule } from 'primeng/message';
import { MessagesModule } from 'primeng/messages';
import { OrderListModule } from 'primeng/orderlist';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { PaginatorModule } from 'primeng/paginator';
import { PanelModule } from 'primeng/panel';
import { PasswordModule } from 'primeng/password';
import { ProgressBarModule } from 'primeng/progressbar';
import { RadioButtonModule } from 'primeng/radiobutton';
import { RippleModule } from 'primeng/ripple';
import { ScrollTopModule } from 'primeng/scrolltop';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabMenuModule } from 'primeng/tabmenu';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TimelineModule } from 'primeng/timeline';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { TreeTableModule } from 'primeng/treetable';

const modules = [
    AccordionModule,
    ButtonModule,
    CalendarModule,
    CheckboxModule,
    DynamicDialogModule,
    InputTextareaModule,
    InputTextModule,
    PaginatorModule,
    PasswordModule,
    ProgressBarModule,
    RadioButtonModule,
    RippleModule,
    SidebarModule,
    TableModule,
    TabMenuModule,
    ToastModule,
    MessagesModule,
    MessageModule,
    TooltipModule,
    DropdownModule,
    InputNumberModule,
    TimelineModule,
    ConfirmDialogModule,
    InputSwitchModule,
    AutoCompleteModule,
    TreeTableModule,
    CardModule,
    PanelModule,
    TabViewModule,
    DialogModule,
    BreadcrumbModule,
    TagModule,
    ScrollTopModule,
    SelectButtonModule,
    OverlayPanelModule,
    OrderListModule,
];

@NgModule({
    imports: [modules],
    exports: [modules],
})
export class PrimeNGModule {}

import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ContractService, FileInfo, Project } from '@api-clients/contracts';
import {
    AccountTypeEnum,
    AttachmentDocumentTypeDto,
    AttachmentService as DocumentAttachmentService,
    CreateDocumentUploadDraftRequest,
    DocumentType,
    DocumentUploadDto,
    PartnerDocumentService,
    ProcessingStatusEnum,
    UpdateDocumentUploadDraftRequest,
    UploadStatusEnum,
} from '@api-clients/document';
import { Status } from '@core/enums/async-status.enum';
import { ProjectStateStatus } from '@core/enums/project-state-status.enum';
import { IConfirmationDialogData } from '@core/interfaces/confirmation-dialog.interface';
import { IPartnerDocumentAttachment } from '@core/interfaces/document-attachment.interface';
import { IDropdownItem } from '@core/interfaces/dropdown-item.interface';
import { IProblemDetails } from '@core/interfaces/problem-details.interface';
import { AuthService } from '@core/services/auth.service';
import { BreadcrumbService } from '@core/services/breadcrumb.service';
import { DocumentTypesService } from '@core/services/document-types.service';
import * as ProjectSelectors from '@core/stores/project.selectors';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { BaseManualDocumentUploadComponent } from '@shared/components/base-manual-document-upload.component';
import { ConfirmationDialogComponent } from '@shared/components/confirmation-dialog/confirmation-dialog.component';
import { DialogService } from 'primeng/dynamicdialog';
import {
    BehaviorSubject,
    catchError,
    combineLatest,
    EMPTY,
    finalize,
    map,
    Observable,
    of,
    switchMap,
    take,
    takeUntil,
} from 'rxjs';
import { v6 as uuidv6 } from 'uuid';

@Component({
    selector: 'app-document',
    styles: [
        `
            .parent {
                display: grid;
                grid-template-columns: auto auto minmax(280px, 1fr) auto auto;
                grid-template-rows: repeat(2, auto);
                row-gap: 5px;
                column-gap: 10px;
                align-items: center;

                @media (max-width: 62em) {
                    grid-template-columns: auto auto minmax(280px, 1fr) auto;
                    grid-template-rows: repeat(3, auto);
                }
            }

            .order-number {
                grid-column: 1;
                grid-row: 1;
            }

            .trash-button {
                grid-column: 2;
                grid-row: 1;
            }

            .document-type {
                grid-column: 3;
                grid-row: 1;
            }

            .file-preview {
                grid-column: 4;
                grid-row: 1;

                @media (max-width: 62em) {
                    grid-column: 1 / 4;
                    grid-row: 3;
                }
            }

            .sp-identifier {
                grid-column: 3;
                grid-row: 2;
            }
        `,
    ],
    template: `
        <sub-head [isSearchVisible]="false"> </sub-head>

        <market-progress-bar
            *ngIf="(projectStatus$ | ngrxPush) === ProjectStateStatus.loading || isLoading$ | ngrxPush"
        ></market-progress-bar>
        <base-layout class="fadein animation-duration-250">
            <div class="mt-5"></div>
            <ng-container *ngIf="(projectStatus$ | ngrxPush) === ProjectStateStatus.success && !errorMessage">
                <p-accordion [multiple]="true">
                    <p-accordionTab header="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.TITLE' | translate }}" [selected]="true">
                        <div class="flex flex-column gap-4 fadein animation-duration-250">
                            <div class="lg:col-5 p-fluid p-0 flex gap-4 flex-column">
                                <span class="p-float-label">
                                    <p-dropdown
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.PROJECT_SELECTION' | translate }}"
                                        placeholder="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.PROJECT_SELECTION' | translate }}"
                                        [options]="projectsDropdownItems$ | ngrxPush"
                                        [ngModel]="selectedProjectNo.value"
                                        (ngModelChange)="onSelectedProjectNoChange($event)"
                                        [disabled]="isSubmitted"
                                        [showClear]="true"
                                        [filter]="true"
                                    ></p-dropdown>
                                    <label for="projectSelection">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.PROJECT_SELECTION' | translate
                                    }}</label>
                                </span>
                                <span class="p-float-label">
                                    <p-dropdown
                                        [options]="contractsDropdownItems$ | ngrxPush"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.CONTRACT_SELECTION' | translate }}"
                                        placeholder="{{
                                            'UPLOAD_DOCUMENT_PAGE.DETAILS.CONTRACT_SELECTION' | translate
                                        }}"
                                        [ngModel]="selectedContractNo.value"
                                        (ngModelChange)="selectedContractNo.next($event)"
                                        [disabled]="isSubmitted"
                                        [showClear]="true"
                                        [filter]="true"
                                    ></p-dropdown>
                                    <label for="contractSelection">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.CONTRACT_SELECTION' | translate
                                    }}</label>
                                </span>
                                <span class="p-float-label">
                                    <p-dropdown
                                        [options]="invoiceTypes"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.INVOICE_TYPE' | translate }}"
                                        placeholder="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.INVOICE_TYPE' | translate }}"
                                        [ngModel]="selectedInvoiceType"
                                        (ngModelChange)="onInvoiceTypeChange($event)"
                                        [disabled]="isSubmitted"
                                    ></p-dropdown>
                                    <label for="invoiceType" class="required-field">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.INVOICE_TYPE' | translate
                                    }}</label>
                                </span>
                            </div>
                        </div>
                    </p-accordionTab>
                    <p-accordionTab
                        header="{{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.TITLE' | translate }}"
                        [selected]="true"
                    >
                        <div
                            class="flex flex-column gap-4 fadein animation-duration-250"
                            *ngIf="documentAttachments$ | async as documentAttachments"
                        >
                            <div>
                                <p class="g-body-1 text-gray-600">
                                    {{
                                        'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.MAX'
                                            | translate : { max: this.fileCountLimit }
                                    }}
                                </p>
                                <p class="g-body-1 text-gray-600">
                                    {{
                                        'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.MAX_FILE_SIZE'
                                            | translate : { maxFileSize: this.fileSizeLimitMb }
                                    }}
                                </p>
                            </div>

                            <p-message
                                class="fadein"
                                *ngIf="existingDocument?.processingStatus === ProcessingStatusEnum.Failed"
                                severity="error"
                                [escape]="false"
                                [text]="'UPLOAD_DOCUMENT_PAGE.PROCESSING_ERROR' | translate"
                            ></p-message>

                            <p-message
                                class="fadein"
                                *ngIf="selectedInvoiceType === AccountTypeEnum.TigInvoice && !isSubmitted"
                                severity="info"
                                [escape]="false"
                                [text]="'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.TIG_INVOICE_REQUIREMENTS' | translate"
                            ></p-message>

                            <p-message
                                class="fadein"
                                severity="info"
                                [escape]="false"
                                [text]="'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.MERGE_WARNING_HTML' | translate"
                                *ngIf="documentAttachments[0]?.file == null"
                            >
                            </p-message>

                            <div
                                *ngFor="let attachment of documentAttachments; let i = index"
                                class="flex flex-row gap-2 align-items-center"
                            >
                                <div class="parent">
                                    <div class="order-number">
                                        <p class="g-body-1 text-gray-600">
                                            {{ i + 1 + '.' }}
                                        </p>
                                    </div>
                                    <div class="trash-button">
                                        <button
                                            type="button"
                                            pButton
                                            icon="pi pi-trash"
                                            class="p-button-rounded p-button-text p-button-danger"
                                            (click)="removeAttachment(i)"
                                            [disabled]="isSubmitted"
                                        ></button>
                                    </div>
                                    <div class="document-type">
                                        <span class="p-float-label">
                                            <p-dropdown
                                                [options]="attachment.availableDocumentTypes || documentTypes"
                                                optionLabel="label"
                                                optionValue="value"
                                                class="p-fluid"
                                                [(ngModel)]="attachment.documentType"
                                                placeholder="{{
                                                    'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.DOCUMENT_TYPE' | translate
                                                }}"
                                                (onChange)="onDocumentTypeChange(i)"
                                                [disabled]="isSubmitted"
                                            ></p-dropdown>

                                            <label for="documentType" class="required-field">{{
                                                'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.DOCUMENT_TYPE' | translate
                                            }}</label>
                                        </span>
                                    </div>
                                    <div class="upload-button">
                                        <button
                                            *ngIf="!attachment.file && attachment.documentType && !isSubmitted"
                                            type="button"
                                            pButton
                                            icon="pi pi-upload"
                                            class="p-button-rounded p-button-text"
                                            (click)="openFileuploadModal(i)"
                                            pTooltip="{{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.UPLOAD_FILE' | translate }}"
                                        ></button>
                                    </div>
                                    <div class="file-preview">
                                        <div *ngIf="attachment.file" class="flex align-items-center gap-2 mt-2 lg:mt-0">
                                            <attachment-tile
                                                [isDeleteMode]="!isEditMode"
                                                [attachmentName]="attachment.file.fileName"
                                                [iconVisible]="isEditMode && !isSubmitted"
                                                [viewVisible]="isEditMode"
                                                (iconClicked)="downloadAttachment(attachment.file)"
                                                (viewClicked)="viewAttachment(attachment.file)"
                                            ></attachment-tile>
                                        </div>
                                    </div>
                                    <div class="sp-identifier">
                                        <div
                                            *ngIf="
                                                attachment.documentType === DocumentType.Invoice &&
                                                attachment.spIdentifier
                                            "
                                            class="g-body-1 mt-1 font-medium fadein animation-duration-250"
                                        >
                                            {{
                                                'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.SP_NUMBER'
                                                    | translate : { spNumber: attachment.spIdentifier }
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button
                                type="button"
                                pButton
                                class="flex flex-row gap-2 align-items-center p-button p-button-label p-button-text p-0"
                                (click)="addNewAttachment()"
                                [disabled]="
                                    isSubmitted ||
                                    (this.fileCountLimit <= documentAttachments.length &&
                                        this.fileCountLimit !== null) ||
                                    !this.isLastAttachmentComplete(documentAttachments)
                                "
                            >
                                <svg
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M7 12L12 12M12 12L17 12M12 12V7M12 12L12 17"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                    <circle
                                        cx="12"
                                        cy="12"
                                        r="9"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                </svg>
                                {{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.ADD_DOCUMENT' | translate }}
                            </button>
                        </div>
                    </p-accordionTab>

                    <p-accordionTab header="{{ 'UPLOAD_DOCUMENT_PAGE.COMMENTS.TITLE' | translate }}" [selected]="true">
                        <div class="pb-4 fadein animation-duration-250">
                            <textarea
                                [(ngModel)]="commentText"
                                pInputTextarea
                                placeholder="{{ 'CREATE_ADDITIONAL_WORK.COMMENT.INPUT_BOX_TIP' | translate }}"
                                class="w-full"
                                maxlength="255"
                                [disabled]="isSubmitted"
                            ></textarea>
                        </div>
                    </p-accordionTab>
                </p-accordion>

                <div
                    class="button-wrapper flex flex-column lg:flex-row gap-4 justify-content-center p-2"
                    *ngIf="!isSubmitted"
                >
                    <button
                        pButton
                        [label]="'UPLOAD_DOCUMENT_PAGE.BUTTONS.SEND' | translate"
                        type="submit"
                        form="myForm"
                        class="px-8"
                        (click)="submitDocument()"
                        [loading]="submitLoading$ | ngrxPush"
                        [disabled]="!isFormValid(true)"
                    ></button>
                    <button
                        pButton
                        class="p-button-outlined px-8"
                        (click)="discardChanges()"
                        [loading]="discardLoading$ | ngrxPush"
                    >
                        {{ 'UPLOAD_DOCUMENT_PAGE.BUTTONS.DISCARD' | translate }}
                    </button>
                    <button
                        pButton
                        class="p-button-outlined px-8"
                        (click)="saveWithoutSending()"
                        [disabled]="!isFormValid(false)"
                        [loading]="saveLoading$ | ngrxPush"
                    >
                        {{ 'UPLOAD_DOCUMENT_PAGE.BUTTONS.SAVE_WITHOUT_SENDING' | translate }}
                    </button>
                </div>
            </ng-container>
            <ng-container *ngIf="projectError$ | ngrxPush as projectError">
                <error-feedback-card
                    *ngIf="errorMessage !== null || projectError !== null"
                    [errorMessage]="errorMessage || projectError?.detail"
                ></error-feedback-card>
            </ng-container>
        </base-layout>
    `,
})
export class PartnerDocumentComponent extends BaseManualDocumentUploadComponent implements OnInit {
    invoiceTypes: IDropdownItem[] = [];
    fileCountLimit: number;
    fileSizeLimit: number;

    get fileSizeLimitMb() {
        return this.fileSizeLimit / 1024 / 1024;
    }

    permittedExtensions: string[];
    attachments$ = new BehaviorSubject<FileInfo[]>([]);
    documentAttachments$ = new BehaviorSubject<IPartnerDocumentAttachment[]>([]);
    projects$: Observable<Project[]>;
    projectStatus$: Observable<ProjectStateStatus>;
    projectError$: Observable<IProblemDetails | null>;
    projectsDropdownItems$: Observable<IDropdownItem[]>;
    contractsDropdownItems$: Observable<IDropdownItem[]>;
    documentTypes: IDropdownItem[] = [];
    selectedInvoiceType: AccountTypeEnum | null = null;
    selectedProjectNo = new BehaviorSubject<string | null>(null);
    selectedContractNo = new BehaviorSubject<string | null>(null);
    isEditMode = false;
    isSubmitted = false;
    existingDocument: DocumentUploadDto | null = null;
    errorMessage: string | null = null;
    commentText = '';
    pageTitle = '';
    projectDescription = '';
    isLoadingContracts$ = new BehaviorSubject<boolean>(false);
    isLoadingDocument$ = new BehaviorSubject<boolean>(false);
    submitLoading$ = new BehaviorSubject<boolean>(false);
    saveLoading$ = new BehaviorSubject<boolean>(false);
    discardLoading$ = new BehaviorSubject<boolean>(false);

    isLoading$ = combineLatest([this.isLoadingContracts$, this.isLoadingDocument$]).pipe(
        map(([isLoadingContracts, isLoadingDocument]) => isLoadingContracts || isLoadingDocument)
    );

    protected contextId: string | null = null;
    get ProjectStateStatus() {
        return ProjectStateStatus;
    }

    get AccountTypeEnum() {
        return AccountTypeEnum;
    }

    get Status() {
        return Status;
    }

    get DocumentType() {
        return DocumentType;
    }

    get ProcessingStatusEnum() {
        return ProcessingStatusEnum;
    }

    constructor(
        protected translateService: TranslateService,
        protected dialogService: DialogService,
        private store: Store,
        protected documentAttachmentService: DocumentAttachmentService,
        private partnerDocumentService: PartnerDocumentService,
        protected router: Router,
        private route: ActivatedRoute,
        private authService: AuthService,
        private contractService: ContractService,
        private breadcrumbService: BreadcrumbService,
        private documentTypesService: DocumentTypesService
    ) {
        super();
    }

    ngOnInit(): void {
        this.documentAttachmentService.getAttachmentConfigs().subscribe((res) => {
            this.fileCountLimit = res.fileCountLimit ?? 25;
            this.fileSizeLimit = res.fileSizeLimit ?? 5242880;
            this.permittedExtensions = res.permittedExtensions ?? ['.pdf', '.jpeg', '.png', '.jpg'];
        });

        this.documentTypesService.getInvoiceTypesDropdown().subscribe((res) => {
            this.invoiceTypes = res;
        });

        this.documentTypesService.getDocumentTypesDropdown().subscribe((res) => {
            this.documentTypes = res;
        });

        this.projectStatus$ = this.store.select(ProjectSelectors.selectProjectStateStatus);
        this.projects$ = this.store.select(ProjectSelectors.selectAllProjects);
        this.projectError$ = this.store.select(ProjectSelectors.selectProjectStateError);
        this.projectsDropdownItems$ = this.projects$.pipe(
            map((projects: Project[]) =>
                projects.map(
                    (project) =>
                        ({
                            label: project.projectNo + ' - ' + project.description,
                            value: project.projectNo,
                            description: project.description,
                        } as IDropdownItem)
                )
            )
        );

        this.contractsDropdownItems$ = combineLatest([
            this.selectedProjectNo.asObservable(),
            this.authService.currentUser$.pipe(take(1)),
        ]).pipe(
            takeUntil(this.destroy$),
            switchMap(([projectNo, user]) => {
                if (projectNo) {
                    this.isLoadingContracts$.next(true);
                    return this.contractService.getContracts(user.profileData.partner.contactNo, projectNo).pipe(
                        map((res) =>
                            res.contractViewModels.map(
                                (contract) =>
                                    ({ label: contract.documentNo, value: contract.documentNo } as IDropdownItem)
                            )
                        ),
                        finalize(() => {
                            this.isLoadingContracts$.next(false);
                        })
                    );
                }
                return of([]);
            }),
            finalize(() => {
                this.isLoadingContracts$.next(false);
            })
        );

        this.route.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
            if (params['documentNo']) {
                this.isEditMode = true;
                this.contextId = params['documentNo'];
                this.loadExistingDocument();
            } else {
                // Initialize with one empty attachment row for create mode
                this.contextId = uuidv6();
                this.addNewAttachment();
            }
        });
    }

    onSelectedProjectNoChange(projectNo: string) {
        this.selectedProjectNo.next(projectNo);
        this.selectedContractNo.next(null);
        this.projectsDropdownItems$.pipe(take(1)).subscribe((items) => {
            const selectedItem = items.find((item) => item.value === projectNo);
            this.projectDescription = selectedItem ? selectedItem['description'] : '';
        });
    }

    /**
     * Load existing document data when in edit mode
     */
    loadExistingDocument(): void {
        this.isLoadingDocument$.next(true);
        this.partnerDocumentService
            .getDocumentUpload(this.contextId)
            .pipe(
                switchMap((document) => {
                    this.setDocument(document);

                    this.translateService.get('UPLOAD_DOCUMENT_PAGE.EDIT_TITLE').subscribe((res) => {
                        this.pageTitle = res;
                        this.breadcrumbService.setBreadcrumbs([
                            { label: '{{PRIMARY_NAVIGATION.UPLOAD_DOCUMENT_LIST}}', url: '/partner/documents' },
                            { label: res, url: `/partner/document/${this.contextId}` },
                        ]);
                    });

                    if (document.status === UploadStatusEnum.Submitted) {
                        this.isSubmitted = true;
                        this.translateService.get('UPLOAD_DOCUMENT_PAGE.SUBMITTED_TITLE').subscribe((res) => {
                            this.breadcrumbService.setBreadcrumbs([
                                { label: '{{PRIMARY_NAVIGATION.UPLOAD_DOCUMENT_LIST}}', url: '/partner/documents' },
                                { label: res, url: `/partner/document/${this.contextId}` },
                            ]);
                        });
                    }

                    return EMPTY;
                }),
                finalize(() => {
                    this.isLoadingDocument$.next(false);
                }),
                catchError((err: IProblemDetails) => {
                    this.isLoadingDocument$.next(false);
                    this.errorMessage = err.detail;
                    return EMPTY;
                })
            )
            .subscribe();
    }

    private setDocument(document: DocumentUploadDto) {
        this.existingDocument = document;
        this.selectedProjectNo.next(document.projectNo);
        this.selectedInvoiceType = document.type;
        this.selectedContractNo.next(document.contractNo);
        this.commentText = document.comment || '';

        const attachments: IPartnerDocumentAttachment[] = [];

        document.attachmentDocumentTypes.forEach((entry) => {
            attachments.push({
                documentType: entry.documentType,
                file: entry.attachment,
                spIdentifier: entry.spIdentifier,
                processingStatus: entry.processingStatus,
            });
        });

        this.documentAttachments$.next(attachments);
    }

    showFeedbackDialog2() {
        this.showFeedbackDialog({
            messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
            description: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'),
            buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
            onButton1Click: () => {
                this.reloadPage();
            },
            button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
            onButton2Click: () => {
                this.router.navigate(['/partner/documents']);
            },
            isSuccess: true,
        });
    }

    /**
     * Submit the document with attachments to the API
     */
    submitDocument() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SUBMIT_CONFIRMATION_MESSAGE'),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            const attachments = this.documentAttachments$.value;
            this.submitLoading$.next(true);
            if (this.isEditMode) {
                const request = this.getUpdateDocumentUploadDraftRequest(attachments);
                this.partnerDocumentService
                    .submitDocumentUpload(this.contextId, request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.submitLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/partner/documents']);
                                },
                                isSuccess: true,
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                const request = this.getCreateDocumentUploadDraftRequest(attachments);
                this.partnerDocumentService
                    .createAndSubmitDocument(request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.submitLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            }
        };
    }

    /**
     * Discard all changes and reset the form
     */
    discardChanges() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.DISCARD_CONFIRMATION'),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            this.discardLoading$.next(true);
            if (this.isEditMode) {
                this.partnerDocumentService
                    .removeDocumentUpload(this.contextId)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.discardLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.router.navigate(['/partner/documents']);
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                this.discardLoading$.next(false);
                this.documentAttachmentService.revertFiles(this.contextId).pipe(take(1)).subscribe();
                this.reloadPage();
            }
        };
    }

    /**
     * Save the document without sending it
     */
    saveWithoutSending() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SAVE_CONFIRMATION_MESSAGE'),
                description: this.translateService.instant(
                    'UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SAVE_CONFIRMATION_DESCRIPTION'
                ),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            const attachments = this.documentAttachments$.value;
            this.saveLoading$.next(true);
            if (this.isEditMode) {
                const request = this.getUpdateDocumentUploadDraftRequest(attachments);
                this.partnerDocumentService
                    .updateDocumentUploadDraft(this.contextId, request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.saveLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: (res) => {
                            this.setDocument(res);
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_BUTTON',
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/partner/documents']);
                                },
                                isSuccess: true,
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                const request = this.getCreateDocumentUploadDraftRequest(attachments);
                this.partnerDocumentService
                    .createDocumentUploadDraft(request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.saveLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: (res) => {
                            this.setDocument(res);
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/partner/documents']);
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            }
        };
    }
    getUpdateDocumentUploadDraftRequest(
        validAttachments: IPartnerDocumentAttachment[]
    ): UpdateDocumentUploadDraftRequest {
        return {
            rowVersion: this.existingDocument?.rowVersion,
            projectNo: this.selectedProjectNo.value,
            projectDescription: this.projectDescription,
            contractNo: this.selectedContractNo.value,
            type: this.selectedInvoiceType,
            attachmentDocumentTypes: validAttachments.map(
                (attachment) =>
                    ({
                        documentType: attachment.documentType,
                        attachment: attachment.file,
                    } as AttachmentDocumentTypeDto)
            ),
            comment: this.commentText.trim(),
        };
    }

    getCreateDocumentUploadDraftRequest(
        validAttachments: IPartnerDocumentAttachment[]
    ): CreateDocumentUploadDraftRequest {
        return {
            contextId: this.contextId,
            projectNo: this.selectedProjectNo.value,
            projectDescription: this.projectDescription,
            contractNo: this.selectedContractNo.value,
            type: this.selectedInvoiceType,
            attachmentDocumentTypes: validAttachments.map(
                (attachment) =>
                    ({
                        documentType: attachment.documentType,
                        attachment: attachment.file,
                    } as AttachmentDocumentTypeDto)
            ),
            comment: this.commentText.trim(),
        };
    }
}

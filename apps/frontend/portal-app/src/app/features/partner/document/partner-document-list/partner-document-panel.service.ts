import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
    DocumentUploadDto,
    PartnerDocumentService,
    SortByEnum,
    SortOrderEnum,
    UploadStatusEnum,
} from '@api-clients/document';
import { IDocumentPanelButtonInfo } from '@core/interfaces/document-panel-button-info.interface';
import { dateToDateString, formatDate } from '@core/utils/common';
import { TranslateService } from '@ngx-translate/core';
import { MenuItem } from 'primeng/api';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, finalize, map, switchMap } from 'rxjs/operators';

@Injectable()
export class PartnerDocumentPanelService {
    // Loading states
    private submittedLoading = new BehaviorSubject<boolean>(false);
    private draftLoading = new BehaviorSubject<boolean>(false);

    // Data streams
    private submittedDocuments = new BehaviorSubject<IDocumentPanelButtonInfo<DocumentUploadDto>[]>([]);
    private draftDocuments = new BehaviorSubject<IDocumentPanelButtonInfo<DocumentUploadDto>[]>([]);
    private submittedTotalItems = new BehaviorSubject<number>(0);
    private draftTotalItems = new BehaviorSubject<number>(0);
    private activeTabIndex = new BehaviorSubject<number>(0);
    private activeItem = new BehaviorSubject<MenuItem>(null);

    // Search and pagination params
    private searchTerm = new BehaviorSubject<string>('');
    private submittedParams = new BehaviorSubject<{ page: number; rows: number }>({ page: 0, rows: 10 });
    private draftParams = new BehaviorSubject<{ page: number; rows: number }>({ page: 0, rows: 10 });

    // Date range
    rangeDates: Date[] = [];
    private dateRange = new BehaviorSubject<{ from: Date | null; to: Date | null }>({ from: null, to: null });
    public dateRange$ = this.dateRange.asObservable();

    // Public observables for components to subscribe to
    public submittedLoading$ = this.submittedLoading.asObservable();
    public draftLoading$ = this.draftLoading.asObservable();
    public submittedDocuments$ = this.submittedDocuments.asObservable();
    public draftDocuments$ = this.draftDocuments.asObservable();
    public submittedTotalItems$ = this.submittedTotalItems.asObservable();
    public draftTotalItems$ = this.draftTotalItems.asObservable();
    public activeTabIndex$ = this.activeTabIndex.asObservable();
    public activeItem$ = this.activeItem.asObservable();

    // Ordering
    private reverseOrder = new BehaviorSubject<boolean>(false);
    public reverseOrder$ = this.reverseOrder.asObservable();

    constructor(
        private partnerDocumentService: PartnerDocumentService,
        private translateService: TranslateService,
        private router: Router
    ) {}

    /**
     * Initialize the document panel data streams
     * @param projectNo Optional project number for filtering
     */
    public initializeDataStreams(projectNo?: string): void {
        // Set up data loading streams
        this.setupSubmittedDocumentsStream(projectNo);
        this.setupDraftDocumentsStream(projectNo);
    }

    /**
     * Get tab menu items with counts
     */
    public getTabMenuItems(projectNo?: string): Observable<MenuItem[]> {
        return this.partnerDocumentService.getDocumentUploadTotalCounts(projectNo).pipe(
            map(({ draft, submitted }) => {
                this.submittedTotalItems.next(submitted);
                this.draftTotalItems.next(draft);

                return [
                    {
                        label: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.MENUS.DOCUMENT_UPLOADS', {
                            count: submitted,
                        }),
                        tabindex: '2',
                    },
                    {
                        label: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.MENUS.DRAFT_DOCUMENT_UPLOADS', {
                            count: draft,
                        }),
                        tabindex: '3',
                    },
                ];
            }),
            map((items) => {
                this.activeItem.next(items[this.activeTabIndex.value]);
                return items;
            })
        );
    }

    /**
     * Handle tab menu active item change
     * @param event The menu item that was selected
     */
    public tabMenuActiveItemChange(event: MenuItem): void {
        this.activeItem.next(event);
        const newTabIndex = +event.tabindex;
        this.activeTabIndex.next(newTabIndex);

        // Reset page when switching tabs
        if (newTabIndex === 2) {
            this.submittedParams.next({ page: 0, rows: 10 });
        } else if (newTabIndex === 3) {
            this.draftParams.next({ page: 0, rows: 10 });
        }
    }

    /**
     * Handle submitted documents page change
     * @param event Page change event with page number and rows per page
     */
    public onSubmittedPageChange(event: { page: number; rows: number }): void {
        this.submittedParams.next({ page: event.page, rows: event.rows });
    }

    /**
     * Handle draft documents page change
     * @param event Page change event with page number and rows per page
     */
    public onDraftPageChange(event: { page: number; rows: number }): void {
        this.draftParams.next({ page: event.page, rows: event.rows });
    }

    /**
     * Handle search box input
     * @param searchTerm The search term to filter documents
     */
    public searchBox(searchTerm: string): void {
        this.searchTerm.next(searchTerm);

        // Reset pagination when search changes
        if (this.activeTabIndex.value === 2) {
            this.submittedParams.next({ page: 0, rows: 10 });
        } else if (this.activeTabIndex.value === 3) {
            this.draftParams.next({ page: 0, rows: 10 });
        }
    }

    private setupSubmittedDocumentsStream(projectNo?: string): void {
        combineLatest([
            this.submittedParams.pipe(
                distinctUntilChanged((prev, curr) => prev.page === curr.page && prev.rows === curr.rows),
                debounceTime(300)
            ),
            this.searchTerm.pipe(distinctUntilChanged(), debounceTime(300)),
            this.dateRange.pipe(
                distinctUntilChanged((prev, curr) => prev.from === curr.from && prev.to === curr.to),
                debounceTime(300)
            ),
            this.reverseOrder.pipe(distinctUntilChanged(), debounceTime(300)),
        ])
            .pipe(
                switchMap(([params, searchTerm, dateRange, reverseOrder]) => {
                    this.submittedLoading.next(true);
                    return this.partnerDocumentService
                        .listDocumentUploads(
                            UploadStatusEnum.Submitted,
                            projectNo,
                            params.page + 1, // API uses 1-based indexing
                            params.rows,
                            searchTerm,
                            dateToDateString(dateRange?.from),
                            dateToDateString(dateRange?.to),
                            SortByEnum.CreatedDate,
                            reverseOrder ? SortOrderEnum.Asc : SortOrderEnum.Desc
                        )
                        .pipe(
                            finalize(() => this.submittedLoading.next(false)),
                            catchError((error) => {
                                console.error('Error loading submitted invoices:', error);
                                return of({ documents: [], totalCount: 0 });
                            })
                        );
                })
            )
            .subscribe((response) => {
                this.submittedDocuments.next(this.mapDocumentsToPanelItems(response.documents));
                console.log(this.submittedDocuments.value);
                this.submittedTotalItems.next(response.totalCount);
            });
    }

    private setupDraftDocumentsStream(projectNo?: string): void {
        combineLatest([
            this.draftParams.pipe(
                distinctUntilChanged((prev, curr) => prev.page === curr.page && prev.rows === curr.rows),
                debounceTime(300)
            ),
            this.searchTerm.pipe(distinctUntilChanged(), debounceTime(300)),
            this.dateRange.pipe(
                distinctUntilChanged((prev, curr) => prev.from === curr.from && prev.to === curr.to),
                debounceTime(300)
            ),
            this.reverseOrder.pipe(distinctUntilChanged(), debounceTime(300)),
        ])
            .pipe(
                switchMap(([params, searchTerm, dateRange, reverseOrder]) => {
                    this.draftLoading.next(true);
                    return this.partnerDocumentService
                        .listDocumentUploads(
                            UploadStatusEnum.Draft,
                            projectNo,
                            params.page + 1, // API uses 1-based indexing
                            params.rows,
                            searchTerm,
                            dateToDateString(dateRange?.from),
                            dateToDateString(dateRange?.to),
                            SortByEnum.CreatedDate,
                            reverseOrder ? SortOrderEnum.Asc : SortOrderEnum.Desc
                        )
                        .pipe(
                            finalize(() => this.draftLoading.next(false)),
                            catchError((error) => {
                                console.error('Error loading draft documents:', error);
                                return of({ documents: [], totalCount: 0 });
                            })
                        );
                })
            )
            .subscribe((response) => {
                this.draftDocuments.next(this.mapDocumentsToPanelItems(response.documents));
                this.draftTotalItems.next(response.totalCount);
            });
    }

    private mapDocumentsToPanelItems(documents: DocumentUploadDto[]): IDocumentPanelButtonInfo<DocumentUploadDto>[] {
        return documents.map((document) => ({
            element: document,
            projectNo: document.projectNo,
            projectDescription: document.projectDescription,
            uploadedFiles: document.attachmentDocumentTypes.map((file) => file.attachment.fileName),
            uploadTime: formatDate(document.createdDate, 'YYYY.MM.DD. HH:mm'),
            status: document.status,
            processingStatus: document.processingStatus,
        }));
    }

    public toggleOrdering(): void {
        this.reverseOrder.next(!this.reverseOrder.value);
    }

    public setDateRange(rangeDates: any): void {
        if (rangeDates) {
            this.dateRange.next({ from: rangeDates[0], to: rangeDates[1] });
        } else {
            this.dateRange.next({ from: null, to: null });
        }
    }

    navigateToDocument(document: DocumentUploadDto): void {
        this.router.navigate(['/partner/document', document.id]);
    }
}

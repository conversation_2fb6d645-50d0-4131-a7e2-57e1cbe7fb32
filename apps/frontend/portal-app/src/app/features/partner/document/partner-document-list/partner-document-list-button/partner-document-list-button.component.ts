import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { IDocumentPanelButtonInfo } from '@core/interfaces/document-panel-button-info.interface';

@Component({
    selector: 'document-list-button',
    changeDetection: ChangeDetectionStrategy.OnPush,
    template: `
        <button
            class="flex px-5 py-3 justify-content-between align-items-center w-full border-1 border-round border-gray-200 bg-white hover:shadow-2 transition-all transition-duration-200 cursor-pointer fadein animation-duration-150"
            (click)="onButtonClick()"
        >
            <div class="grid grid-nogutter w-full justify-content-between">
                <div class="col-8 text-left">
                    <div class="g-body-1 text-gray-900">
                        {{ buttonInfo.projectNo || '' }}
                        {{ buttonInfo.projectDescription ? ' - ' + buttonInfo.projectDescription : '' }}
                    </div>
                    <div class="headline-6">
                        {{
                            'DOCUMENT_LIST_PAGE.UPLOADED_FILES' | translate : { count: buttonInfo.uploadedFiles.length }
                        }}
                    </div>
                </div>

                <div class="col text-left flex flex-column align-items-center justify-content-center">
                    <div class="headline-6">
                        {{ 'DOCUMENT_LIST_PAGE.UPLOAD_TIME' | translate }}
                    </div>
                    <div class="g-body-1 text-gray-900">{{ buttonInfo.uploadTime }}</div>
                </div>

                <div class="col text-left flex align-items-center">
                    <badge
                        *ngIf="buttonInfo.status || buttonInfo.processingStatus"
                        [status]="buttonInfo.status === 'Submitted' ? buttonInfo.processingStatus : buttonInfo.status"
                        [autoMargin]="false"
                    >
                    </badge>
                </div>
            </div>

            <!-- Arrow Icon -->
            <img src="assets/images/arrow_next.svg" alt="Next" class="ml-2" />
        </button>
    `,
})
export class PartnerDocumentListButtonComponent<TPanelElement> {
    @Input() buttonInfo: IDocumentPanelButtonInfo<TPanelElement>;
    @Output() elementPressed: EventEmitter<TPanelElement> = new EventEmitter();

    onButtonClick() {
        this.elementPressed.emit(this.buttonInfo.element);
    }
}

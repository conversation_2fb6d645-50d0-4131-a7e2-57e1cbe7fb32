import { Component, OnInit, Optional } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FileInfo } from '@api-clients/contracts';
import {
    AccountTypeEnum,
    AttachmentService as DocumentAttachmentService,
    DocumentType,
    EloProcessingStatusEnum,
    SecretaryDocumentService,
    SecretaryPartnerDocumentDetailDto,
    UploadStatusEnum,
} from '@api-clients/document';
import { IPartnerDocumentAttachment } from '@core/interfaces/document-attachment.interface';
import { IDropdownItem } from '@core/interfaces/dropdown-item.interface';
import { IFeedbackDialogData } from '@core/interfaces/feedback-dialog.interface';
import { IProblemDetails } from '@core/interfaces/problem-details.interface';
import { DocumentTypesService } from '@core/services/document-types.service';
import { downloadBlob, formatDate, openBlobInBrowser } from '@core/utils/common';
import { TranslateService } from '@ngx-translate/core';
import { AppBaseSubscriptionComponent } from '@shared/components/app-base-subscription.component';
import { FeedbackDialogComponent } from '@shared/components/feedback-dialog/feedback-dialog.component';
import { DialogService, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { BehaviorSubject, catchError, EMPTY, finalize, takeUntil } from 'rxjs';

@Component({
    selector: 'app-secretary-partner-document',
    styles: [
        `
            .parent {
                display: grid;
                grid-template-columns: auto minmax(280px, 1fr) auto;
                grid-template-rows: repeat(2, auto);
                row-gap: 5px;
                column-gap: 10px;
                align-items: center;

                @media (max-width: 62em) {
                    grid-template-columns: auto minmax(280px, 1fr) auto;
                    grid-template-rows: repeat(3, auto);
                }
            }

            .order-number {
                grid-column: 1;
                grid-row: 1;
            }

            .document-type {
                grid-column: 2;
                grid-row: 1;
            }

            .file-preview {
                grid-column: 3;
                grid-row: 1;

                @media (max-width: 62em) {
                    grid-column: 1 / 4;
                    grid-row: 3;
                }
            }

            .sp-identifier {
                grid-column: 2;
                grid-row: 2;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
        `,
    ],
    template: `
        <market-progress-bar *ngIf="isLoading$ | async"></market-progress-bar>
        <base-layout class="fadein animation-duration-250">
            <div class="mt-5"></div>
            <ng-container *ngIf="!errorMessage && document">
                <p-accordion [multiple]="true">
                    <p-accordionTab
                        header="{{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.TITLE' | translate }}"
                        [selected]="true"
                    >
                        <div class="flex flex-column gap-4 fadein animation-duration-250">
                            <p class="g-body-1 font-bold">
                                {{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.DOCUMENT_STATUS' | translate }}
                                {{ documentStatus }}
                            </p>
                            <div class="lg:col-5 p-fluid p-0 flex gap-4 flex-column">
                                <span class="p-float-label">
                                    <input
                                        type="text"
                                        pInputText
                                        [value]="document.partner"
                                        readonly
                                        class="p-inputtext-readonly"
                                    />
                                    <label>{{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.PARTNER' | translate }}</label>
                                </span>
                                <span class="p-float-label">
                                    <input
                                        type="text"
                                        pInputText
                                        [value]="document.partnerContactNo"
                                        readonly
                                        class="p-inputtext-readonly"
                                    />
                                    <label>{{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.CONTACT_NO' | translate }}</label>
                                </span>
                                <span class="p-float-label">
                                    <input
                                        type="text"
                                        pInputText
                                        [value]="selectedProjectDisplay"
                                        readonly
                                        class="p-inputtext-readonly"
                                    />
                                    <label>{{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.PROJECT' | translate }}</label>
                                </span>
                                <span class="p-float-label">
                                    <input
                                        type="text"
                                        pInputText
                                        [value]="document.contractNo || '-'"
                                        readonly
                                        class="p-inputtext-readonly"
                                    />
                                    <label>{{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.CONTRACT' | translate }}</label>
                                </span>
                                <span class="p-float-label">
                                    <input
                                        type="text"
                                        pInputText
                                        [value]="getAccountTypeLabel(document.type)"
                                        readonly
                                        class="p-inputtext-readonly"
                                    />
                                    <label>{{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.INVOICE_TYPE' | translate }}</label>
                                </span>
                                <span class="p-float-label" *ngIf="document.dateOfArrival">
                                    <input
                                        type="text"
                                        pInputText
                                        [value]="formatDate(document.dateOfArrival)"
                                        readonly
                                        class="p-inputtext-readonly"
                                    />
                                    <label>{{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.ARRIVAL_DATE' | translate }}</label>
                                </span>
                            </div>
                        </div>
                    </p-accordionTab>
                    <p-accordionTab
                        header="{{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.TITLE' | translate }}"
                        [selected]="true"
                    >
                        <div
                            class="flex flex-column gap-4 fadein animation-duration-250"
                            *ngIf="documentAttachments$ | async as documentAttachments"
                        >
                            <div
                                *ngFor="let attachment of documentAttachments; let i = index"
                                class="flex flex-row gap-2 align-items-center"
                            >
                                <div class="parent">
                                    <div class="order-number">
                                        <p class="g-body-1 text-gray-600">
                                            {{ i + 1 + '.' }}
                                        </p>
                                    </div>

                                    <div class="document-type">
                                        <span class="p-float-label">
                                            <input
                                                type="text"
                                                pInputText
                                                [value]="getDocumentTypeLabel(attachment.documentType)"
                                                readonly
                                                class="p-inputtext-readonly w-full"
                                            />
                                            <label>{{
                                                'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.DOCUMENT_TYPE' | translate
                                            }}</label>
                                        </span>
                                    </div>

                                    <div class="file-preview">
                                        <div *ngIf="attachment.file" class="flex align-items-center gap-2 mt-2 lg:mt-0">
                                            <attachment-tile
                                                [isDeleteMode]="false"
                                                [attachmentName]="attachment.file.fileName"
                                                [iconVisible]="true"
                                                [viewVisible]="true"
                                                (iconClicked)="downloadAttachment(attachment.file)"
                                                (viewClicked)="viewAttachment(attachment.file)"
                                            ></attachment-tile>
                                        </div>
                                    </div>
                                    <div class="sp-identifier">
                                        <div
                                            *ngIf="
                                                attachment.documentType === DocumentType.Invoice &&
                                                attachment.spIdentifier
                                            "
                                            class="g-body-1 mt-1 font-medium fadein animation-duration-250"
                                        >
                                            {{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.SP_NUMBER' | translate }}
                                            <span
                                                [ngClass]="{
                                                    'text-green-500':
                                                        attachment.processingStatus === EloProcessingStatusEnum.Success,
                                                    'text-red-500':
                                                        attachment.processingStatus === EloProcessingStatusEnum.Failed
                                                }"
                                                >{{ attachment.spIdentifier }}</span
                                            >
                                        </div>
                                        <i
                                            *ngIf="attachment.processingStatus === EloProcessingStatusEnum.Success"
                                            class="pi pi-check-circle text-green-500 text-xl"
                                            title="Processing completed successfully"
                                        >
                                        </i>
                                        <i
                                            *ngIf="attachment.processingStatus === EloProcessingStatusEnum.Failed"
                                            class="pi pi-times-circle text-red-500 text-xl"
                                            title="Processing failed"
                                        >
                                        </i>
                                        <span
                                            *ngIf="
                                                attachment.processingStatus === EloProcessingStatusEnum.Failed &&
                                                attachment.failureReason
                                            "
                                            class="text-red-500"
                                            >{{ attachment.failureReason }}</span
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </p-accordionTab>

                    <p-accordionTab
                        header="{{ 'UPLOAD_DOCUMENT_PAGE.COMMENTS.TITLE' | translate }}"
                        [selected]="true"
                        *ngIf="document.comment"
                    >
                        <div class="pb-4 fadein animation-duration-250">
                            <textarea
                                [value]="document.comment"
                                pInputTextarea
                                readonly
                                class="w-full p-inputtextarea-readonly"
                                rows="3"
                            ></textarea>
                        </div>
                    </p-accordionTab>
                </p-accordion>
                <p class="g-body-1 mt-4 text-gray-600">
                    {{ 'UPLOAD_DOCUMENT_PAGE.SECRETARY_INVOICE.UPLOAD_IDENTIFIER' | translate : { contextId } }}
                </p>
            </ng-container>

            <ng-container *ngIf="errorMessage">
                <error-feedback-card [errorMessage]="errorMessage"></error-feedback-card>
            </ng-container>
        </base-layout>
    `,
})
export class SecretaryPartnerDocumentComponent extends AppBaseSubscriptionComponent implements OnInit {
    document: SecretaryPartnerDocumentDetailDto | null = null;
    documentAttachments$ = new BehaviorSubject<IPartnerDocumentAttachment[]>([]);

    accountTypes: IDropdownItem[] = [];
    documentTypes: IDropdownItem[] = [];
    errorMessage: string | null = null;
    isLoading$ = new BehaviorSubject<boolean>(false);

    contextId: string | null = null;

    get AccountTypeEnum() {
        return AccountTypeEnum;
    }

    get DocumentType() {
        return DocumentType;
    }

    get EloProcessingStatusEnum() {
        return EloProcessingStatusEnum;
    }

    get selectedProjectDisplay(): string {
        if (!this.document) return '-';
        return this.document.projectNo ? `${this.document.projectNo} - ${this.document.projectDescription || ''}` : '-';
    }

    get documentStatus(): string {
        if (!this.document) return '-';
        return this.document.status === UploadStatusEnum.Draft
            ? this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.DRAFT')
            : this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.SUBMITTED');
    }

    constructor(
        private translateService: TranslateService,
        private dialogService: DialogService,
        private documentAttachmentService: DocumentAttachmentService,
        private secretaryDocumentService: SecretaryDocumentService,
        private route: ActivatedRoute,
        private documentTypesService: DocumentTypesService,
        @Optional() private dynamicDialogConfig?: DynamicDialogConfig
    ) {
        super();
    }

    ngOnInit(): void {
        this.documentTypesService.getInvoiceTypesDropdown().subscribe((res) => {
            this.accountTypes = res;
        });

        this.documentTypesService.getDocumentTypesDropdown().subscribe((res) => {
            this.documentTypes = res;
        });

        // Check if we're in a dialog and have documentNo from dialog config
        if (this.dynamicDialogConfig?.data?.documentNo) {
            this.contextId = this.dynamicDialogConfig.data.documentNo;
            this.loadDocumentDetails();
        } else {
            // Otherwise get documentNo from route params
            this.route.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
                if (params['documentNo']) {
                    this.contextId = params['documentNo'];
                    this.loadDocumentDetails();
                } else {
                    this.errorMessage = 'Document ID not found in route parameters';
                }
            });
        }
    }

    /**
     * Load document details using the getPartnerDocumentDetails endpoint
     */
    loadDocumentDetails(): void {
        if (!this.contextId) return;

        this.isLoading$.next(true);
        this.secretaryDocumentService
            .getPartnerDocumentDetails(this.contextId)
            .pipe(
                finalize(() => {
                    this.isLoading$.next(false);
                }),
                catchError((err: IProblemDetails) => {
                    this.errorMessage = err.detail;
                    return EMPTY;
                })
            )
            .subscribe((document: SecretaryPartnerDocumentDetailDto) => {
                this.setDocument(document);
            });
    }

    private setDocument(document: SecretaryPartnerDocumentDetailDto) {
        this.document = document;

        const attachments: IPartnerDocumentAttachment[] = [];
        if (document.attachmentDocumentTypes) {
            document.attachmentDocumentTypes.forEach((entry) => {
                attachments.push({
                    documentType: entry.documentType || null,
                    file: entry.attachment,
                    spIdentifier: entry.spIdentifier || null,
                    processingStatus: entry.processingStatus || null,
                    failureReason: entry.failureReason || null,
                });
            });
        }

        this.documentAttachments$.next(attachments);
    }

    formatDate = formatDate;

    getAccountTypeLabel(accountType: AccountTypeEnum | undefined): string {
        if (!accountType) return '-';
        const accountTypeItem = this.accountTypes.find((item) => item.value === accountType);
        return accountTypeItem ? accountTypeItem.label : accountType.toString();
    }

    getDocumentTypeLabel(documentType: DocumentType | null): string {
        if (!documentType) return '-';
        const documentTypeItem = this.documentTypes.find((item) => item.value === documentType);
        return documentTypeItem ? documentTypeItem.label : documentType.toString();
    }

    downloadAttachment(attachment: FileInfo) {
        if (!this.contextId) return;

        this.documentAttachmentService
            .downloadFile(this.contextId, attachment.fileId)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (response: Blob) => {
                    downloadBlob(response, attachment.fileName, attachment.mimeType);
                },
                error: (err: IProblemDetails) => {
                    this.dialogService.open(FeedbackDialogComponent, {
                        width: '40rem',
                        showHeader: false,
                        dismissableMask: true,
                        data: <IFeedbackDialogData>{
                            message: this.translateService.instant('ATTACHMENTS.UNSUCCESSFUL_DOWNLOAD'),
                            description: err.detail,
                            buttonText: this.translateService.instant('COMMON.BACK'),
                            isSuccess: false,
                        },
                    });
                },
            });
    }

    viewAttachment(attachment: FileInfo) {
        if (!this.contextId) return;

        this.documentAttachmentService
            .downloadFile(this.contextId, attachment.fileId)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (response: Blob) => {
                    openBlobInBrowser(response, attachment.mimeType, attachment.fileName);
                },
                error: (err: IProblemDetails) => {
                    this.dialogService.open(FeedbackDialogComponent, {
                        width: '40rem',
                        showHeader: false,
                        dismissableMask: true,
                        data: <IFeedbackDialogData>{
                            message: this.translateService.instant('ATTACHMENTS.UNSUCCESSFUL_DOWNLOAD'),
                            description: err.detail,
                            buttonText: this.translateService.instant('COMMON.BACK'),
                            isSuccess: false,
                        },
                    });
                },
            });
    }
}

import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Directive, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { FilterMatchMode } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Table } from 'primeng/table';
import {
    BehaviorSubject,
    catchError,
    combineLatest,
    debounceTime,
    EMPTY,
    finalize,
    map,
    Observable,
    startWith,
    Subject,
    switchMap,
    takeUntil,
} from 'rxjs';

// API imports
import {
    ColumnFilterMatchModeEnum,
    ProcessingStatusEnum,
    SecretaryDocumentService,
    SecretaryUploadTypeEnum,
    TableRequest,
    UploadStatusEnum,
} from '@api-clients/document';
import { IProblemDetails } from '@core/interfaces/problem-details.interface';
import { formatDate } from '@core/utils/common';
import { TableUtils } from '@core/utils/table-utils';
import { ColumnConfigComponent } from './column-config.component';
import { SecretaryDocumentBatchComponent } from './secretary-document/secretary-document-batch.component';
import { SecretaryDocumentManualComponent } from './secretary-document/secretary-document-manual.component';
import { SecretaryDocumentPercaseComponent } from './secretary-document/secretary-document-percase.component';

export interface TableColumn {
    id: string;
    label: string;
    field: string;
    sortable: boolean;
    filterable: boolean;
    filterType: 'text' | 'date' | 'dropdown';
    width: string;
    visible: boolean;
}

export interface StatusOption {
    label: string;
    value: ProcessingStatusEnum | null;
}

export interface TableState {
    page: number;
    pageSize: number;
    filters: any;
    sortField: string | null;
    sortOrder: number;
}

@Directive()
export abstract class SecretaryTableBaseComponent<T> implements OnInit, OnDestroy {
    @ViewChild('dataTable') dataTable!: Table;
    @ViewChild(ColumnConfigComponent) columnConfigComponent!: ColumnConfigComponent;

    @Input() uploadStatus!: UploadStatusEnum;
    @Input() tableStateKey!: string;
    @Input() columnConfigKey!: string;

    private tableState$ = new BehaviorSubject<TableState>({
        page: 1,
        pageSize: 100,
        filters: {},
        sortField: null,
        sortOrder: 1,
    });

    formatDate = formatDate;

    private refresh$ = new Subject<void>();
    protected destroy$ = new Subject<void>();
    private error$ = new Subject<string | null>();

    documents$ = new BehaviorSubject<T[]>([]);
    loading$ = new BehaviorSubject<boolean>(false);
    totalRecords$ = new BehaviorSubject<number>(0);
    errorMessage$ = this.error$.asObservable();

    pageSize = 100;

    textMatchModeOptions = [
        {
            label: this.translateService.instant('COMMON.PRIME_NG.MATCH_MODES.CONTAINS'),
            value: FilterMatchMode.CONTAINS,
        },
        {
            label: this.translateService.instant('COMMON.PRIME_NG.MATCH_MODES.STARTS_WITH'),
            value: FilterMatchMode.STARTS_WITH,
        },
        { label: this.translateService.instant('COMMON.PRIME_NG.MATCH_MODES.EQUALS'), value: FilterMatchMode.EQUALS },
    ];

    statusOptions: StatusOption[] = [];

    selectedDocument: T | null = null;

    protected dialogRef: DynamicDialogRef | null = null;

    // Column management
    private _availableColumnsSubject = new BehaviorSubject<TableColumn[]>([]);
    readonly availableColumns$ = this._availableColumnsSubject.asObservable();
    readonly visibleColumns$ = this.availableColumns$.pipe(map((cols) => cols.filter((c) => c.visible)));
    availableColumns: TableColumn[] = [];
    visibleColumns: TableColumn[] = [];
    private workingColumnsSubject = new BehaviorSubject<TableColumn[]>([]);
    readonly workingColumns$ = this.workingColumnsSubject.asObservable();

    readonly rowsPerPageOptions = [100, 50, 25];
    readonly tableStyle = { 'min-width': '60rem' } as const;

    constructor(
        protected secretaryDocumentService: SecretaryDocumentService,
        protected translateService: TranslateService,
        protected router: Router,
        protected route: ActivatedRoute,
        protected dialogService: DialogService
    ) {}

    ngOnInit(): void {
        this.initializeStatusOptions();
        this.initializeColumns();
        this.loadColumnConfiguration();
        this.setupTableDataStream();
        this.setupRouterEvents();

        // Keep existing array bindings working, but drive them from RxJS
        this.visibleColumns$.pipe(takeUntil(this.destroy$)).subscribe((cols) => (this.visibleColumns = cols));
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.tableState$.complete();
        this.documents$.complete();
        this.loading$.complete();
        this.totalRecords$.complete();
        this.error$.complete();
        this.refresh$.complete();
        this._availableColumnsSubject.complete();
        this.workingColumnsSubject.complete();
        this.closeDialog();
    }

    private setupRouterEvents(): void {
        this.route.firstChild?.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
            if (params['documentNo']) {
                if (this.dialogRef) {
                    return;
                }
                const childRoutePath = this.route.firstChild?.routeConfig?.path || null;
                this.openDialogForRouteById(params['documentNo'], childRoutePath);
            }
        });
    }

    // Implemented by derived components to open the appropriate dialog when a route param is present
    protected abstract openDialogForRouteById(documentId: string, childRoutePath: string | null): void;

    private initializeStatusOptions(): void {
        this.updateStatusOptions();

        this.translateService.onLangChange.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.updateStatusOptions();
        });
    }

    private updateStatusOptions(): void {
        this.statusOptions = [
            {
                label: this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.STATUS.SUCCESS'),
                value: ProcessingStatusEnum.Success,
            },
            {
                label: this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.STATUS.PROCESSING'),
                value: ProcessingStatusEnum.Processing,
            },
            {
                label: this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.STATUS.FAILED'),
                value: ProcessingStatusEnum.Failed,
            },
        ];
    }

    private setupTableDataStream(): void {
        combineLatest([this.tableState$, this.refresh$.pipe(startWith(null))])
            .pipe(
                debounceTime(50),
                switchMap(([tableState]) => {
                    this.loading$.next(true);
                    this.clearError();

                    const tableRequest: TableRequest = {
                        page: tableState.page,
                        pageSize: tableState.pageSize,
                        columnFilters: TableUtils.buildColumnFilters(tableState.filters),
                        sortFields: TableUtils.buildSortFields(tableState.sortField, tableState.sortOrder),
                    };

                    tableRequest.columnFilters?.push({
                        field: 'status',
                        matchMode: ColumnFilterMatchModeEnum.Equals,
                        value: this.uploadStatus as unknown as object,
                    });

                    return this.getDocuments(tableRequest).pipe(
                        catchError((error: IProblemDetails) => {
                            this.error$.next(error.detail);
                            return EMPTY;
                        }),
                        finalize(() => this.loading$.next(false))
                    );
                }),
                takeUntil(this.destroy$)
            )
            .subscribe({
                next: (response: any) => {
                    this.documents$.next(response.data);
                    this.totalRecords$.next(response.totalRecords);
                },
            });
    }

    onTableStateChange(event: any): void {
        const newState: TableState = {
            page: Math.floor(event.first / event.rows) + 1,
            pageSize: event.rows,
            filters: event.filters || {},
            sortField: event.sortField || null,
            sortOrder: event.sortOrder || 1,
        };

        this.pageSize = newState.pageSize;
        this.tableState$.next(newState);
    }

    refreshTable(): void {
        this.refresh$.next();
    }

    clearTable(): void {
        this.dataTable?.clear();
        this.dataTable.clearState();
        this.clearError();
    }

    clearError(): void {
        this.error$.next(null);
    }

    getStatusSeverity(processingStatus: ProcessingStatusEnum | null): string {
        switch (processingStatus) {
            case ProcessingStatusEnum.Success:
                return 'success';
            case ProcessingStatusEnum.Processing:
                return 'info';
            case ProcessingStatusEnum.Failed:
                return 'danger';
            default:
                return 'warning';
        }
    }

    getStatusLabel(processingStatus: ProcessingStatusEnum | null): string {
        switch (processingStatus) {
            case ProcessingStatusEnum.Success:
                return this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.STATUS.SUCCESS');
            case ProcessingStatusEnum.Processing:
                return this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.STATUS.PROCESSING');
            case ProcessingStatusEnum.Failed:
                return this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.STATUS.FAILED');
            default:
                return processingStatus || '';
        }
    }

    viewDocument(document: T): void {
        if (document) {
            this.navigateToDocument(document);
            this.openDocumentDialog(document);
        }
    }

    private closeDialog(): void {
        if (this.dialogRef) {
            this.dialogRef.close();
            this.dialogRef = null;
        }
    }

    // Column management methods
    private loadColumnConfiguration(): void {
        const saved = localStorage.getItem(this.columnConfigKey);
        if (saved) {
            try {
                const config = JSON.parse(saved);
                // Merge saved configuration with default columns
                const current = [...this.availableColumns];
                config.forEach((savedCol: any) => {
                    const existingCol = current.find((col) => col.id === savedCol.id);
                    if (existingCol) {
                        existingCol.visible = savedCol.visible;
                    }
                });
                // Reorder columns based on saved order
                const reorderedColumns: TableColumn[] = [];
                config.forEach((savedCol: any) => {
                    const col = current.find((c) => c.id === savedCol.id);
                    if (col) {
                        reorderedColumns.push(col);
                    }
                });
                // Add any new columns that weren't in the saved config
                current.forEach((col) => {
                    if (!reorderedColumns.find((c) => c.id === col.id)) {
                        reorderedColumns.push(col);
                    }
                });
                this.setAvailableColumns(reorderedColumns);
            } catch (error) {
                console.error('Error loading column configuration:', error);
            }
        }
    }

    updateVisibleColumns(): void {
        this.visibleColumns = (this._availableColumnsSubject.value || []).filter((col) => col.visible);
    }

    onDropColumns(event: CdkDragDrop<TableColumn[]>): void {
        const list = [...this.workingColumnsSubject.value];
        moveItemInArray(list, event.previousIndex, event.currentIndex);
        this.workingColumnsSubject.next(list);
    }

    resetColumnConfiguration(): void {
        const defaults = this.getDefaultColumns();
        this.workingColumnsSubject.next(defaults);
    }

    saveColumnConfiguration(): void {
        const committed = this.workingColumnsSubject.value.map((c) => ({ ...c }));
        this.setAvailableColumns(committed);
        const config = committed.map((col) => ({ id: col.id, visible: col.visible }));
        localStorage.setItem(this.columnConfigKey, JSON.stringify(config));
        this.columnConfigComponent.hide();
    }

    toggleColumnConfig(event: Event): void {
        this.startColumnConfig();
        this.columnConfigComponent.toggle(event);
    }

    startColumnConfig(): void {
        const snapshot = this._availableColumnsSubject.value.map((c) => ({ ...c }));
        this.workingColumnsSubject.next(snapshot);
    }

    onColumnVisibilityChanged(event: { index: number; visible: boolean }): void {
        const list = [...this.workingColumnsSubject.value];
        list[event.index] = { ...list[event.index], visible: event.visible };
        this.workingColumnsSubject.next(list);
    }

    private setAvailableColumns(columns: TableColumn[]): void {
        this.availableColumns = columns;
        this._availableColumnsSubject.next(columns);
        this.updateVisibleColumns();
    }

    protected get availableColumnsSubject() {
        return this._availableColumnsSubject;
    }

    protected getDocuments(tableRequest: TableRequest): Observable<any> {
        return this.getDocumentsApiCall(tableRequest);
    }

    protected getDialogComponentFromUploadType(uploadType?: SecretaryUploadTypeEnum): any {
        switch (uploadType) {
            case SecretaryUploadTypeEnum.Manual:
                return SecretaryDocumentManualComponent;
            case SecretaryUploadTypeEnum.Batch:
                return SecretaryDocumentBatchComponent;
            case SecretaryUploadTypeEnum.PerCase:
            default:
                return SecretaryDocumentPercaseComponent;
        }
    }

    protected getHeaderKeyFromUploadType(uploadType?: SecretaryUploadTypeEnum): string {
        switch (uploadType) {
            case SecretaryUploadTypeEnum.Manual:
                return 'UPLOAD_DOCUMENT_PAGE.MANUAL_EDIT_TITLE';
            case SecretaryUploadTypeEnum.Batch:
                return 'UPLOAD_DOCUMENT_PAGE.BATCH_EDIT_TITLE';
            case SecretaryUploadTypeEnum.PerCase:
            default:
                return 'UPLOAD_DOCUMENT_PAGE.PERCASE_EDIT_TITLE';
        }
    }

    // Abstract methods to be implemented by derived classes
    protected abstract initializeColumns(): void;
    protected abstract getDefaultColumns(): TableColumn[];
    protected abstract getDocumentsApiCall(tableRequest: TableRequest): Observable<any>;
    protected abstract navigateToDocument(document: T): void;
    protected abstract openDocumentDialog(document: T): void;
}

import { DragDropModule } from '@angular/cdk/drag-drop';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';
import { SecretaryDocumentListComponent } from './secretary-document-list/secretary-document-list.component';
import { SecretaryDocumentBatchComponent } from './secretary-document/secretary-document-batch.component';
import { SecretaryDocumentCreateComponent } from './secretary-document/secretary-document-create.component';
import { SecretaryDocument<PERSON>anualComponent } from './secretary-document/secretary-document-manual.component';
import { SecretaryDocumentPercaseComponent } from './secretary-document/secretary-document-percase.component';

import { ColumnConfigComponent } from './column-config.component';
import { SecretaryDraftTableComponent } from './secretary-document-list/secretary-draft-table.component';
import { SecretarySubmittedTableComponent } from './secretary-document-list/secretary-submitted-table.component';
import { SecretaryPartnerDocumentsNewComponent } from './secretary-partner-document-list/secretary-partner-documents-new.component';
import { SecretaryPartnerDraftTableComponent } from './secretary-partner-document-list/secretary-partner-draft-table.component';
import { SecretaryPartnerSubmittedTableComponent } from './secretary-partner-document-list/secretary-partner-submitted-table.component';
import { SecretaryPartnerDocumentComponent } from './secretary-partner-document/secretary-partner-document.component';
import { secretaryRoutes } from './secretary.routes';

@NgModule({
    declarations: [
        SecretaryDocumentPercaseComponent,
        SecretaryDocumentCreateComponent,
        SecretaryPartnerDocumentComponent,
        SecretaryDocumentManualComponent,
        SecretaryDocumentBatchComponent,
        SecretaryDocumentListComponent,
        SecretaryDraftTableComponent,
        SecretarySubmittedTableComponent,
        SecretaryPartnerDocumentsNewComponent,
        SecretaryPartnerDraftTableComponent,
        SecretaryPartnerSubmittedTableComponent,
        ColumnConfigComponent,
    ],
    imports: [RouterModule.forChild(secretaryRoutes), SharedModule, ReactiveFormsModule, FormsModule, DragDropModule],
})
export class SecretaryModule {}

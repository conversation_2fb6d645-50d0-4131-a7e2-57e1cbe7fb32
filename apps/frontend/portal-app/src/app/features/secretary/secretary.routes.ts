import { Route } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';
import { SecretaryDocumentListComponent } from './secretary-document-list/secretary-document-list.component';
import { SecretaryDocumentBatchComponent } from './secretary-document/secretary-document-batch.component';
import { SecretaryDocumentCreateComponent } from './secretary-document/secretary-document-create.component';
import { SecretaryDocumentManualComponent } from './secretary-document/secretary-document-manual.component';
import { SecretaryDocumentPercaseComponent } from './secretary-document/secretary-document-percase.component';
import { SecretaryPartnerDocumentsNewComponent } from './secretary-partner-document-list/secretary-partner-documents-new.component';
import { SecretaryPartnerDocumentComponent } from './secretary-partner-document/secretary-partner-document.component';

export const secretaryRoutes: Route[] = [
    { path: '', redirectTo: 'documents-list', pathMatch: 'full' },
    {
        path: 'document/create',
        component: SecretaryDocumentCreateComponent,
        data: {
            breadcrumb: [
                {
                    label: '{{PRIMARY_NAVIGATION.UPLOAD_DOCUMENT_LIST}}',
                    url: '/',
                },
                {
                    label: '{{UPLOAD_DOCUMENT_PAGE.TITLE}}',
                    url: '/secretary/document/create',
                },
            ],
        },
        canActivate: [AuthGuard],
        children: [
            { path: '', pathMatch: 'full', redirectTo: 'percase' },
            {
                path: 'percase',
                component: SecretaryDocumentPercaseComponent,
                canActivate: [AuthGuard],
            },
            {
                path: 'batch',
                component: SecretaryDocumentBatchComponent,
                canActivate: [AuthGuard],
            },
            {
                path: 'manual',
                component: SecretaryDocumentManualComponent,
                canActivate: [AuthGuard],
            },
        ],
    },
    {
        path: 'documents-list',
        component: SecretaryDocumentListComponent,
        canActivate: [AuthGuard],
        data: {
            title: 'DOCUMENT_LIST_PAGE.TITLE',
        },
        children: [
            {
                path: 'percase/:documentNo',
                component: SecretaryDocumentPercaseComponent,
                canActivate: [AuthGuard],
            },
            {
                path: 'manual/:documentNo',
                component: SecretaryDocumentManualComponent,
                canActivate: [AuthGuard],
            },
            {
                path: 'batch/:documentNo',
                component: SecretaryDocumentBatchComponent,
                canActivate: [AuthGuard],
            },
        ],
    },
    {
        path: 'documents-list-partner',
        component: SecretaryPartnerDocumentsNewComponent,
        canActivate: [AuthGuard],
        children: [
            {
                path: ':documentNo',
                component: SecretaryPartnerDocumentComponent,
                canActivate: [AuthGuard],
            },
        ],
    },
];

import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { OverlayPanel } from 'primeng/overlaypanel';
import { TableColumn } from './secretary-table-base.component';

@Component({
    selector: 'app-column-config',
    template: `
        <p-overlayPanel #columnConfigOverlay [showCloseIcon]="true" [style]="{ width: '300px' }">
            <div class="flex flex-column gap-3">
                <div
                    class="column-list"
                    cdkDropList
                    (cdkDropListDropped)="columnsDropped.emit($event)"
                    cdkDropListLockAxis="y"
                >
                    <div
                        *ngFor="let column of workingColumns; let i = index"
                        class="flex align-items-center gap-2 mb-2 p-2 border-1 border-300 border-round cursor-move"
                        cdkDrag
                    >
                        <p-checkbox
                            [ngModel]="column.visible"
                            (onChange)="columnVisibilityChanged.emit({ index: i, visible: $event.checked })"
                            [binary]="true"
                        ></p-checkbox>
                        <span class="flex-1">{{ column.label | translate }}</span>
                        <i class="pi pi-arrows-v text-400"></i>
                    </div>
                </div>
                <div class="flex gap-2">
                    <button
                        pButton
                        label="Reset"
                        class="p-button-outlined p-button-sm flex-1"
                        (click)="resetConfiguration.emit()"
                    ></button>
                    <button pButton label="Save" class="p-button-sm flex-1" (click)="saveConfiguration.emit()"></button>
                </div>
            </div>
        </p-overlayPanel>
    `,
    styles: [
        `
            .cdk-drag-preview {
                box-sizing: border-box;
                border-radius: 4px;
                box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14),
                    0 3px 14px 2px rgba(0, 0, 0, 0.12);
            }

            .cdk-drag-placeholder {
                outline: 2px dashed var(--primary-color);
                background: rgba(63, 81, 181, 0.06);
            }

            .cdk-drag-animating {
                transition: transform 500ms cubic-bezier(0, 0, 0.2, 1);
            }

            .column-list.cdk-drop-list-dragging .cdk-drag:not(.cdk-drag-placeholder) {
                transition: transform 500ms cubic-bezier(0, 0, 0.2, 1);
            }

            .column-list .cdk-drag:hover {
                background-color: rgba(0, 0, 0, 0.04);
            }
        `,
    ],
})
export class ColumnConfigComponent {
    @Input() workingColumns: TableColumn[] = [];

    @Output() columnVisibilityChanged = new EventEmitter<{ index: number; visible: boolean }>();
    @Output() resetConfiguration = new EventEmitter<void>();
    @Output() saveConfiguration = new EventEmitter<void>();
    @Output() columnsDropped = new EventEmitter<CdkDragDrop<TableColumn[]>>();

    @ViewChild('columnConfigOverlay') columnConfigOverlay!: OverlayPanel;

    toggle(event: Event) {
        this.columnConfigOverlay.toggle(event);
    }

    hide() {
        this.columnConfigOverlay.hide();
    }
}

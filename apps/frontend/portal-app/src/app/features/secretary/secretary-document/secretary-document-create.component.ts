import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { SecretaryUploadTypeEnum } from '@api-clients/document';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-secretary-document-create',
    template: `
        <sub-head [isSearchVisible]="false">
            <ng-template mTemplate="subHeaderContent">
                <div class="flex flex-row gap-2 align-items-center">
                    <p-selectButton
                        [options]="stateOptions"
                        [formControl]="modeControl"
                        optionLabel="label"
                        optionValue="value"
                    ></p-selectButton>
                </div>
            </ng-template>
        </sub-head>

        <router-outlet></router-outlet>
    `,
})
export class SecretaryDocumentCreateComponent implements OnInit, OnD<PERSON>roy {
    stateOptions: Array<{ label: string; value: string }> = [];

    modeControl = new FormControl<string>(SecretaryUploadTypeEnum.PerCase.toString().toLowerCase(), {
        nonNullable: true,
    });

    private destroy$ = new Subject<void>();

    constructor(private router: Router, private route: ActivatedRoute, private translateService: TranslateService) {}

    ngOnInit(): void {
        this.translateService.get('SECRETARY_PARTNER_DOCUMENTS.UPLOAD_MODE').subscribe((res) => {
            this.stateOptions = [
                {
                    label: res['PER_CASE'],
                    value: SecretaryUploadTypeEnum.PerCase.toString().toLowerCase(),
                },
                {
                    label: res['BATCH'],
                    value: SecretaryUploadTypeEnum.Batch.toString().toLowerCase(),
                },
                {
                    label: res['MANUAL'],
                    value: SecretaryUploadTypeEnum.Manual.toString().toLowerCase(),
                },
            ];
        });
        // Sync control with current route
        this.route.firstChild?.url.pipe(takeUntil(this.destroy$)).subscribe((segments) => {
            const childPath = segments[0]?.path ?? SecretaryUploadTypeEnum.PerCase.toString().toLowerCase();
            const valid = this.stateOptions.some((o) => o.value === childPath);
            this.modeControl.setValue(valid ? childPath : SecretaryUploadTypeEnum.PerCase.toString().toLowerCase(), {
                emitEvent: false,
            });
        });

        // Navigate on selection change
        this.modeControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((val) => {
            this.router.navigate([val], { relativeTo: this.route });
        });
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }
}

import { Component, OnInit, Optional } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FileInfo } from '@api-clients/contracts';
import {
    AccountTypeEnum,
    AttachmentService as DocumentAttachmentService,
    CreateSecretaryDocumentUploadDraftRequest,
    DocumentType,
    EloProcessingStatusEnum,
    SecretaryDocumentService,
    SecretaryDocumentUploadDto,
    SecretaryUploadTypeEnum,
    UpdateSecretaryDocumentUploadDraftRequest,
    UploadStatusEnum,
} from '@api-clients/document';
import { Status } from '@core/enums/async-status.enum';
import { ProjectStateStatus } from '@core/enums/project-state-status.enum';
import { IConfirmationDialogData } from '@core/interfaces/confirmation-dialog.interface';
import { ISecretaryDocumentAttachment } from '@core/interfaces/document-attachment.interface';

import { IProblemDetails } from '@core/interfaces/problem-details.interface';
import { IUploadDialogData } from '@core/interfaces/upload-dialog.interface';
import { BreadcrumbService } from '@core/services/breadcrumb.service';

import { TranslateService } from '@ngx-translate/core';
import { BaseDocumentUploadComponent } from '@shared/components/base-document-upload.component';
import { ConfirmationDialogComponent } from '@shared/components/confirmation-dialog/confirmation-dialog.component';

import { UploadDialogComponent } from '@shared/components/upload-dialog/upload-dialog.component';
import { DialogService, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { BehaviorSubject, catchError, combineLatest, EMPTY, finalize, map, switchMap, take, takeUntil } from 'rxjs';
import { v6 as uuidv6 } from 'uuid';

@Component({
    selector: 'app-secretary-document-batch',
    styles: [
        `
            .parent {
                display: grid;
                grid-template-columns: auto auto 1fr;
                grid-template-rows: repeat(2, auto);
                align-items: center;
                column-gap: 1rem;
                row-gap: 0.3rem;
            }

            .classifications {
                grid-column: 1 / -1;
                grid-row: 2;
            }
        `,
    ],
    template: `
        <market-progress-bar *ngIf="isLoading$ | ngrxPush"></market-progress-bar>

        <base-layout class="fadein animation-duration-250">
            <div class="mt-5"></div>
            <ng-container *ngIf="!errorMessage">
                <p-accordion [multiple]="true">
                    <p-accordionTab
                        header="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.BATCH_TITLE' | translate }}"
                        [selected]="true"
                    >
                        <div class="flex flex-column gap-4 fadein animation-duration-250">
                            <p class="g-body-1 font-bold" *ngIf="isDialogMode">
                                {{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.DOCUMENT_STATUS' | translate }}
                                {{ documentStatus }}
                            </p>
                            <div class="lg:col-5 p-fluid p-0 flex gap-4 flex-column">
                                <span class="p-float-label">
                                    <p-calendar
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.ARRIVAL_DATE' | translate }}"
                                        placeholder="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.ARRIVAL_DATE' | translate }}"
                                        [ngModel]="actualArrivalDate"
                                        (ngModelChange)="onArrivalDateChange($event)"
                                        [disabled]="isSubmitted"
                                        [showClear]="secretaryDateOfArrival !== null"
                                        dateFormat="yy.mm.dd."
                                    ></p-calendar>
                                    <label for="actualArrivalDate">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.ARRIVAL_DATE' | translate
                                    }}</label>
                                </span>
                            </div>
                        </div>
                    </p-accordionTab>
                    <p-accordionTab
                        header="{{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.TITLE' | translate }}"
                        [selected]="true"
                    >
                        <div class="mb-3">
                            <p class="g-body-1 text-gray-600">
                                {{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.MAX' | translate : { max: this.fileCountLimit } }}
                            </p>
                            <p class="g-body-1 text-gray-600">
                                {{
                                    'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.MAX_FILE_SIZE'
                                        | translate : { maxFileSize: this.fileSizeLimitMb }
                                }}
                            </p>
                        </div>
                        <div
                            class="flex flex-column gap-4 fadein animation-duration-250"
                            *ngIf="documentAttachments$ | async as documentAttachments"
                        >
                            <div *ngFor="let attachment of documentAttachments; let i = index">
                                <div class="parent">
                                    <div class="order-number">
                                        <p class="g-body-1 text-gray-600">
                                            {{ i + 1 + '.' }}
                                        </p>
                                    </div>
                                    <div class="trash-button">
                                        <button
                                            type="button"
                                            pButton
                                            icon="pi pi-trash"
                                            class="p-button-rounded p-button-text p-button-danger"
                                            (click)="removeAttachment(i)"
                                            [disabled]="isSubmitted"
                                        ></button>
                                    </div>
                                    <div class="file-preview">
                                        <div *ngIf="attachment" class="flex-grow-1 flex align-items-center gap-2">
                                            <attachment-tile
                                                [isDeleteMode]="!isEditMode"
                                                [attachmentName]="attachment.file.fileName"
                                                [iconVisible]="isEditMode"
                                                [viewVisible]="true"
                                                (iconClicked)="downloadAttachment(attachment.file)"
                                                (viewClicked)="viewAttachment(attachment.file)"
                                            ></attachment-tile>
                                        </div>
                                    </div>
                                    <div class="classifications">
                                        <div
                                            *ngFor="let classification of attachment.classifications"
                                            class="g-body-1 mt-1 font-medium fadein animation-duration-250 flex flex-row align-items-center gap-2"
                                        >
                                            {{ getDocumentTypeLabel(classification.documentType) }}
                                            <span
                                                [ngClass]="{
                                                    'text-green-500':
                                                        classification.processingStatus ===
                                                        EloProcessingStatusEnum.Success,
                                                    'text-red-500':
                                                        classification.processingStatus ===
                                                        EloProcessingStatusEnum.Failed
                                                }"
                                                >{{ classification.spIdentifier }}</span
                                            >

                                            <i
                                                *ngIf="
                                                    classification.processingStatus === EloProcessingStatusEnum.Success
                                                "
                                                class="pi pi-check-circle text-green-500 text-xl"
                                                title="Processing completed successfully"
                                            >
                                            </i>
                                            <i
                                                *ngIf="
                                                    classification.processingStatus === EloProcessingStatusEnum.Failed
                                                "
                                                class="pi pi-times-circle text-red-500 text-xl"
                                                title="Processing failed"
                                            >
                                            </i>
                                            <span
                                                *ngIf="
                                                    classification.processingStatus ===
                                                        EloProcessingStatusEnum.Failed && classification.failureReason
                                                "
                                                class="text-red-500"
                                                >{{ classification.failureReason }}</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div *ngIf="documentAttachments.length === 0">
                                <p class="g-body-1 text-gray-600">
                                    {{ 'UPLOAD_DOCUMENT_PAGE.SECRETARY_INVOICE.NO_ATTACHMENTS' | translate }}
                                </p>
                            </div>

                            <button
                                type="button"
                                pButton
                                class="flex flex-row gap-2 align-items-center p-button p-button-label p-button-text p-0"
                                (click)="openFileuploadModal()"
                                [disabled]="isSubmitted"
                            >
                                <svg
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M7 12L12 12M12 12L17 12M12 12V7M12 12L12 17"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                    <circle
                                        cx="12"
                                        cy="12"
                                        r="9"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                </svg>
                                {{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.ADD_DOCUMENT' | translate }}
                            </button>
                        </div>
                    </p-accordionTab>

                    <p-accordionTab header="{{ 'UPLOAD_DOCUMENT_PAGE.COMMENTS.TITLE' | translate }}" [selected]="true">
                        <div class="pb-4 fadein animation-duration-250">
                            <textarea
                                [(ngModel)]="commentText"
                                pInputTextarea
                                placeholder="{{ 'CREATE_ADDITIONAL_WORK.COMMENT.INPUT_BOX_TIP' | translate }}"
                                class="w-full"
                                maxlength="255"
                                [disabled]="isSubmitted"
                            ></textarea>
                        </div>
                    </p-accordionTab>
                </p-accordion>

                <div
                    class="button-wrapper flex flex-column lg:flex-row gap-4 justify-content-center p-2"
                    *ngIf="!isSubmitted"
                >
                    <button
                        pButton
                        [label]="'UPLOAD_DOCUMENT_PAGE.BUTTONS.SEND' | translate"
                        type="submit"
                        form="myForm"
                        class="px-8"
                        (click)="submitDocument()"
                        [disabled]="!isFormValid()"
                        [loading]="submitLoading$ | ngrxPush"
                    ></button>
                    <button
                        pButton
                        class="p-button-outlined px-8"
                        (click)="discardChanges()"
                        [loading]="discardLoading$ | ngrxPush"
                    >
                        {{ 'UPLOAD_DOCUMENT_PAGE.BUTTONS.DISCARD' | translate }}
                    </button>
                    <button
                        pButton
                        class="p-button-outlined px-8"
                        (click)="saveWithoutSending()"
                        [disabled]="!isFormValid()"
                        [loading]="saveLoading$ | ngrxPush"
                    >
                        {{ 'UPLOAD_DOCUMENT_PAGE.BUTTONS.SAVE_WITHOUT_SENDING' | translate }}
                    </button>
                </div>
            </ng-container>
            <p *ngIf="isSubmitted" class="g-body-1 mt-4 text-gray-600">
                {{ 'UPLOAD_DOCUMENT_PAGE.SECRETARY_INVOICE.UPLOAD_IDENTIFIER' | translate : { contextId } }}
            </p>
            <error-feedback-card *ngIf="errorMessage !== null" [errorMessage]="errorMessage"></error-feedback-card>
        </base-layout>
    `,
})
export class SecretaryDocumentBatchComponent extends BaseDocumentUploadComponent implements OnInit {
    isDialogMode = false;
    fileCountLimit: number;
    fileSizeLimit: number;
    permittedExtensions: string[] = ['.pdf'];
    attachments$ = new BehaviorSubject<FileInfo[]>([]);
    documentAttachments$ = new BehaviorSubject<ISecretaryDocumentAttachment[]>([]);
    isEditMode = false;
    isSubmitted = false;
    existingDocument: SecretaryDocumentUploadDto | null = null;
    errorMessage: string | null = null;
    commentText = '';
    isLoadingDocument$ = new BehaviorSubject<boolean>(false);
    isLoadingDateOfArrival$ = new BehaviorSubject<boolean>(false);
    submitLoading$ = new BehaviorSubject<boolean>(false);
    saveLoading$ = new BehaviorSubject<boolean>(false);
    discardLoading$ = new BehaviorSubject<boolean>(false);

    get DocumentType() {
        return DocumentType;
    }

    get fileSizeLimitMb() {
        return this.fileSizeLimit / 1024 / 1024;
    }

    isLoading$ = combineLatest([this.isLoadingDocument$, this.isLoadingDateOfArrival$]).pipe(
        map(([isLoadingDocument, isLoadingDateOfArrival]) => isLoadingDocument || isLoadingDateOfArrival)
    );

    dateOfArrival: Date | null = null;
    secretaryDateOfArrival: Date | null = null;

    protected contextId: string | null = null;
    get ProjectStateStatus() {
        return ProjectStateStatus;
    }

    get AccountTypeEnum() {
        return AccountTypeEnum;
    }

    get Status() {
        return Status;
    }

    get EloProcessingStatusEnum() {
        return EloProcessingStatusEnum;
    }

    get actualArrivalDate() {
        return this.secretaryDateOfArrival ?? this.dateOfArrival;
    }

    get documentStatus(): string {
        if (!this.existingDocument) return '-';
        return this.existingDocument.status === UploadStatusEnum.Draft
            ? this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.DRAFT')
            : this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.SUBMITTED');
    }

    documentTypes: Record<DocumentType, string>;

    constructor(
        protected translateService: TranslateService,
        protected dialogService: DialogService,
        protected documentAttachmentService: DocumentAttachmentService,
        private secretaryDocumentService: SecretaryDocumentService,
        protected router: Router,
        private route: ActivatedRoute,
        private breadcrumbService: BreadcrumbService,
        @Optional() private dynamicDialogConfig?: DynamicDialogConfig
    ) {
        super();
    }

    onArrivalDateChange(date: Date) {
        this.secretaryDateOfArrival = date;
    }

    ngOnInit(): void {
        this.isDialogMode = !!this.dynamicDialogConfig?.data?.documentNo;
        this.documentAttachmentService.getAttachmentConfigs().subscribe((res) => {
            this.fileCountLimit = res.fileCountLimit ?? 25;
            this.fileSizeLimit = res.fileSizeLimit ?? 5242880;
        });

        if (this.dynamicDialogConfig?.data?.documentNo) {
            this.isEditMode = true;
            this.contextId = this.dynamicDialogConfig.data.documentNo;
            this.loadExistingDocument();
            this.dynamicDialogConfig.header = this.translateService.instant('UPLOAD_DOCUMENT_PAGE.DETAILS.BATCH_TITLE');
        } else {
            this.route.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
                if (params['documentNo']) {
                    this.isEditMode = true;
                    this.contextId = params['documentNo'];
                    this.loadExistingDocument();
                } else {
                    this.contextId = uuidv6();
                    this.loadDateOfArrival();
                }
            });
        }

        this.translateService.get('UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.DOCUMENT_TYPE_SELECT').subscribe((res) => {
            this.documentTypes = {
                [DocumentType.Invoice]: res.DOCUMENT_TYPE_1,
                [DocumentType.CompletionCert]: res.DOCUMENT_TYPE_2,
                [DocumentType.DeliveryNote]: res.DOCUMENT_TYPE_3,
                [DocumentType.Waybill]: res.DOCUMENT_TYPE_4,
                [DocumentType.Order]: res.DOCUMENT_TYPE_5,
                [DocumentType.Contract]: res.DOCUMENT_TYPE_6,
                [DocumentType.CorrectionInvoice]: res.DOCUMENT_TYPE_7,
                [DocumentType.AdvanceInvoice]: res.DOCUMENT_TYPE_8,
                [DocumentType.ReceiptConfirmation]: res.DOCUMENT_TYPE_9,
                [DocumentType.Other]: res.DOCUMENT_TYPE_10,
            };
        });
    }

    loadDateOfArrival() {
        this.isLoadingDateOfArrival$.next(true);
        this.secretaryDocumentService
            .getSecretaryDocumentDateOfArrival()
            .pipe(take(1))
            .subscribe((res) => {
                this.dateOfArrival = res.dateOfArrival ? new Date(res.dateOfArrival) : null;
                this.isLoadingDateOfArrival$.next(false);
            });
    }

    getDocumentTypeLabel(documentType: DocumentType): string {
        return this.documentTypes[documentType];
    }

    loadExistingDocument(): void {
        this.isLoadingDocument$.next(true);

        this.secretaryDocumentService
            .getSecretaryDocumentUpload(this.contextId)
            .pipe(
                switchMap((document) => {
                    this.setDocument(document);
                    return EMPTY;
                }),
                finalize(() => {
                    this.isLoadingDocument$.next(false);
                })
            )
            .pipe(
                catchError((err: IProblemDetails) => {
                    this.isLoadingDocument$.next(false);
                    this.errorMessage = err.detail;
                    return EMPTY;
                })
            )
            .subscribe();
    }

    private setDocument(document: SecretaryDocumentUploadDto) {
        this.existingDocument = document;
        this.commentText = document.comment || '';
        const attachments: ISecretaryDocumentAttachment[] = [];
        document.attachmentDocumentTypes.forEach((entry) => {
            attachments.push({
                file: entry.attachment,
                classifications: entry.classifications,
            });
        });

        this.documentAttachments$.next(attachments);
        this.dateOfArrival = document.dateOfArrival ? new Date(document.dateOfArrival) : null;
        this.secretaryDateOfArrival =
            document.secretaryDateOfArrival != null ? new Date(document.secretaryDateOfArrival) : null;

        if (document.status === UploadStatusEnum.Submitted) {
            this.isSubmitted = true;
        }
    }

    removeAttachment(index: number) {
        const currentAttachments = this.documentAttachments$.value;
        if (currentAttachments[index]) {
            this.documentAttachmentService
                .revertFile(this.contextId, currentAttachments[index].file.fileId)
                .pipe(take(1))
                .subscribe();
        }
        currentAttachments.splice(index, 1);
        this.documentAttachments$.next(currentAttachments);
    }

    openFileuploadModal() {
        const ref = this.dialogService.open(UploadDialogComponent, {
            width: '40rem',
            showHeader: false,
            dismissableMask: false,
            data: <IUploadDialogData>{
                accept: this.permittedExtensions,
                maxFileSizeInBytes: this.fileSizeLimit,
                contextObjectId: this.contextId,
                uploadFileFn: (contextObjectId: string, files: File[]) =>
                    this.documentAttachmentService.uploadFile(contextObjectId, files, 'events', true),
                revertFileFn: (contextObjectId: string, fileId: number) =>
                    this.documentAttachmentService.revertFile(contextObjectId, fileId),
            },
        });
        return ref.onClose.pipe(takeUntil(this.destroy$)).subscribe((res: FileInfo[]) => {
            if (res && res.length > 0) {
                const currentAttachments = this.documentAttachments$.value;
                this.documentAttachments$.next([...currentAttachments, ...res.map((file) => ({ file }))]);
            }
        });
    }

    isFormValid(): boolean {
        return this.documentAttachments$.value.length > 0;
    }

    submitDocument() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SUBMIT_CONFIRMATION_MESSAGE'),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            this.submitLoading$.next(true);
            if (this.isEditMode) {
                const request = this.getUpdateDocumentUploadDraftRequest();
                this.secretaryDocumentService
                    .submitSecretaryDocumentUpload(this.contextId, request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.submitLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
                                onButton1Click: () => {
                                    this.router.navigate(['/secretary/document/create/batch']);
                                },
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                                isSuccess: true,
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                const request = this.getCreateDocumentUploadDraftRequest();
                this.secretaryDocumentService
                    .createAndSubmitSecretaryDocument(request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.submitLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            }
        };
    }

    getUpdateDocumentUploadDraftRequest(): UpdateSecretaryDocumentUploadDraftRequest {
        return {
            rowVersion: this.existingDocument?.rowVersion,
            secretaryDateOfArrival: this.secretaryDateOfArrival?.toLocaleDateString('hu-HU'),
            attachmentDocumentTypes: this.documentAttachments$.value.map((attachment) => ({
                attachment: attachment.file,
            })),
            comment: this.commentText.trim(),
        };
    }

    getCreateDocumentUploadDraftRequest(): CreateSecretaryDocumentUploadDraftRequest {
        return {
            contextId: this.contextId,
            dateOfArrival: this.dateOfArrival?.toLocaleDateString('hu-HU'),
            secretaryDateOfArrival: this.secretaryDateOfArrival?.toLocaleDateString('hu-HU'),
            secretaryUploadType: SecretaryUploadTypeEnum.Batch,
            attachmentDocumentTypes: this.documentAttachments$.value.map((attachment) => ({
                attachment: attachment.file,
            })),
            comment: this.commentText.trim(),
        };
    }
    /**
     * Discard all changes and reset the form
     */
    discardChanges() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.DISCARD_CONFIRMATION'),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            this.discardLoading$.next(true);
            if (this.isEditMode) {
                this.secretaryDocumentService
                    .removeSecretaryDocumentUpload(this.contextId)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.discardLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                this.discardLoading$.next(false);
                this.documentAttachmentService.revertFiles(this.contextId).pipe(take(1)).subscribe();
                this.reloadPage();
            }
        };
    }

    saveWithoutSending() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SAVE_CONFIRMATION_MESSAGE'),
                description: this.translateService.instant(
                    'UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SAVE_CONFIRMATION_DESCRIPTION'
                ),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            this.saveLoading$.next(true);
            if (this.isEditMode) {
                const request = this.getUpdateDocumentUploadDraftRequest();
                this.secretaryDocumentService
                    .updateSecretaryDocumentUploadDraft(this.contextId, request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.saveLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: (res) => {
                            this.setDocument(res);
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_BUTTON',
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                                isSuccess: true,
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                const request = this.getCreateDocumentUploadDraftRequest();
                this.secretaryDocumentService
                    .createSecretaryDocumentUploadDraft(request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.saveLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: (res) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_DESCRIPTION',
                                    { spIdentifier: res.spIdentifier }
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            }
        };
    }
}

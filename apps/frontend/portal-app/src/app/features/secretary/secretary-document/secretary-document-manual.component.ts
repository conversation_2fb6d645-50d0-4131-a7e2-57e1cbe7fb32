import { Component, OnInit, Optional } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ContractService, FileInfo, ProjectService } from '@api-clients/contracts';
import {
    AccountTypeEnum,
    AttachmentDocumentTypeDto,
    AttachmentService as DocumentAttachmentService,
    CreateSecretaryManualDocumentUploadDraftRequest,
    DocumentType,
    EloProcessingStatusEnum,
    PartnersService,
    SecretaryDocumentService,
    SecretaryManualDocumentUploadDto,
    UpdateSecretaryManualDocumentUploadDraftRequest,
    UploadStatusEnum,
} from '@api-clients/document';
import { Status } from '@core/enums/async-status.enum';
import { IConfirmationDialogData } from '@core/interfaces/confirmation-dialog.interface';
import { IPartnerDocumentAttachment } from '@core/interfaces/document-attachment.interface';
import { IDropdownItem } from '@core/interfaces/dropdown-item.interface';

import { IProblemDetails } from '@core/interfaces/problem-details.interface';
import { BreadcrumbService } from '@core/services/breadcrumb.service';
import { DocumentTypesService } from '@core/services/document-types.service';

import { TranslateService } from '@ngx-translate/core';
import { BaseManualDocumentUploadComponent } from '@shared/components/base-manual-document-upload.component';
import { ConfirmationDialogComponent } from '@shared/components/confirmation-dialog/confirmation-dialog.component';

import { DialogService, DynamicDialogConfig } from 'primeng/dynamicdialog';
import {
    BehaviorSubject,
    catchError,
    combineLatest,
    debounceTime,
    distinctUntilChanged,
    EMPTY,
    finalize,
    map,
    Observable,
    of,
    switchMap,
    take,
    takeUntil,
} from 'rxjs';
import { v6 as uuidv6 } from 'uuid';

@Component({
    selector: 'app-secretary-document-manual',
    styles: [
        `
            .parent {
                display: grid;
                grid-template-columns: auto auto minmax(280px, 1fr) auto auto;
                grid-template-rows: repeat(2, auto);
                row-gap: 5px;
                column-gap: 10px;
                align-items: center;

                @media (max-width: 62em) {
                    grid-template-columns: auto auto 1fr;
                    grid-template-rows: repeat(3, auto);
                }
            }

            .order-number {
                grid-column: 1;
                grid-row: 1;
            }

            .trash-button {
                grid-column: 2;
                grid-row: 1;
            }

            .document-type {
                grid-column: 3;
                grid-row: 1;
            }

            .file-preview {
                grid-column: 4;
                grid-row: 1;

                @media (max-width: 62em) {
                    grid-column: 1 / 4;
                    grid-row: 3;
                }
            }
            .sp-identifier {
                grid-column: 2 / -1;
                grid-row: span 2;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
        `,
    ],
    template: `
        <market-progress-bar *ngIf="isLoading$ | ngrxPush"></market-progress-bar>
        <base-layout class="fadein animation-duration-250">
            <div class="mt-5"></div>
            <ng-container *ngIf="!errorMessage">
                <p-accordion [multiple]="true">
                    <p-accordionTab
                        header="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.MANUAL_TITLE' | translate }}"
                        [selected]="true"
                    >
                        <div class="flex flex-column gap-4 fadein animation-duration-250">
                            <p class="g-body-1 font-bold" *ngIf="isDialogMode">
                                {{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.DOCUMENT_STATUS' | translate }}
                                {{ documentStatus }}
                            </p>
                            <div class="lg:col-5 p-fluid p-0 flex gap-4 flex-column">
                                <span class="p-float-label">
                                    <p-autoComplete
                                        [suggestions]="partnerDropdownItems$ | ngrxPush"
                                        field="label"
                                        dataKey="value"
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.PARTNER_SELECTION' | translate }}"
                                        placeholder="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.PARTNER_SELECTION' | translate }}"
                                        [ngModel]="selectedPartner.value"
                                        (ngModelChange)="onSelectedPartnerChange($event)"
                                        [disabled]="isSubmitted"
                                        [showClear]="true"
                                        (completeMethod)="onPartnerFilter($event)"
                                        (onClear)="onPartnerClear()"
                                    ></p-autoComplete>
                                    <label for="partnerSelection">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.PARTNER_SELECTION' | translate
                                    }}</label>
                                </span>
                                <span class="p-float-label">
                                    <p-dropdown
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.PROJECT_SELECTION' | translate }}"
                                        placeholder="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.PROJECT_SELECTION' | translate }}"
                                        [options]="projectsDropdownItems$ | ngrxPush"
                                        [ngModel]="selectedProjectNo.value"
                                        (ngModelChange)="onSelectedProjectNoChange($event)"
                                        [disabled]="isSubmitted"
                                        [showClear]="true"
                                        [filter]="true"
                                        filterBy="label"
                                    ></p-dropdown>
                                    <label for="projectSelection">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.PROJECT_SELECTION' | translate
                                    }}</label>
                                </span>
                                <span class="p-float-label">
                                    <p-dropdown
                                        [options]="contractsDropdownItems$ | ngrxPush"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.CONTRACT_SELECTION' | translate }}"
                                        placeholder="{{
                                            'UPLOAD_DOCUMENT_PAGE.DETAILS.CONTRACT_SELECTION' | translate
                                        }}"
                                        [ngModel]="selectedContractNo.value"
                                        (ngModelChange)="selectedContractNo.next($event)"
                                        [disabled]="isSubmitted"
                                        [showClear]="true"
                                        [filter]="true"
                                        filterBy="label"
                                    ></p-dropdown>
                                    <label for="contractSelection">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.CONTRACT_SELECTION' | translate
                                    }}</label>
                                </span>
                                <span class="p-float-label">
                                    <p-dropdown
                                        [options]="invoiceTypes"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.INVOICE_TYPE' | translate }}"
                                        placeholder="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.INVOICE_TYPE' | translate }}"
                                        [ngModel]="selectedInvoiceType"
                                        (ngModelChange)="onInvoiceTypeChange($event)"
                                        [disabled]="isSubmitted"
                                    ></p-dropdown>
                                    <label for="invoiceType" class="required-field">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.INVOICE_TYPE' | translate
                                    }}</label>
                                </span>
                                <span class="p-float-label">
                                    <p-calendar
                                        class="p-fluid"
                                        name="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.ARRIVAL_DATE' | translate }}"
                                        placeholder="{{ 'UPLOAD_DOCUMENT_PAGE.DETAILS.ARRIVAL_DATE' | translate }}"
                                        [ngModel]="actualArrivalDate"
                                        (ngModelChange)="onArrivalDateChange($event)"
                                        [disabled]="isSubmitted"
                                        [showClear]="secretaryDateOfArrival !== null"
                                        dateFormat="yy.mm.dd."
                                    ></p-calendar>
                                    <label for="actualArrivalDate">{{
                                        'UPLOAD_DOCUMENT_PAGE.DETAILS.ARRIVAL_DATE' | translate
                                    }}</label>
                                </span>
                            </div>
                        </div>
                    </p-accordionTab>
                    <p-accordionTab
                        header="{{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.TITLE' | translate }}"
                        [selected]="true"
                    >
                        <div
                            class="flex flex-column gap-4 fadein animation-duration-250"
                            *ngIf="documentAttachments$ | async as documentAttachments"
                        >
                            <div>
                                <p class="g-body-1 text-gray-600">
                                    {{
                                        'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.MAX'
                                            | translate : { max: this.fileCountLimit }
                                    }}
                                </p>
                                <p class="g-body-1 text-gray-600">
                                    {{
                                        'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.MAX_FILE_SIZE'
                                            | translate : { maxFileSize: this.fileSizeLimitMb }
                                    }}
                                </p>
                            </div>

                            <p-message
                                class="fadein"
                                *ngIf="selectedInvoiceType === AccountTypeEnum.TigInvoice"
                                severity="info"
                                [escape]="false"
                                [text]="'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.TIG_INVOICE_REQUIREMENTS' | translate"
                            ></p-message>

                            <div
                                *ngFor="let attachment of documentAttachments; let i = index"
                                class="flex flex-row gap-2 align-items-center"
                            >
                                <div class="parent">
                                    <div class="order-number">
                                        <p class="g-body-1 text-gray-600">
                                            {{ i + 1 + '.' }}
                                        </p>
                                    </div>
                                    <div class="trash-button">
                                        <button
                                            type="button"
                                            pButton
                                            icon="pi pi-trash"
                                            class="p-button-rounded p-button-text p-button-danger"
                                            (click)="removeAttachment(i)"
                                            [disabled]="isSubmitted"
                                        ></button>
                                    </div>
                                    <div class="document-type">
                                        <span class="p-float-label">
                                            <p-dropdown
                                                [options]="attachment.availableDocumentTypes || documentTypes"
                                                optionLabel="label"
                                                optionValue="value"
                                                class="p-fluid"
                                                [(ngModel)]="attachment.documentType"
                                                placeholder="{{
                                                    'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.DOCUMENT_TYPE' | translate
                                                }}"
                                                (onChange)="onDocumentTypeChange(i)"
                                                [disabled]="isSubmitted"
                                            ></p-dropdown>

                                            <label for="documentType" class="required-field">{{
                                                'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.DOCUMENT_TYPE' | translate
                                            }}</label>
                                        </span>
                                    </div>
                                    <div class="upload-button">
                                        <button
                                            *ngIf="!attachment.file && attachment.documentType && !isSubmitted"
                                            type="button"
                                            pButton
                                            icon="pi pi-upload"
                                            class="p-button-rounded p-button-text"
                                            (click)="openFileuploadModal(i)"
                                            pTooltip="{{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.UPLOAD_FILE' | translate }}"
                                        ></button>
                                    </div>
                                    <div class="file-preview">
                                        <div *ngIf="attachment.file" class="flex align-items-center gap-2 mt-2 lg:mt-0">
                                            <attachment-tile
                                                [isDeleteMode]="!isEditMode"
                                                [attachmentName]="attachment.file.fileName"
                                                [iconVisible]="isEditMode"
                                                [viewVisible]="true"
                                                (iconClicked)="downloadAttachment(attachment.file)"
                                                (viewClicked)="viewAttachment(attachment.file)"
                                            ></attachment-tile>
                                        </div>
                                    </div>
                                    <div class="sp-identifier">
                                        <div
                                            *ngIf="
                                                attachment.documentType === DocumentType.Invoice &&
                                                attachment.spIdentifier
                                            "
                                            class="g-body-1 mt-1 font-medium fadein animation-duration-250"
                                        >
                                            {{ 'SECRETARY_PARTNER_DOCUMENTS.DETAILS.SP_NUMBER' | translate }}
                                            <span
                                                [ngClass]="{
                                                    'text-green-500':
                                                        attachment.processingStatus === EloProcessingStatusEnum.Success,
                                                    'text-red-500':
                                                        attachment.processingStatus === EloProcessingStatusEnum.Failed
                                                }"
                                                >{{ attachment.spIdentifier }}</span
                                            >
                                        </div>
                                        <i
                                            *ngIf="attachment.processingStatus === EloProcessingStatusEnum.Success"
                                            class="pi pi-check-circle text-green-500 text-xl"
                                            title="Processing completed successfully"
                                        >
                                        </i>
                                        <i
                                            *ngIf="attachment.processingStatus === EloProcessingStatusEnum.Failed"
                                            class="pi pi-times-circle text-red-500 text-xl"
                                            title="Processing failed"
                                        >
                                        </i>
                                        <span
                                            *ngIf="
                                                attachment.processingStatus === EloProcessingStatusEnum.Failed &&
                                                attachment.failureReason
                                            "
                                            class="text-red-500"
                                            >{{ attachment.failureReason }}</span
                                        >
                                    </div>
                                </div>
                            </div>

                            <button
                                type="button"
                                pButton
                                class="flex flex-row gap-2 align-items-center p-button p-button-label p-button-text p-0"
                                (click)="addNewAttachment()"
                                [disabled]="
                                    isSubmitted ||
                                    (this.fileCountLimit <= documentAttachments.length &&
                                        this.fileCountLimit !== null) ||
                                    !this.isLastAttachmentComplete(documentAttachments)
                                "
                            >
                                <svg
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M7 12L12 12M12 12L17 12M12 12V7M12 12L12 17"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                    <circle
                                        cx="12"
                                        cy="12"
                                        r="9"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                </svg>
                                {{ 'UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.ADD_DOCUMENT' | translate }}
                            </button>
                        </div>
                    </p-accordionTab>

                    <p-accordionTab header="{{ 'UPLOAD_DOCUMENT_PAGE.COMMENTS.TITLE' | translate }}" [selected]="true">
                        <div class="pb-4 fadein animation-duration-250">
                            <textarea
                                [(ngModel)]="commentText"
                                pInputTextarea
                                placeholder="{{ 'CREATE_ADDITIONAL_WORK.COMMENT.INPUT_BOX_TIP' | translate }}"
                                class="w-full"
                                maxlength="255"
                                [disabled]="isSubmitted"
                            ></textarea>
                        </div>
                    </p-accordionTab>
                </p-accordion>

                <div
                    class="button-wrapper flex flex-column lg:flex-row gap-4 justify-content-center p-2"
                    *ngIf="!isSubmitted"
                >
                    <button
                        pButton
                        [label]="'UPLOAD_DOCUMENT_PAGE.BUTTONS.SEND' | translate"
                        type="submit"
                        form="myForm"
                        class="px-8"
                        (click)="submitDocument()"
                        [loading]="submitLoading$ | ngrxPush"
                        [disabled]="!isFormValid(true)"
                    ></button>
                    <button
                        pButton
                        class="p-button-outlined px-8"
                        (click)="discardChanges()"
                        [loading]="discardLoading$ | ngrxPush"
                    >
                        {{ 'UPLOAD_DOCUMENT_PAGE.BUTTONS.DISCARD' | translate }}
                    </button>
                    <button
                        pButton
                        class="p-button-outlined px-8"
                        (click)="saveWithoutSending()"
                        [disabled]="!isFormValid(false)"
                        [loading]="saveLoading$ | ngrxPush"
                    >
                        {{ 'UPLOAD_DOCUMENT_PAGE.BUTTONS.SAVE_WITHOUT_SENDING' | translate }}
                    </button>
                </div>
            </ng-container>
            <p *ngIf="isSubmitted" class="g-body-1 mt-4 text-gray-600">
                {{ 'UPLOAD_DOCUMENT_PAGE.SECRETARY_INVOICE.UPLOAD_IDENTIFIER' | translate : { contextId } }}
            </p>
            <error-feedback-card *ngIf="errorMessage !== null" [errorMessage]="errorMessage"></error-feedback-card>
        </base-layout>
    `,
})
export class SecretaryDocumentManualComponent extends BaseManualDocumentUploadComponent implements OnInit {
    isDialogMode = false;
    invoiceTypes: IDropdownItem[] = [];
    fileCountLimit: number;
    fileSizeLimit: number;

    get fileSizeLimitMb() {
        return this.fileSizeLimit / 1024 / 1024;
    }

    permittedExtensions: string[];
    attachments$ = new BehaviorSubject<FileInfo[]>([]);
    documentAttachments$ = new BehaviorSubject<IPartnerDocumentAttachment[]>([]);
    partnerDropdownItems$: Observable<IDropdownItem[]>;
    projectsDropdownItems$: Observable<IDropdownItem[]>;
    contractsDropdownItems$: Observable<IDropdownItem[]>;
    documentTypes: IDropdownItem[] = [];
    selectedInvoiceType: AccountTypeEnum | null = null;
    selectedPartner = new BehaviorSubject<IDropdownItem | null>(null);
    partnerFilter = new BehaviorSubject<string | null>(null);
    isLoadingPartners$ = new BehaviorSubject<boolean>(false);
    selectedProjectNo = new BehaviorSubject<string | null>(null);
    selectedContractNo = new BehaviorSubject<string | null>(null);
    isEditMode = false;
    isSubmitted = false;
    existingDocument: SecretaryManualDocumentUploadDto | null = null;
    errorMessage: string | null = null;
    commentText = '';
    pageTitle = '';
    projectDescription = '';
    isLoadingContracts$ = new BehaviorSubject<boolean>(false);
    isLoadingDocument$ = new BehaviorSubject<boolean>(false);
    submitLoading$ = new BehaviorSubject<boolean>(false);
    saveLoading$ = new BehaviorSubject<boolean>(false);
    discardLoading$ = new BehaviorSubject<boolean>(false);
    isLoadingDateOfArrival$ = new BehaviorSubject<boolean>(false);
    isLoadingProjects$ = new BehaviorSubject<boolean>(false);
    dateOfArrival: Date | null = null;
    secretaryDateOfArrival: Date | null = null;
    onArrivalDateChange(date: Date) {
        this.secretaryDateOfArrival = date;
    }

    isLoading$ = combineLatest([
        this.isLoadingContracts$,
        this.isLoadingDocument$,
        this.isLoadingPartners$,
        this.isLoadingProjects$,
    ]).pipe(
        map(
            ([isLoadingContracts, isLoadingDocument, isLoadingPartners, isLoadingProjects]) =>
                isLoadingContracts || isLoadingDocument || isLoadingPartners || isLoadingProjects
        )
    );

    protected contextId: string | null = null;

    get AccountTypeEnum() {
        return AccountTypeEnum;
    }

    get Status() {
        return Status;
    }

    get DocumentType() {
        return DocumentType;
    }

    get actualArrivalDate() {
        return this.secretaryDateOfArrival ?? this.dateOfArrival;
    }

    get EloProcessingStatusEnum() {
        return EloProcessingStatusEnum;
    }

    get documentStatus(): string {
        if (!this.existingDocument) return '-';
        return this.existingDocument.status === UploadStatusEnum.Draft
            ? this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.DRAFT')
            : this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.SUBMITTED');
    }

    constructor(
        protected translateService: TranslateService,
        protected dialogService: DialogService,
        protected documentAttachmentService: DocumentAttachmentService,
        protected router: Router,
        private route: ActivatedRoute,
        private contractService: ContractService,
        private breadcrumbService: BreadcrumbService,
        private documentTypesService: DocumentTypesService,
        private partnersService: PartnersService,
        private projectService: ProjectService,
        private secretaryDocumentService: SecretaryDocumentService,
        @Optional() private dynamicDialogConfig?: DynamicDialogConfig
    ) {
        super();
    }

    ngOnInit(): void {
        this.documentAttachmentService
            .getAttachmentConfigs()
            .pipe(take(1))
            .subscribe((res) => {
                this.fileCountLimit = res.fileCountLimit ?? 25;
                this.fileSizeLimit = res.fileSizeLimit ?? 5242880;
                this.permittedExtensions = res.permittedExtensions ?? ['.pdf', '.jpeg', '.png', '.jpg'];
            });

        this.documentTypesService
            .getInvoiceTypesDropdown()
            .pipe(take(1))
            .subscribe((res) => {
                this.invoiceTypes = res;
            });

        this.documentTypesService
            .getDocumentTypesDropdown()
            .pipe(take(1))
            .subscribe((res) => {
                this.documentTypes = res;
            });

        this.partnerDropdownItems$ = this.partnerFilter.pipe(
            distinctUntilChanged(),
            debounceTime(300),
            switchMap((filter) => {
                if (filter) {
                    this.isLoadingPartners$.next(true);
                    return this.partnersService.getPartners(filter).pipe(
                        map(({ partners }) => {
                            if (this.selectedPartner.value?.value) {
                                const selectedPartner = partners.find(
                                    (partner) => partner.contactNo === this.selectedPartner.value.value
                                );
                                if (selectedPartner) {
                                    const vatNumber =
                                        selectedPartner.vatRegistrationNo?.trim() ||
                                        selectedPartner.euVatRegistrationNo?.trim();
                                    this.selectedPartner.next({
                                        label: selectedPartner.name + (vatNumber ? ' (' + vatNumber + ')' : ''),
                                        value: selectedPartner.contactNo,
                                    } as IDropdownItem);
                                }
                            }
                            this.isLoadingPartners$.next(false);
                            return partners.map((partner) => {
                                const vatNumber =
                                    partner.vatRegistrationNo?.trim() || partner.euVatRegistrationNo?.trim();
                                return {
                                    label: partner.name + (vatNumber ? ' (' + vatNumber + ')' : ''),
                                    value: partner.contactNo,
                                } as IDropdownItem;
                            });
                        }),
                        finalize(() => {
                            this.isLoadingPartners$.next(false);
                        })
                    );
                }
                return of([]);
            })
        );

        this.projectsDropdownItems$ = this.selectedPartner.pipe(
            distinctUntilChanged((prev, curr) => prev?.value === curr?.value),
            switchMap((partner) => {
                if (partner?.value) {
                    this.isLoadingProjects$.next(true);
                    return this.projectService.getProjects(partner.value as string).pipe(
                        map((res) => {
                            this.isLoadingProjects$.next(false);
                            return res.projects.map((project) => ({
                                label: project.projectNo + ' - ' + project.description,
                                value: project.projectNo,
                                description: project.description,
                            }));
                        }),
                        finalize(() => {
                            this.isLoadingProjects$.next(false);
                        })
                    );
                }
                return of([]);
            })
        );

        this.contractsDropdownItems$ = combineLatest([this.selectedProjectNo, this.selectedPartner]).pipe(
            distinctUntilChanged((prev, curr) => prev[0] === curr[0] && prev[1]?.value === curr[1]?.value),
            takeUntil(this.destroy$),
            switchMap(([projectNo, partner]) => {
                if (projectNo && partner?.value) {
                    this.isLoadingContracts$.next(true);
                    return this.contractService.getContracts(partner.value as string, projectNo).pipe(
                        map((res) => {
                            this.isLoadingContracts$.next(false);
                            return res.contractViewModels.map(
                                (contract) =>
                                    ({ label: contract.documentNo, value: contract.documentNo } as IDropdownItem)
                            );
                        }),
                        finalize(() => {
                            this.isLoadingContracts$.next(false);
                        })
                    );
                }
                return of([]);
            })
        );

        if (this.dynamicDialogConfig?.data?.documentNo) {
            this.isEditMode = true;
            this.contextId = this.dynamicDialogConfig.data.documentNo;
            this.loadExistingDocument();
            this.isDialogMode = true;
            this.dynamicDialogConfig.header = this.translateService.instant(
                'UPLOAD_DOCUMENT_PAGE.DETAILS.MANUAL_TITLE'
            );
        } else {
            this.route.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
                if (params['documentNo']) {
                    this.isEditMode = true;
                    this.contextId = params['documentNo'];
                    this.loadExistingDocument();
                } else {
                    // Initialize with one empty attachment row for create mode
                    this.contextId = uuidv6();
                    this.addNewAttachment();
                    this.loadDateOfArrival();
                }
            });
        }
    }

    onSelectedProjectNoChange(projectNo: string | null) {
        if (!projectNo) {
            this.selectedContractNo.next(null);
            return;
        }
        this.selectedProjectNo.next(projectNo);
        this.selectedContractNo.next(null);
        this.projectsDropdownItems$.pipe(take(1)).subscribe((items) => {
            const selectedItem = items.find((item) => item.value === projectNo);
            this.projectDescription = selectedItem ? selectedItem['description'] : '';
        });
    }

    onSelectedPartnerChange(partner: IDropdownItem) {
        this.selectedPartner.next(partner);
        this.selectedProjectNo.next(null);
        this.selectedContractNo.next(null);
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onPartnerFilter(event: any) {
        this.partnerFilter.next(event.query);
    }

    onPartnerClear() {
        this.selectedPartner.next(null);
        this.selectedProjectNo.next(null);
        this.selectedContractNo.next(null);
    }

    /**
     * Load existing document data when in edit mode
     */
    loadExistingDocument(): void {
        this.isLoadingDocument$.next(true);
        this.secretaryDocumentService
            .getSecretaryManualDocumentUpload(this.contextId)
            .pipe(
                switchMap((document) => {
                    this.setDocument(document);

                    if (document.status === UploadStatusEnum.Submitted) {
                        this.isSubmitted = true;
                    }

                    return EMPTY;
                }),
                finalize(() => {
                    this.isLoadingDocument$.next(false);
                }),
                catchError((err: IProblemDetails) => {
                    this.isLoadingDocument$.next(false);
                    this.errorMessage = err.detail;
                    return EMPTY;
                })
            )
            .subscribe();
    }

    loadDateOfArrival() {
        this.isLoadingDateOfArrival$.next(true);
        this.secretaryDocumentService.getSecretaryDocumentDateOfArrival().subscribe((res) => {
            this.dateOfArrival = res.dateOfArrival ? new Date(res.dateOfArrival) : null;
            this.isLoadingDateOfArrival$.next(false);
        });
    }

    private setDocument(document: SecretaryManualDocumentUploadDto) {
        this.existingDocument = document;
        this.selectedPartner.next({
            label: '',
            value: document.contactNo,
        });
        this.selectedProjectNo.next(document.projectNo);
        this.selectedInvoiceType = document.type;
        this.selectedContractNo.next(document.contractNo);
        this.commentText = document.comment || '';

        this.dateOfArrival = document.dateOfArrival ? new Date(document.dateOfArrival) : null;
        this.secretaryDateOfArrival =
            document.secretaryDateOfArrival != null ? new Date(document.secretaryDateOfArrival) : null;
        this.partnerFilter.next(document.contactNo);

        const attachments: IPartnerDocumentAttachment[] = [];

        document.attachmentDocumentTypes.forEach((entry) => {
            attachments.push({
                documentType: entry.documentType,
                file: entry.attachment,
                spIdentifier: entry.spIdentifier,
                processingStatus: entry.processingStatus,
                failureReason: entry.failureReason,
            });
        });

        this.documentAttachments$.next(attachments);

        this.translateService.get('UPLOAD_DOCUMENT_PAGE.MANUAL_EDIT_TITLE').subscribe((res) => {
            this.breadcrumbService.setBreadcrumbs([
                { label: '{{PRIMARY_NAVIGATION.UPLOAD_DOCUMENT_LIST}}', url: '/secretary/documents-list' },
                { label: res, url: `/secretary/document/${this.contextId}` },
            ]);
        });

        if (document.status === UploadStatusEnum.Submitted) {
            this.isSubmitted = true;
            this.translateService.get('UPLOAD_DOCUMENT_PAGE.MANUAL_SUBMITTED_TITLE').subscribe((res) => {
                this.breadcrumbService.setBreadcrumbs([
                    {
                        label: '{{PRIMARY_NAVIGATION.UPLOAD_DOCUMENT_LIST}}',
                        url: '/secretary/documents-list',
                    },
                    { label: res, url: `/secretary/document/${this.contextId}` },
                ]);
            });
        }
    }

    showFeedbackDialog2() {
        this.showFeedbackDialog({
            messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
            description: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'),
            buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
            onButton1Click: () => {
                this.reloadPage();
            },
            button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
            onButton2Click: () => {
                this.router.navigate(['/secretary/documents-list']);
            },
            isSuccess: true,
        });
    }

    /**
     * Submit the document with attachments to the API
     */
    submitDocument() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SUBMIT_CONFIRMATION_MESSAGE'),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            const attachments = this.documentAttachments$.value;
            this.submitLoading$.next(true);
            if (this.isEditMode) {
                const request = this.getUpdateDocumentUploadDraftRequest(attachments);
                this.secretaryDocumentService
                    .submitManualDocumentUpload(this.contextId, request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.submitLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                                isSuccess: true,
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                const request = this.getCreateDocumentUploadDraftRequest(attachments);
                this.secretaryDocumentService
                    .createAndSubmitManualDocument(request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.submitLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SUBMIT_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            }
        };
    }

    /**
     * Discard all changes and reset the form
     */
    discardChanges() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.DISCARD_CONFIRMATION'),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            this.discardLoading$.next(true);
            if (this.isEditMode) {
                this.secretaryDocumentService
                    .removeSecretaryDocumentUpload(this.contextId)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.discardLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.DISCARD_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                this.discardLoading$.next(false);
                this.documentAttachmentService.revertFiles(this.contextId).pipe(take(1)).subscribe();
                this.reloadPage();
            }
        };
    }

    /**
     * Save the document without sending it
     */
    saveWithoutSending() {
        this.dialogService.open(ConfirmationDialogComponent, {
            width: '40rem',
            closable: false,
            header: this.translateService.instant('COMMON.CONFIRMATION'),
            data: {
                message: this.translateService.instant('UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SAVE_CONFIRMATION_MESSAGE'),
                description: this.translateService.instant(
                    'UPLOAD_DOCUMENT_PAGE.CONFIRMATION.SAVE_CONFIRMATION_DESCRIPTION'
                ),
                accept: () => {
                    acceptAction();
                },
            } as IConfirmationDialogData,
        });

        const acceptAction = () => {
            const attachments = this.documentAttachments$.value;
            this.saveLoading$.next(true);
            if (this.isEditMode) {
                const request = this.getUpdateDocumentUploadDraftRequest(attachments);
                this.secretaryDocumentService
                    .updateManualDocumentUploadDraft(this.contextId, request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.saveLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: (res) => {
                            this.setDocument(res);
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_BUTTON',
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                                isSuccess: true,
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.UPDATE_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            } else {
                const request = this.getCreateDocumentUploadDraftRequest(attachments);
                this.secretaryDocumentService
                    .createManualDocumentUploadDraft(request)
                    .pipe(
                        take(1),
                        finalize(() => {
                            this.saveLoading$.next(false);
                        })
                    )
                    .subscribe({
                        next: (res) => {
                            this.setDocument(res);
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_TITLE',
                                description: this.translateService.instant(
                                    'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_DESCRIPTION'
                                ),
                                buttonTextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_BUTTON',
                                isSuccess: true,
                                onButton1Click: () => {
                                    this.reloadPage();
                                },
                                button2TextKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.BUTTON_2',
                                onButton2Click: () => {
                                    this.router.navigate(['/secretary/documents-list']);
                                },
                            });
                        },
                        error: (err: IProblemDetails) => {
                            this.showFeedbackDialog({
                                messageKey: 'UPLOAD_DOCUMENT_PAGE.FEEDBACK.SAVE_FAILED_TITLE',
                                description: err.detail,
                                buttonTextKey: 'COMMON.BACK',
                                isSuccess: false,
                            });
                        },
                    });
            }
        };
    }
    getUpdateDocumentUploadDraftRequest(
        validAttachments: IPartnerDocumentAttachment[]
    ): UpdateSecretaryManualDocumentUploadDraftRequest {
        return {
            rowVersion: this.existingDocument?.rowVersion,
            contactNo: this.selectedPartner.value?.value as string,
            contactName: this.selectedPartner.value?.label,
            projectNo: this.selectedProjectNo.value,
            projectDescription: this.projectDescription,
            contractNo: this.selectedContractNo.value,
            type: this.selectedInvoiceType,
            secretaryDateOfArrival: this.secretaryDateOfArrival?.toLocaleDateString('hu-HU'),
            attachmentDocumentTypes: validAttachments.map(
                (attachment) =>
                    ({
                        documentType: attachment.documentType,
                        attachment: attachment.file,
                    } as AttachmentDocumentTypeDto)
            ),
            comment: this.commentText.trim(),
        };
    }

    getCreateDocumentUploadDraftRequest(
        validAttachments: IPartnerDocumentAttachment[]
    ): CreateSecretaryManualDocumentUploadDraftRequest {
        return {
            contextId: this.contextId,
            contactNo: this.selectedPartner.value?.value as string,
            contactName: this.selectedPartner.value?.label,
            projectNo: this.selectedProjectNo.value,
            projectDescription: this.projectDescription,
            contractNo: this.selectedContractNo.value,
            dateOfArrival: this.dateOfArrival?.toLocaleDateString('hu-HU'),
            secretaryDateOfArrival: this.secretaryDateOfArrival?.toLocaleDateString('hu-HU'),
            type: this.selectedInvoiceType,
            attachmentDocumentTypes: validAttachments.map(
                (attachment) =>
                    ({
                        documentType: attachment.documentType,
                        attachment: attachment.file,
                    } as AttachmentDocumentTypeDto)
            ),
            comment: this.commentText.trim(),
        };
    }
}

import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-document-list',
    template: `
        <div class="flex align-items-center p-3 border-round surface-border">
            <p-inputSwitch [(ngModel)]="isDraftMode" [trueValue]="true" [falseValue]="false"></p-inputSwitch>
            <span class="ml-3 font-medium text-lg">
                {{ getModeLabel() }}
            </span>
        </div>

        <app-secretary-submitted-table *ngIf="!isDraftMode"> </app-secretary-submitted-table>

        <app-secretary-draft-table *ngIf="isDraftMode"> </app-secretary-draft-table>

        <p-scrollTop></p-scrollTop>
    `,
    changeDetection: ChangeDetectionStrategy.OnPush,
    styleUrls: ['../secretary-document-list.component.scss'],
})
export class SecretaryDocumentListComponent {
    isDraftMode = false;

    constructor(private translateService: TranslateService) {}

    getModeLabel(): string {
        const statusKey = this.isDraftMode ? 'SECRETARY_DRAFT' : 'SECRETARY_SUBMITTED';
        return this.translateService.instant(`SECRETARY_PARTNER_DOCUMENTS.MODE.${statusKey}`);
    }
}

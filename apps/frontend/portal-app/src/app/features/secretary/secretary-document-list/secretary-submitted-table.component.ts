import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { DialogService } from 'primeng/dynamicdialog';
import { Observable, takeUntil } from 'rxjs';

import {
    ListSecretaryDocuments200Response,
    SecretaryDocumentListDto,
    SecretaryDocumentService,
    SecretaryUploadTypeEnum,
    TableRequest,
    UploadStatusEnum,
} from '@api-clients/document';
import { SecretaryTableBaseComponent, TableColumn } from '../secretary-table-base.component';

@Component({
    selector: 'app-secretary-submitted-table',
    template: `
        <div *ngIf="errorMessage$ | async as errorMessage" class="m-3">
            <p-message severity="error" [text]="errorMessage"></p-message>
        </div>

        <div #submittedTableContainer>
            <p-table
                #dataTable
                [value]="documents$ | async"
                dataKey="id"
                [rows]="pageSize"
                [showCurrentPageReport]="true"
                [rowsPerPageOptions]="rowsPerPageOptions"
                [loading]="loading$ | async"
                [paginator]="true"
                [totalRecords]="totalRecords$ | async"
                [lazy]="true"
                (onLazyLoad)="onTableStateChange($event)"
                [currentPageReportTemplate]="'SECRETARY_PARTNER_DOCUMENTS.TABLE_INFO' | translate"
                [stateKey]="tableStateKey"
                stateStorage="local"
                selectionMode="single"
                styleClass="p-datatable-sm"
                [(selection)]="selectedDocument"
                sortField="createdDate"
                [sortOrder]="-1"
            >
                <ng-template pTemplate="caption">
                    <div class="flex">
                        <button
                            pButton
                            [label]="'SECRETARY_PARTNER_DOCUMENTS.BUTTONS.CLEAR' | translate"
                            class="p-button-outlined"
                            icon="pi pi-filter-slash"
                            (click)="clearTable()"
                            styleClass="p-button-sm"
                        ></button>
                        <button
                            pButton
                            [label]="'SECRETARY_PARTNER_DOCUMENTS.BUTTONS.REFRESH' | translate"
                            class="p-button-outlined ml-2"
                            icon="pi pi-refresh"
                            [loading]="loading$ | async"
                            (click)="refreshTable()"
                            styleClass="p-button-sm"
                        ></button>
                        <button
                            #columnConfigBtn
                            pButton
                            class="p-button-outlined ml-2"
                            icon="pi pi-cog"
                            (click)="toggleColumnConfig($event)"
                            styleClass="p-button-sm"
                        ></button>
                    </div>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th
                            *ngFor="let column of visibleColumns"
                            [style.width]="column.width"
                            [pSortableColumn]="column.sortable ? column.field : null"
                        >
                            {{ column.label | translate }}
                            <p-sortIcon *ngIf="column.sortable" [field]="column.field"></p-sortIcon>
                        </th>
                        <th style="width:5%"></th>
                    </tr>
                    <tr>
                        <th *ngFor="let column of visibleColumns">
                            <p-columnFilter
                                *ngIf="column.filterable && column.filterType === 'text'"
                                type="text"
                                [field]="column.field"
                                matchMode="contains"
                                [matchModeOptions]="textMatchModeOptions"
                            ></p-columnFilter>
                            <p-columnFilter
                                *ngIf="column.filterable && column.filterType === 'date'"
                                [field]="column.field"
                                type="date"
                            >
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-calendar
                                        [ngModel]="value"
                                        (onSelect)="filter($event)"
                                        [appendTo]="'body'"
                                    ></p-calendar>
                                </ng-template>
                            </p-columnFilter>
                            <p-columnFilter
                                *ngIf="column.filterable && column.filterType === 'dropdown'"
                                [field]="column.field"
                                matchMode="equals"
                                [showMenu]="false"
                            >
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-dropdown
                                        [ngModel]="value"
                                        [options]="statusOptions"
                                        (onChange)="filter($event.value)"
                                        [placeholder]="
                                            'SECRETARY_PARTNER_DOCUMENTS.FILTERS.STATUS_PLACEHOLDER' | translate
                                        "
                                        [showClear]="true"
                                        optionLabel="label"
                                        optionValue="value"
                                        [appendTo]="submittedTableContainer"
                                    >
                                        <ng-template let-option pTemplate="item">
                                            <p-tag
                                                [value]="option.label"
                                                [severity]="getStatusSeverity(option.value)"
                                            ></p-tag>
                                        </ng-template>
                                    </p-dropdown>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-document>
                    <tr [pSelectableRow]="document">
                        <td *ngFor="let column of visibleColumns">
                            <ng-container [ngSwitch]="column.field">
                                <span *ngSwitchCase="'contactName'">{{ document.contactName || '-' }}</span>
                                <span *ngSwitchCase="'spIdentifiers'">
                                    <span *ngIf="document.spIdentifiers?.length; else noSpIdentifiers">
                                        {{ document.spIdentifiers.join(', ') }}
                                    </span>
                                    <ng-template #noSpIdentifiers>-</ng-template>
                                </span>
                                <span *ngSwitchCase="'createdDate'">{{ formatDate(document.createdDate) }}</span>
                                <span *ngSwitchCase="'dateOfArrival'">{{ formatDate(document.dateOfArrival) }}</span>
                                <span *ngSwitchCase="'uploader'">{{ document.uploader || '-' }}</span>
                                <p-tag
                                    *ngSwitchCase="'processingStatus'"
                                    [value]="getStatusLabel(document.processingStatus)"
                                    [severity]="getStatusSeverity(document.processingStatus)"
                                ></p-tag>
                            </ng-container>
                        </td>
                        <td>
                            <button
                                pButton
                                type="button"
                                icon="pi pi-eye"
                                class="p-button-rounded"
                                (click)="viewDocument(document)"
                                [disabled]="!document.id"
                            ></button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td [attr.colspan]="visibleColumns.length + 1">
                            {{ 'SECRETARY_PARTNER_DOCUMENTS.NO_DATA' | translate }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>

        <app-column-config
            [workingColumns]="workingColumns$ | async"
            (columnVisibilityChanged)="onColumnVisibilityChanged($event)"
            (resetConfiguration)="resetColumnConfiguration()"
            (saveConfiguration)="saveColumnConfiguration()"
            (columnsDropped)="onDropColumns($event)"
        ></app-column-config>
    `,
})
export class SecretarySubmittedTableComponent
    extends SecretaryTableBaseComponent<SecretaryDocumentListDto>
    implements OnInit
{
    @Input() override uploadStatus = UploadStatusEnum.Submitted;
    @Input() override tableStateKey = 'secretary-document-list-submitted';
    @Input() override columnConfigKey = 'secretary-document-list-submitted-column-config';

    constructor(
        secretaryDocumentService: SecretaryDocumentService,
        translateService: TranslateService,
        router: Router,
        route: ActivatedRoute,
        dialogService: DialogService
    ) {
        super(secretaryDocumentService, translateService, router, route, dialogService);
    }

    protected initializeColumns(): void {
        this.availableColumns = this.getDefaultColumns();
        this.availableColumnsSubject.next(this.availableColumns);
        this.updateVisibleColumns();
    }

    protected getDefaultColumns(): TableColumn[] {
        return [
            {
                id: 'contactName',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.PARTNER',
                field: 'contactName',
                sortable: true,
                filterable: true,
                filterType: 'text',
                width: '20%',
                visible: true,
            },
            {
                id: 'spIdentifiers',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.SP_IDENTIFIERS',
                field: 'spIdentifiers',
                sortable: true,
                filterable: true,
                filterType: 'text',
                width: '20%',
                visible: true,
            },
            {
                id: 'createdDate',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.CREATED_DATE',
                field: 'createdDate',
                sortable: true,
                filterable: true,
                filterType: 'date',
                width: '15%',
                visible: true,
            },
            {
                id: 'dateOfArrival',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.DATE_OF_ARRIVAL',
                field: 'dateOfArrival',
                sortable: true,
                filterable: true,
                filterType: 'date',
                width: '15%',
                visible: true,
            },
            {
                id: 'uploader',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.UPLOADER',
                field: 'uploader',
                sortable: true,
                filterable: true,
                filterType: 'text',
                width: '15%',
                visible: true,
            },
            {
                id: 'processingStatus',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.STATUS',
                field: 'processingStatus',
                sortable: false,
                filterable: true,
                filterType: 'dropdown',
                width: '10%',
                visible: true,
            },
        ];
    }

    protected getDocumentsApiCall(tableRequest: TableRequest): Observable<ListSecretaryDocuments200Response> {
        return this.secretaryDocumentService.listSecretaryDocuments(tableRequest);
    }

    protected navigateToDocument(document: SecretaryDocumentListDto): void {
        if (!document.id) {
            return;
        }

        if (document.uploadType === SecretaryUploadTypeEnum.PerCase) {
            this.router.navigate(['/secretary/documents-list/percase', document.id]);
        } else if (document.uploadType === SecretaryUploadTypeEnum.Manual) {
            this.router.navigate(['/secretary/documents-list/manual', document.id]);
        } else if (document.uploadType === SecretaryUploadTypeEnum.Batch) {
            this.router.navigate(['/secretary/documents-list/batch', document.id]);
        }
    }

    protected openDocumentDialog(document: SecretaryDocumentListDto): void {
        if (!document.id) {
            return;
        }

        const dialogComponent = this.getDialogComponentFromUploadType(document.uploadType);

        const headerKey = this.getHeaderKeyFromUploadType(document.uploadType);
        this.dialogRef = this.dialogService.open(dialogComponent, {
            width: '95vw',
            height: '95vh',
            header: this.translateService.instant(headerKey),
            contentStyle: { backgroundColor: '#f6f6f6' },
            styleClass: 'market-dialog',
            dismissableMask: true,
            closeOnEscape: true,
            maximizable: true,
            data: {
                documentNo: document.id,
            },
        });

        this.dialogRef.onClose.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.router.navigate(['/secretary/documents-list'], { replaceUrl: true });
            this.dialogRef = null;
        });
    }

    private getUploadTypeFromRoute(childRoutePath: string | null): SecretaryUploadTypeEnum | null {
        if (childRoutePath?.startsWith('percase')) {
            return SecretaryUploadTypeEnum.PerCase;
        } else if (childRoutePath?.startsWith('manual')) {
            return SecretaryUploadTypeEnum.Manual;
        } else if (childRoutePath?.startsWith('batch')) {
            return SecretaryUploadTypeEnum.Batch;
        }
        return null;
    }

    protected openDialogForRouteById(documentId: string, childRoutePath: string | null): void {
        let dialogComponent: any = null;
        const uploadType = this.getUploadTypeFromRoute(childRoutePath);
        dialogComponent = this.getDialogComponentFromUploadType(uploadType);
        const headerKey = this.getHeaderKeyFromUploadType(uploadType);
        if (!dialogComponent) {
            return;
        }

        this.dialogRef = this.dialogService.open(dialogComponent, {
            width: '95vw',
            height: '95vh',
            header: this.translateService.instant(headerKey),
            contentStyle: { backgroundColor: '#f6f6f6' },
            styleClass: 'market-dialog',
            dismissableMask: true,
            closeOnEscape: true,
            maximizable: true,
            data: { documentNo: documentId },
        });

        this.dialogRef.onClose.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.router.navigate(['/secretary/documents-list'], { replaceUrl: true });
            this.dialogRef = null;
        });
    }
}

import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { DialogService } from 'primeng/dynamicdialog';
import { Observable, takeUntil } from 'rxjs';

import {
    ListSecretaryDocuments200Response,
    SecretaryDocumentList<PERSON><PERSON>,
    SecretaryDocumentService,
    SecretaryUploadTypeEnum,
    TableRequest,
    UploadStatusEnum,
} from '@api-clients/document';
import { SecretaryDocumentBatchComponent } from '../secretary-document/secretary-document-batch.component';
import { SecretaryDocumentManualComponent } from '../secretary-document/secretary-document-manual.component';
import { SecretaryDocumentPercaseComponent } from '../secretary-document/secretary-document-percase.component';
import { SecretaryTableBaseComponent, TableColumn } from '../secretary-table-base.component';

@Component({
    selector: 'app-secretary-draft-table',
    template: `
        <div *ngIf="errorMessage$ | async as errorMessage" class="m-3">
            <p-message severity="error" [text]="errorMessage"></p-message>
        </div>

        <div>
            <p-table
                #dataTable
                [value]="documents$ | async"
                dataKey="id"
                [rows]="pageSize"
                [showCurrentPageReport]="true"
                [rowsPerPageOptions]="rowsPerPageOptions"
                [loading]="loading$ | async"
                [paginator]="true"
                [totalRecords]="totalRecords$ | async"
                [lazy]="true"
                (onLazyLoad)="onTableStateChange($event)"
                [currentPageReportTemplate]="'SECRETARY_PARTNER_DOCUMENTS.TABLE_INFO' | translate"
                [tableStyle]="tableStyle"
                [stateKey]="tableStateKey"
                stateStorage="local"
                selectionMode="single"
                styleClass="p-datatable-sm"
                [(selection)]="selectedDocument"
                sortField="createdDate"
                [sortOrder]="-1"
            >
                <ng-template pTemplate="caption">
                    <div class="flex">
                        <button
                            pButton
                            [label]="'SECRETARY_PARTNER_DOCUMENTS.BUTTONS.CLEAR' | translate"
                            class="p-button-outlined"
                            icon="pi pi-filter-slash"
                            (click)="clearTable()"
                            styleClass="p-button-sm"
                        ></button>
                        <button
                            pButton
                            [label]="'SECRETARY_PARTNER_DOCUMENTS.BUTTONS.REFRESH' | translate"
                            class="p-button-outlined ml-2"
                            icon="pi pi-refresh"
                            [loading]="loading$ | async"
                            (click)="refreshTable()"
                            styleClass="p-button-sm"
                        ></button>
                        <button
                            #columnConfigBtn
                            pButton
                            class="p-button-outlined ml-2"
                            icon="pi pi-cog"
                            (click)="toggleColumnConfig($event)"
                            styleClass="p-button-sm"
                        ></button>
                    </div>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th
                            *ngFor="let column of visibleColumns"
                            [style.width]="column.width"
                            [pSortableColumn]="column.sortable ? column.field : null"
                        >
                            {{ column.label | translate }}
                            <p-sortIcon *ngIf="column.sortable" [field]="column.field"></p-sortIcon>
                        </th>
                        <th style="width:10%"></th>
                    </tr>
                    <tr>
                        <th *ngFor="let column of visibleColumns">
                            <p-columnFilter
                                *ngIf="column.filterable && column.filterType === 'text'"
                                type="text"
                                [field]="column.field"
                                matchMode="contains"
                                [matchModeOptions]="textMatchModeOptions"
                            ></p-columnFilter>
                            <p-columnFilter
                                *ngIf="column.filterable && column.filterType === 'date'"
                                [field]="column.field"
                                type="date"
                            >
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-calendar
                                        [ngModel]="value"
                                        (onSelect)="filter($event)"
                                        [appendTo]="'body'"
                                    ></p-calendar>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-document>
                    <tr [pSelectableRow]="document">
                        <td *ngFor="let column of visibleColumns">
                            <ng-container [ngSwitch]="column.field">
                                <span *ngSwitchCase="'contactName'">{{ document.contactName || '-' }}</span>
                                <span *ngSwitchCase="'uploader'">{{ document.uploader || '-' }}</span>
                                <span *ngSwitchCase="'createdDate'">{{ formatDate(document.createdDate) }}</span>
                            </ng-container>
                        </td>
                        <td>
                            <button
                                pButton
                                type="button"
                                icon="pi pi-eye"
                                class="p-button-rounded"
                                (click)="viewDocument(document)"
                                [disabled]="!document.id"
                            ></button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td [attr.colspan]="visibleColumns.length + 1">
                            {{ 'SECRETARY_PARTNER_DOCUMENTS.NO_DATA' | translate }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>

        <app-column-config
            [workingColumns]="workingColumns$ | async"
            (columnVisibilityChanged)="onColumnVisibilityChanged($event)"
            (resetConfiguration)="resetColumnConfiguration()"
            (saveConfiguration)="saveColumnConfiguration()"
            (columnsDropped)="onDropColumns($event)"
        ></app-column-config>
    `,
})
export class SecretaryDraftTableComponent
    extends SecretaryTableBaseComponent<SecretaryDocumentListDto>
    implements OnInit
{
    @Input() override uploadStatus = UploadStatusEnum.Draft;
    @Input() override tableStateKey = 'secretary-document-list-draft';
    @Input() override columnConfigKey = 'secretary-document-list-draft-column-config';

    constructor(
        secretaryDocumentService: SecretaryDocumentService,
        translateService: TranslateService,
        router: Router,
        route: ActivatedRoute,
        dialogService: DialogService
    ) {
        super(secretaryDocumentService, translateService, router, route, dialogService);
    }

    protected initializeColumns(): void {
        this.availableColumns = this.getDefaultColumns();
        this.availableColumnsSubject.next(this.availableColumns);
        this.updateVisibleColumns();
    }

    protected getDefaultColumns(): TableColumn[] {
        return [
            {
                id: 'contactName',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.PARTNER',
                field: 'contactName',
                sortable: true,
                filterable: true,
                filterType: 'text',
                width: '35%',
                visible: true,
            },
            {
                id: 'uploader',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.UPLOADER',
                field: 'uploader',
                sortable: true,
                filterable: true,
                filterType: 'text',
                width: '25%',
                visible: true,
            },
            {
                id: 'createdDate',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.CREATED_DATE',
                field: 'createdDate',
                sortable: true,
                filterable: true,
                filterType: 'date',
                width: '30%',
                visible: true,
            },
        ];
    }

    protected getDocumentsApiCall(tableRequest: TableRequest): Observable<ListSecretaryDocuments200Response> {
        return this.secretaryDocumentService.listSecretaryDocuments(tableRequest);
    }

    protected navigateToDocument(document: SecretaryDocumentListDto): void {
        if (!document || !document.id) {
            return;
        }

        if (document.uploadType === SecretaryUploadTypeEnum.PerCase) {
            this.router.navigate(['/secretary/documents-list/percase', document.id]);
        } else if (document.uploadType === SecretaryUploadTypeEnum.Manual) {
            this.router.navigate(['/secretary/documents-list/manual', document.id]);
        } else if (document.uploadType === SecretaryUploadTypeEnum.Batch) {
            this.router.navigate(['/secretary/documents-list/batch', document.id]);
        }
    }

    protected openDocumentDialog(document: SecretaryDocumentListDto): void {
        if (!document || !document.id) {
            return;
        }

        const dialogComponent = this.getDialogComponentFromUploadType(document.uploadType);

        const headerKey = this.getHeaderKeyFromUploadType(document.uploadType);
        this.dialogRef = this.dialogService.open(dialogComponent, {
            width: '95vw',
            height: '95vh',
            header: this.translateService.instant(headerKey),
            contentStyle: { backgroundColor: '#f6f6f6' },
            styleClass: 'market-dialog',
            dismissableMask: true,
            closeOnEscape: true,
            maximizable: true,
            data: {
                documentNo: document.id,
            },
        });

        this.dialogRef.onClose.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.router.navigate(['/secretary/documents-list'], { replaceUrl: true });
            this.dialogRef = null;
        });
    }

    protected openDialogForRouteById(documentId: string, childRoutePath: string | null): void {
        let dialogComponent: any = null;

        if (childRoutePath?.startsWith('percase')) {
            dialogComponent = SecretaryDocumentPercaseComponent;
        } else if (childRoutePath?.startsWith('manual')) {
            dialogComponent = SecretaryDocumentManualComponent;
        } else if (childRoutePath?.startsWith('batch')) {
            dialogComponent = SecretaryDocumentBatchComponent;
        }

        if (!dialogComponent) {
            return;
        }

        this.dialogRef = this.dialogService.open(dialogComponent, {
            width: '95vw',
            height: '95vh',
            contentStyle: { backgroundColor: '#f6f6f6' },
            styleClass: 'market-dialog',
            dismissableMask: true,
            closeOnEscape: true,
            maximizable: true,
            data: { documentNo: documentId },
        });

        this.dialogRef.onClose.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.router.navigate(['/secretary/documents-list'], { replaceUrl: true });
            this.dialogRef = null;
        });
    }
}

import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-secretary-partner-documents-new',
    template: `
        <div class="flex align-items-center p-3 border-round surface-border">
            <p-inputSwitch [(ngModel)]="isDraftMode" [trueValue]="true" [falseValue]="false"></p-inputSwitch>
            <span class="ml-3 font-medium text-lg">
                {{ getModeLabel() }}
            </span>
        </div>

        <app-secretary-partner-submitted-table *ngIf="!isDraftMode"> </app-secretary-partner-submitted-table>

        <app-secretary-partner-draft-table *ngIf="isDraftMode"> </app-secretary-partner-draft-table>

        <p-scrollTop></p-scrollTop>
    `,
    styleUrls: ['../secretary-document-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SecretaryPartnerDocumentsNewComponent {
    isDraftMode = false;

    constructor(private translateService: TranslateService) {}

    getModeLabel(): string {
        const statusKey = this.isDraftMode ? 'PARTNER_DRAFT' : 'PARTNER_SUBMITTED';
        return this.translateService.instant(`SECRETARY_PARTNER_DOCUMENTS.MODE.${statusKey}`);
    }
}

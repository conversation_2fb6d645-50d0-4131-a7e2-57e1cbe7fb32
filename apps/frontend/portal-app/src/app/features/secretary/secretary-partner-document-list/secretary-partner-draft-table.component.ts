import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { DialogService } from 'primeng/dynamicdialog';
import { Observable, takeUntil } from 'rxjs';

import {
    ListPartnerDocuments200Response,
    SecretaryDocumentService,
    SecretaryPartnerDocumentListDto,
    TableRequest,
    UploadStatusEnum,
} from '@api-clients/document';
import { SecretaryPartnerDocumentComponent } from '../secretary-partner-document/secretary-partner-document.component';
import { SecretaryTableBaseComponent, TableColumn } from '../secretary-table-base.component';

@Component({
    selector: 'app-secretary-partner-draft-table',
    template: `
        <div *ngIf="errorMessage$ | async as errorMessage" class="m-3">
            <p-message severity="error" [text]="errorMessage"></p-message>
        </div>

        <div>
            <p-table
                #dataTable
                [value]="documents$ | async"
                dataKey="id"
                [rows]="pageSize"
                [showCurrentPageReport]="true"
                [rowsPerPageOptions]="rowsPerPageOptions"
                [loading]="loading$ | async"
                [paginator]="true"
                [totalRecords]="totalRecords$ | async"
                [lazy]="true"
                (onLazyLoad)="onTableStateChange($event)"
                [currentPageReportTemplate]="'SECRETARY_PARTNER_DOCUMENTS.TABLE_INFO' | translate"
                [tableStyle]="tableStyle"
                [stateKey]="tableStateKey"
                stateStorage="local"
                selectionMode="single"
                styleClass="p-datatable-sm"
                [(selection)]="selectedDocument"
                sortField="createdDate"
                [sortOrder]="-1"
            >
                <ng-template pTemplate="caption">
                    <div class="flex">
                        <button
                            pButton
                            [label]="'SECRETARY_PARTNER_DOCUMENTS.BUTTONS.CLEAR' | translate"
                            class="p-button-outlined"
                            icon="pi pi-filter-slash"
                            (click)="clearTable()"
                            styleClass="p-button-sm"
                        ></button>
                        <button
                            pButton
                            [label]="'SECRETARY_PARTNER_DOCUMENTS.BUTTONS.REFRESH' | translate"
                            class="p-button-outlined ml-2"
                            icon="pi pi-refresh"
                            [loading]="loading$ | async"
                            (click)="refreshTable()"
                            styleClass="p-button-sm"
                        ></button>
                        <button
                            #columnConfigBtn
                            pButton
                            class="p-button-outlined ml-2"
                            icon="pi pi-cog"
                            (click)="toggleColumnConfig($event)"
                            styleClass="p-button-sm"
                        ></button>
                    </div>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th
                            *ngFor="let column of visibleColumns"
                            [style.width]="column.width"
                            [pSortableColumn]="column.sortable ? column.field : null"
                        >
                            {{ column.label | translate }}
                            <p-sortIcon *ngIf="column.sortable" [field]="column.field"></p-sortIcon>
                        </th>
                        <th style="width:10%"></th>
                    </tr>
                    <tr>
                        <th *ngFor="let column of visibleColumns">
                            <p-columnFilter
                                *ngIf="column.filterable && column.filterType === 'text'"
                                type="text"
                                [field]="column.field"
                                matchMode="contains"
                                [matchModeOptions]="textMatchModeOptions"
                            ></p-columnFilter>
                            <p-columnFilter
                                *ngIf="column.filterable && column.filterType === 'date'"
                                [field]="column.field"
                                type="date"
                            >
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-calendar
                                        [ngModel]="value"
                                        (onSelect)="filter($event)"
                                        [appendTo]="'body'"
                                    ></p-calendar>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-document>
                    <tr [pSelectableRow]="document">
                        <td *ngFor="let column of visibleColumns">
                            <ng-container [ngSwitch]="column.field">
                                <span *ngSwitchCase="'partner'">{{ document.partner || '-' }}</span>
                                <span *ngSwitchCase="'partnerContactNo'">{{ document.partnerContactNo || '-' }}</span>
                                <span *ngSwitchCase="'createdDate'">{{ formatDate(document.createdDate) }}</span>
                            </ng-container>
                        </td>
                        <td>
                            <button
                                pButton
                                type="button"
                                icon="pi pi-eye"
                                class="p-button-rounded"
                                (click)="viewDocument(document)"
                                [disabled]="!document.id"
                            ></button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td [attr.colspan]="visibleColumns.length + 1">
                            {{ 'SECRETARY_PARTNER_DOCUMENTS.NO_DATA' | translate }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>

        <app-column-config
            [workingColumns]="workingColumns$ | async"
            (columnVisibilityChanged)="onColumnVisibilityChanged($event)"
            (resetConfiguration)="resetColumnConfiguration()"
            (saveConfiguration)="saveColumnConfiguration()"
            (columnsDropped)="onDropColumns($event)"
        ></app-column-config>
    `,
})
export class SecretaryPartnerDraftTableComponent
    extends SecretaryTableBaseComponent<SecretaryPartnerDocumentListDto>
    implements OnInit
{
    @Input() override uploadStatus = UploadStatusEnum.Draft;
    @Input() override tableStateKey = 'secretary-partner-documents-draft';
    @Input() override columnConfigKey = 'secretary-partner-documents-draft-column-config';

    constructor(
        secretaryDocumentService: SecretaryDocumentService,
        translateService: TranslateService,
        router: Router,
        route: ActivatedRoute,
        dialogService: DialogService
    ) {
        super(secretaryDocumentService, translateService, router, route, dialogService);
    }

    protected initializeColumns(): void {
        this.availableColumns = this.getDefaultColumns();
        this.availableColumnsSubject.next(this.availableColumns);
        this.updateVisibleColumns();
    }

    protected getDefaultColumns(): TableColumn[] {
        return [
            {
                id: 'partner',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.PARTNER',
                field: 'partner',
                sortable: true,
                filterable: true,
                filterType: 'text',
                width: '35%',
                visible: true,
            },
            {
                id: 'partnerContactNo',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.PARTNER_CONTACT_NO',
                field: 'partnerContactNo',
                sortable: true,
                filterable: true,
                filterType: 'text',
                width: '25%',
                visible: true,
            },
            {
                id: 'createdDate',
                label: 'SECRETARY_PARTNER_DOCUMENTS.COLUMNS.CREATED_DATE',
                field: 'createdDate',
                sortable: true,
                filterable: true,
                filterType: 'date',
                width: '30%',
                visible: true,
            },
        ];
    }

    protected getDocumentsApiCall(tableRequest: TableRequest): Observable<ListPartnerDocuments200Response> {
        return this.secretaryDocumentService.listPartnerDocuments(tableRequest);
    }

    protected navigateToDocument(document: SecretaryPartnerDocumentListDto): void {
        if (document.id) {
            this.router.navigate(['/secretary/documents-list-partner', document.id]);
        }
    }

    protected openDocumentDialog(document: SecretaryPartnerDocumentListDto): void {
        if (!document.id) {
            return;
        }

        this.dialogRef = this.dialogService.open(SecretaryPartnerDocumentComponent, {
            width: '95vw',
            height: '95vh',
            header: this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.DIALOG_TITLE'),
            contentStyle: { backgroundColor: '#f6f6f6' },
            styleClass: 'market-dialog',
            dismissableMask: true,
            closeOnEscape: true,
            maximizable: true,
            data: {
                documentNo: document.id,
            },
        });

        this.dialogRef.onClose.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.router.navigate(['/secretary/documents-list-partner'], { replaceUrl: true });
            this.dialogRef = null;
        });
    }

    protected openDialogForRouteById(documentId: string, _childRoutePath: string | null): void {
        this.dialogRef = this.dialogService.open(SecretaryPartnerDocumentComponent, {
            width: '95vw',
            height: '95vh',
            header: this.translateService.instant('SECRETARY_PARTNER_DOCUMENTS.DETAILS.DIALOG_TITLE'),
            contentStyle: { backgroundColor: '#f6f6f6' },
            styleClass: 'market-dialog',
            dismissableMask: true,
            closeOnEscape: true,
            maximizable: true,
            data: { documentNo: documentId },
        });

        this.dialogRef.onClose.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.router.navigate(['/secretary/documents-list-partner'], { replaceUrl: true });
            this.dialogRef = null;
        });
    }
}

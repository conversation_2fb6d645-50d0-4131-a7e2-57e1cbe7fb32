import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
    AttachmentService as DocumentAttachmentService,
    DocumentAnalysisStatusEnum,
    DocumentEloStatusEnum,
    DocumentReportService,
    DocumentStatusReportDto,
    DocumentValidationStatusEnum,
    SimpleDocumentStatusReportDto,
    ValidationJsonDto,
} from '@api-clients/document';
import { IFeedbackDialogData } from '@core/interfaces/feedback-dialog.interface';
import { IProblemDetails } from '@core/interfaces/problem-details.interface';
import { dateToDateString, downloadBlob, openBlobInBrowser } from '@core/utils/common';
import { TranslateService } from '@ngx-translate/core';
import { FeedbackDialogComponent } from '@shared/components/feedback-dialog/feedback-dialog.component';
import hljs from 'highlight.js/lib/core';
import xml from 'highlight.js/lib/languages/xml';
import { MessageService } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { Subject, takeUntil } from 'rxjs';
import { createJSONEditor } from 'vanilla-jsoneditor/standalone.js';
import { JsonViewerDialogComponent } from './json-viewer-dialog.component';
import { XmlViewerComponent } from './xml-viewer.component';

@Component({
    selector: 'document-status-dashboard',
    template: `
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/vs2015.min.css"
        />
        <div class="p-4">
            <h1 class="text-xl font-bold mb-4">Document Processing Dashboard</h1>

            <!-- Filters -->
            <p-panel header="Filters" [toggleable]="true" styleClass="mb-4 p-4">
                <form [formGroup]="filterForm" class="grid mt-2">
                    <div class="field col-12 md:col-3">
                        <label for="fromDate">From Date</label>
                        <p-calendar
                            id="fromDate"
                            formControlName="fromDate"
                            [showIcon]="true"
                            dateFormat="yy-mm-dd"
                            styleClass="w-full"
                        ></p-calendar>
                    </div>
                    <div class="field col-12 md:col-3">
                        <label for="toDate">To Date</label>
                        <p-calendar
                            id="toDate"
                            formControlName="toDate"
                            [showIcon]="true"
                            dateFormat="yy-mm-dd"
                            styleClass="w-full"
                        ></p-calendar>
                    </div>
                    <div class="field col-12 md:col-3 flex flex-column">
                        <label for="spIdentifier">SP Identifier</label>
                        <input
                            pInputText
                            name="spIdentifier"
                            formControlName="spIdentifier"
                            type="text"
                            placeholder="SP Identifier"
                        />
                    </div>
                    <div class="field col-12 md:col-3 flex align-items-end gap-2">
                        <p-button label="Apply Filters" icon="pi pi-search" (onClick)="applyFilters()"></p-button>
                        <p-button label="Clear Filters" icon="pi pi-times" (onClick)="clearFilters()"></p-button>
                    </div>
                </form>

                <div class="flex align-items-center justify-content-between mt-3">
                    <p-button icon="pi pi-refresh" styleClass="p-button-outlined" (onClick)="refreshData()"></p-button>
                    <p-button
                        label="Download Based on SP"
                        icon="pi pi-download"
                        styleClass="p-button-outlined"
                        (onClick)="downloadBasedOnSp()"
                    ></p-button>
                </div>
            </p-panel>

            <!-- Documents Table -->
            <p-table
                [value]="documents"
                [scrollable]="true"
                scrollHeight="calc(100vh - 450px)"
                [paginator]="true"
                [totalRecords]="totalRecords"
                [rows]="pageSize"
                [first]="(currentPage - 1) * pageSize"
                [showCurrentPageReport]="true"
                [lazy]="true"
                styleClass="p-datatable-sm"
                (onLazyLoad)="onLazyLoad($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                [rowsPerPageOptions]="[10, 20, 50]"
                [loading]="loading"
                styleClass="market-table"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="id">Document ID <p-sortIcon field="id"></p-sortIcon></th>
                        <th pSortableColumn="createdDate">
                            Created Date <p-sortIcon field="createdDate"></p-sortIcon>
                        </th>
                        <th pSortableColumn="documentType">Type <p-sortIcon field="documentType"></p-sortIcon></th>
                        <th>Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-document>
                    <tr class="cursor-pointer" (click)="viewDocumentDetails(document.id)">
                        <td>{{ document.id }}</td>
                        <td>{{ document.createdDate | date : 'medium' }}</td>
                        <td>
                            <span
                                class="status-badge"
                                [ngClass]="{
                                    'status-success': document.documentType === 'Partner',
                                    'status-processing': document.documentType === 'Secretary'
                                }"
                            >
                                {{ document.documentType }}
                            </span>
                        </td>
                        <td>
                            <p-button
                                icon="pi pi-eye"
                                styleClass="p-button-sm p-button-outlined"
                                (onClick)="viewDocumentDetails(document.id); $event.stopPropagation()"
                                pTooltip="View Details"
                            ></p-button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="4" class="text-center p-4">No documents found.</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>

        <p-button
            icon="pi pi-refresh"
            styleClass="p-button-danger m-4"
            label="Retry All Validations"
            (onClick)="retryAllValidation()"
        ></p-button>

        <!-- Document Details Dialog -->
        <p-dialog
            header="Document Details"
            [visible]="!!selectedDocument"
            (visibleChange)="$event ? null : closeDocumentDetails()"
            [style]="{ width: '90vw', minHeight: '40vh' }"
            [modal]="true"
            [maximizable]="true"
            [baseZIndex]="1000"
            [dismissableMask]="true"
        >
            <ng-template pTemplate="header">
                <div class="flex align-items-center">
                    <span class="font-bold mr-auto">Document Details</span>
                    <p-button
                        icon="pi pi-link"
                        styleClass="p-button-text p-button-sm"
                        (onClick)="copyDocumentLink()"
                        pTooltip="Copy document link"
                        tooltipPosition="bottom"
                    ></p-button>
                </div>
            </ng-template>
            <div *ngIf="selectedDocument">
                <p-tabView>
                    <!-- Document Info Tab -->
                    <p-tabPanel header="Document Info">
                        <div class="grid">
                            <div class="col-12 md:col-6">
                                <p-panel header="Basic Information">
                                    <div class="grid">
                                        <div class="col-6"><strong>Document No:</strong></div>
                                        <div class="col-6">{{ selectedDocument.id || 'N/A' }}</div>

                                        <div class="col-6"><strong>Document Upload Type:</strong></div>
                                        <div class="col-6">
                                            {{ selectedDocument.isSecretaryDocument ? 'Secretary' : 'Partner' }}
                                        </div>

                                        <div class="col-6"><strong>Secretary Upload Type:</strong></div>
                                        <div class="col-6">{{ selectedDocument.secretaryUploadType || 'N/A' }}</div>

                                        <div class="col-6"><strong>Contact No:</strong></div>
                                        <div class="col-6">{{ selectedDocument.contactNo || 'N/A' }}</div>

                                        <div class="col-6"><strong>Project No:</strong></div>
                                        <div class="col-6">{{ selectedDocument.projectNo || 'N/A' }}</div>

                                        <div class="col-6"><strong>Contract No:</strong></div>
                                        <div class="col-6">{{ selectedDocument.contractNo || 'N/A' }}</div>

                                        <div class="col-6"><strong>Created Date:</strong></div>
                                        <div class="col-6">{{ selectedDocument.createdDate | date : 'medium' }}</div>

                                        <div class="col-6"><strong>Date of Arrival:</strong></div>
                                        <div class="col-6">{{ selectedDocument.dateOfArrival | date : 'medium' }}</div>

                                        <div class="col-6"><strong>Secretary Date of Arrival:</strong></div>
                                        <div class="col-6">
                                            {{ selectedDocument.secretaryDateOfArrival | date : 'medium' }}
                                        </div>
                                    </div>
                                </p-panel>
                            </div>

                            <div class="col-12 md:col-6">
                                <p-panel header="Processing Information">
                                    <div class="grid">
                                        <div class="col-6"><strong>Upload Status:</strong></div>
                                        <div class="col-6">{{ selectedDocument.uploadStatus }}</div>

                                        <div class="col-6"><strong>Total Attachments:</strong></div>
                                        <div class="col-6">{{ selectedDocument.totalAttachments }}</div>
                                    </div>
                                </p-panel>
                            </div>
                        </div>
                    </p-tabPanel>

                    <!-- Classification Status Tab -->
                    <p-tabPanel header="Classification Status" [disabled]="!selectedDocument.isSecretaryDocument">
                        <p-table
                            [value]="selectedDocument.classificationDetails"
                            styleClass="p-datatable-sm"
                            styleClass="market-table"
                        >
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Id</th>
                                    <th>Attachment</th>
                                    <th>Status</th>
                                    <th>Error Message</th>
                                    <th>Retry Count</th>
                                    <th>Result Json</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-detail>
                                <tr>
                                    <td>{{ detail.id }}</td>
                                    <td>
                                        <span
                                            class="attachment-name"
                                            (click)="viewAttachment(detail.attachmentId, detail.attachmentName)"
                                        >
                                            {{ detail.attachmentName }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge" [ngClass]="getStatusClass(detail.status)">
                                            {{ detail.status }}
                                        </span>
                                    </td>
                                    <td>{{ detail.errorMessage || 'N/A' }}</td>
                                    <td>{{ detail.retryCount }}</td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.classificationResultJson"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showAnalysisResultJson(detail.classificationResultJson)"
                                        >
                                        </p-button>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="text-center p-4">No classification details available.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </p-tabPanel>

                    <!-- Analysis Status Tab -->
                    <p-tabPanel header="Analysis Status">
                        <p-table
                            [value]="selectedDocument.analysisDetails"
                            styleClass="p-datatable-sm"
                            styleClass="market-table"
                        >
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Processing Source Id</th>
                                    <th>Document Type</th>
                                    <th>Attachment</th>
                                    <th>Status</th>
                                    <th>Error Message</th>
                                    <th>Retry Count</th>
                                    <th>Result Json</th>
                                    <th>Retry</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-detail>
                                <tr>
                                    <td>{{ detail.processingSourceId }}</td>
                                    <td>{{ detail.documentType }}</td>
                                    <td>
                                        <span
                                            class="attachment-name"
                                            (click)="viewAttachment(detail.attachmentId, detail.attachmentName)"
                                        >
                                            {{ detail.attachmentName }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge" [ngClass]="getStatusClass(detail.status)">
                                            {{ detail.status }}
                                        </span>
                                    </td>
                                    <td>{{ detail.errorMessage || 'N/A' }}</td>
                                    <td>{{ detail.retryCount }}</td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.azureDocumentAiResultJson"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showAnalysisResultJson(detail.azureDocumentAiResultJson)"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            icon="pi pi-refresh"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="retryAnalysis(detail.id)"
                                        >
                                        </p-button>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="text-center p-4">No analysis details available.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </p-tabPanel>

                    <!-- Validation Status Tab -->
                    <p-tabPanel header="Validation Status">
                        <p-table
                            [value]="selectedDocument.validationDetails"
                            styleClass="p-datatable-sm"
                            styleClass="market-table"
                        >
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Processing Source Id</th>
                                    <th>Document Type</th>
                                    <th>Attachment</th>
                                    <th>Status</th>
                                    <th>Error Message</th>
                                    <th>Retry Count</th>
                                    <th>Validation Result</th>
                                    <th>Retry</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-detail>
                                <tr>
                                    <td>{{ detail.processingSourceId }}</td>
                                    <td>{{ detail.documentType }}</td>
                                    <td>
                                        <span
                                            class="attachment-name"
                                            (click)="viewAttachment(detail.attachmentId, detail.attachmentName)"
                                        >
                                            {{ detail.attachmentName }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge" [ngClass]="getStatusClass(detail.status)">
                                            {{ detail.status }}
                                        </span>
                                    </td>
                                    <td>{{ detail.errorMessage || 'N/A' }}</td>
                                    <td>{{ detail.retryCount }}</td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.validationResultJson"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showValidationResultJson(detail.validationResultJson)"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            icon="pi pi-refresh"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="retryValidation(detail.id)"
                                        >
                                        </p-button>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="text-center p-4">No validation details available.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </p-tabPanel>

                    <p-tabPanel header="Elo Status">
                        <p-table
                            [value]="selectedDocument.eloDetails"
                            styleClass="p-datatable-sm"
                            styleClass="market-table"
                        >
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Processing Source Id</th>
                                    <th>Attachment</th>
                                    <th>Status</th>
                                    <th>Error Message</th>
                                    <th>Retry Count</th>
                                    <th>Create Request XML</th>
                                    <th>Create Response XML</th>
                                    <th>Upload Request XML</th>
                                    <th>Upload Response XML</th>
                                    <th>Workflow Request XML</th>
                                    <th>Workflow Response XML</th>
                                    <th>View Merged Document</th>
                                    <th>Retry</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-detail>
                                <tr>
                                    <td>{{ detail.processingSourceId }}</td>
                                    <td>
                                        <span
                                            class="attachment-name"
                                            (click)="viewAttachment(detail.attachmentId, detail.attachmentName)"
                                        >
                                            {{ detail.attachmentName }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge" [ngClass]="getStatusClass(detail.status)">
                                            {{ detail.status }}
                                        </span>
                                    </td>
                                    <td>{{ detail.errorMessage || 'N/A' }}</td>
                                    <td>{{ detail.retryCount }}</td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.createRequestXml"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showXml(detail.createRequestXml, 'Create Request XML')"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.createResponseXml"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showXml(detail.createResponseXml, 'Create Response XML')"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.uploadRequestXml"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showXml(detail.uploadRequestXml, 'Upload Request XML')"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.uploadResponseXml"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showXml(detail.uploadResponseXml, 'Upload Response XML')"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.workflowRequestXml"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showXml(detail.workflowRequestXml, 'Workflow Request XML')"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            *ngIf="detail.workflowResponseXml"
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="showXml(detail.workflowResponseXml, 'Workflow Response XML')"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            icon="pi pi-eye"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="viewMergedDocument(detail.documentId, detail.processingSourceId)"
                                        >
                                        </p-button>
                                    </td>
                                    <td>
                                        <p-button
                                            icon="pi pi-refresh"
                                            styleClass="p-button-sm p-button-outlined"
                                            (onClick)="retryElo(detail.id)"
                                        >
                                        </p-button>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="text-center p-4">No validation details available.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </p-tabPanel>
                </p-tabView>
            </div>
        </p-dialog>

        <p-dialog
            header="Validation Result"
            [(visible)]="showValidationDialog"
            [style]="{ width: '90vw' }"
            [modal]="true"
            [dismissableMask]="true"
        >
            <p-table
                [value]="validationResults"
                styleClass="p-datatable-sm"
                [tableStyle]="{ 'table-layout': 'fixed', width: '100%' }"
                styleClass="market-table"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 20%">Column Name</th>
                        <th style="width: 10%">Status</th>
                        <th style="width: 15%">BC Value</th>
                        <th style="width: 15%">Document AI Value</th>
                        <th style="width: 15%">Nav Value</th>
                        <th style="width: 25%">Value Only</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-result>
                    <tr>
                        <td style="word-break: break-word">{{ result.eloColumnName }}</td>
                        <td style="word-break: break-word">{{ result.status }}</td>
                        <td style="word-break: break-word">{{ result.bcColumnValue || '' }}</td>
                        <td style="word-break: break-word">{{ result.documentAiColumnValue || '' }}</td>
                        <td style="word-break: break-word">{{ result.navColumnValue || '' }}</td>
                        <td style="word-break: break-word">{{ result.valueOnly || '' }}</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5" class="text-center p-4">No validation details available.</td>
                    </tr>
                </ng-template>
            </p-table>
        </p-dialog>

        <p-toast></p-toast>
    `,
    styles: [
        `
            :host ::ng-deep {
                .status-badge {
                    display: inline-block;
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    font-size: 0.875rem;
                    font-weight: 500;
                }

                .status-success {
                    background-color: #dff0d8;
                    color: #1e7e34;
                }

                .status-error {
                    background-color: #f8d7da;
                    color: #721c24;
                }

                .status-processing {
                    background-color: #cce5ff;
                    color: #0056b3;
                }

                .status-pending {
                    background-color: #fff3cd;
                    color: #856404;
                }

                .attachment-name {
                    cursor: pointer;
                    text-decoration: underline;
                    color: #007bff;
                }

                tr.cursor-pointer {
                    cursor: pointer;
                }

                tr.cursor-pointer:hover {
                    background-color: var(--surface-hover);
                }
            }
        `,
    ],
})
export class DocumentStatusDashboardComponent implements OnInit, OnDestroy, AfterViewChecked {
    documents: SimpleDocumentStatusReportDto[] = [];

    // Filtering
    filterForm = new FormGroup({
        fromDate: new FormControl<Date | null>(null),
        toDate: new FormControl<Date | null>(null),
        spIdentifier: new FormControl<string | null>(null),
    });

    // Pagination
    totalRecords = 0;
    currentPage = 1;
    pageSize = 10;
    private destroy$ = new Subject<void>();
    loading = false;
    selectedDocument: DocumentStatusReportDto | null = null;

    showValidationDialog = false;
    validationResults: ValidationJsonDto[] = [];

    showAnalysisResultDialog = false;
    showXmlDialog = false;
    xmlContent = '';

    private editor: any;

    constructor(
        private documentReportService: DocumentReportService,
        private dialogService: DialogService,
        private documentAttachmentService: DocumentAttachmentService,
        private translateService: TranslateService,
        private messageService: MessageService,
        private route: ActivatedRoute,
        private router: Router
    ) {
        // Register the XML language
        hljs.registerLanguage('xml', xml);
    }

    ngOnInit(): void {
        // Subscribe to route query params to handle document ID
        this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params) => {
            if (params['documentId']) {
                this.viewDocumentDetails(params['documentId']);
            }
        });

        // Load initial data
        this.loadDocuments();
    }

    ngAfterViewChecked() {
        // Initialize highlighting for any code blocks
        hljs.highlightAll();
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    onLazyLoad(event: any): void {
        // Calculate page from first record index
        const page = Math.floor(event.first / event.rows) + 1;
        this.currentPage = page;
        this.pageSize = event.rows;

        console.log('onLazyLoad', event);

        this.loadDocuments();
    }

    applyFilters(): void {
        // Reset to first page when applying filters
        this.currentPage = 1;
        this.loadDocuments();
    }

    refreshData(): void {
        this.loadDocuments();
    }

    loadDocuments(): void {
        const fromDate = this.filterForm.get('fromDate')?.value;
        const toDate = this.filterForm.get('toDate')?.value;
        const spIdentifier = this.filterForm.get('spIdentifier')?.value;

        // Convert Date objects to ISO strings if they exist
        const fromDateStr = fromDate ? dateToDateString(fromDate) : undefined;
        const toDateStr = toDate ? dateToDateString(toDate) : undefined;

        this.loading = true;
        this.documentReportService
            .getDocumentStatusReports(fromDateStr, toDateStr, this.currentPage, this.pageSize, spIdentifier)
            .pipe(takeUntil(this.destroy$))
            .subscribe((response) => {
                this.loading = false;
                this.totalRecords = response.totalRecords || 0;
                this.documents = response.data || [];

                // Check if we have a document ID in the URL and reload that document
                const params = this.route.snapshot.queryParams;
                if (params['documentId'] && this.selectedDocument) {
                    this.viewDocumentDetails(params['documentId']);
                }
            });
    }

    viewDocumentDetails(docId: string): void {
        this.loading = true;

        // Update URL with document ID
        this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { documentId: docId },
            queryParamsHandling: 'merge',
        });

        // Get the document details from the service
        this.documentReportService
            .getDocumentStatusReportById(docId)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (document) => {
                    this.selectedDocument = document;
                    this.loading = false;
                },
                error: () => {
                    this.loading = false;
                },
            });
    }

    showValidationResultJson(jsonData?: ValidationJsonDto[]): void {
        if (jsonData) {
            this.showValidationDialog = true;
            this.validationResults = jsonData;
        }
    }

    showXml(xmlData?: string, title?: string): void {
        if (!xmlData) return;
        this.dialogService.open(XmlViewerComponent, {
            header: title || 'XML',
            width: '80vw',
            data: { xmlContent: xmlData },
            modal: true,
            dismissableMask: true,
            maximizable: true,
        });
    }

    showAnalysisResultJson(jsonData?: string): void {
        if (!jsonData) return;

        // Parse the JSON string to ensure it's valid
        const jsonContent = JSON.parse(jsonData);

        // Create dialog with JSON editor after view init
        setTimeout(() => {
            const dialogRef = document.createElement('div');
            dialogRef.className = 'json-editor-dialog';
            document.body.appendChild(dialogRef);

            const content: any = {
                text: undefined,
                json: jsonContent,
            };

            const editor = createJSONEditor({
                target: dialogRef,
                props: {
                    content,
                    mode: 'view',
                    readOnly: true,
                },
            });

            // Show PrimeNG dialog
            this.dialogService
                .open(JsonViewerDialogComponent, {
                    width: '90%',
                    height: '90%',
                    showHeader: false,
                    closable: true,
                    dismissableMask: true,
                    data: {
                        editorElement: dialogRef,
                    },
                })
                .onClose.subscribe(() => {
                    // Cleanup
                    if (editor) {
                        editor.destroy();
                    }
                    if (document.body.contains(dialogRef)) {
                        document.body.removeChild(dialogRef);
                    }
                });
        });
    }

    getStatusClass(status: string): string {
        switch (status.toLowerCase().trim()) {
            case 'completed':
            case DocumentAnalysisStatusEnum.Success.toLowerCase():
            case DocumentValidationStatusEnum.Success.toLowerCase():
            case DocumentEloStatusEnum.Success.toLowerCase():
                return 'status-success';
            case DocumentAnalysisStatusEnum.Failed.toLowerCase():
            case DocumentValidationStatusEnum.TechnicalError.toLowerCase():
            case DocumentValidationStatusEnum.UnknownDocumentType.toLowerCase():
            case DocumentValidationStatusEnum.BcFailed.toLowerCase():
            case DocumentEloStatusEnum.Failed.toLowerCase():
            case 'partially failed':
                return 'status-error';
            case DocumentAnalysisStatusEnum.Processing.toLowerCase():
            case DocumentValidationStatusEnum.Processing.toLowerCase():
            case DocumentEloStatusEnum.Processing.toLowerCase():
                return 'status-processing';
            case DocumentAnalysisStatusEnum.Pending.toLowerCase():
            case DocumentValidationStatusEnum.Pending.toLowerCase():
            case DocumentEloStatusEnum.Pending.toLowerCase():
                return 'status-pending';
            default:
                return '';
        }
    }

    retryAnalysis(id: string): void {
        this.documentReportService.retryAnalysis(Number(id)).subscribe({
            next: () => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Analysis retry successfully started',
                });
            },
            error: () => {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Analysis retry failed' });
            },
        });
    }

    retryValidation(id: string): void {
        this.documentReportService.retryValidation(Number(id)).subscribe({
            next: () => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Validation retry successfully started',
                });
            },
            error: () => {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Validation retry failed' });
            },
        });
    }

    retryElo(id: string): void {
        this.documentReportService.retryElo(Number(id)).subscribe({
            next: () => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'ELO retry successfully started',
                });
            },
            error: () => {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'ELO retry failed' });
            },
        });
    }

    viewMergedDocument(documentId: string, processingSourceId: string): void {
        this.documentReportService.downloadMergedDocument(documentId, Number(processingSourceId)).subscribe((file) => {
            if (file) {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Merged document downloaded successfully',
                });
                openBlobInBrowser(file, 'application/pdf', 'merged.pdf');
            }
        });
    }
    downloadMergedDocument(documentId: string, processingSourceId: string): void {
        this.documentReportService.downloadMergedDocument(documentId, Number(processingSourceId)).subscribe((file) => {
            if (file) {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Merged document downloaded successfully',
                });
                downloadBlob(file, 'merged.pdf', 'application/pdf');
            }
        });
    }

    viewAttachment(attachmentId: string, fileName: string) {
        this.documentAttachmentService
            .downloadFile(this.selectedDocument?.id, Number(attachmentId))
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (response: Blob) => {
                    openBlobInBrowser(response, response.type, fileName);
                },
                error: (err: IProblemDetails) => {
                    this.dialogService.open(FeedbackDialogComponent, {
                        width: '40rem',
                        showHeader: false,
                        dismissableMask: true,
                        data: <IFeedbackDialogData>{
                            message: this.translateService.instant('ATTACHMENTS.UNSUCCESSFUL_DOWNLOAD'),
                            description: err.detail,
                            buttonText: this.translateService.instant('COMMON.BACK'),
                            isSuccess: false,
                        },
                    });
                },
            });
    }

    // Update the dialog close handler to remove the query parameter
    closeDocumentDetails(): void {
        this.selectedDocument = null;

        // Remove documentId from URL when dialog is closed
        this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { documentId: null },
            queryParamsHandling: 'merge',
        });
    }

    // Add a method to copy the document link to clipboard
    copyDocumentLink(): void {
        if (this.selectedDocument) {
            const url = window.location.href;

            navigator.clipboard.writeText(url).then(
                () => {
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Success',
                        detail: 'Document link copied to clipboard',
                    });
                },
                () => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to copy link',
                    });
                }
            );
        }
    }

    clearFilters(): void {
        this.filterForm.reset();
        this.applyFilters();
    }

    retryAllValidation(): void {
        if (!window.confirm('Are you sure you want to retry all validations? Only do this if you are sure...')) {
            return;
        }

        this.documentReportService.retryAllValidation().subscribe({
            next: () => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'All validations retried successfully',
                });
            },
        });
    }

    downloadBasedOnSp(): void {
        const input = window.prompt('Enter SP Identifiers line break separated:', '');

        if (input === null) return;

        const spIdentifiers = input
            .split('\n')
            .map((sp) => sp.trim())
            .filter((sp) => sp !== '');

        if (spIdentifiers.length === 0) {
            window.alert('No valid SP identifiers provided!');
            return;
        }

        const confirmMessage = `Download documents for:\n${spIdentifiers.join('\n')}`;
        if (!window.confirm(confirmMessage)) {
            return;
        }

        this.documentReportService
            .downloadBasedOnSp({ spIdentifiers })
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (file: Blob) => {
                    downloadBlob(file, 'sp-documents.zip', 'application/zip');
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Success',
                        detail: 'SP documents downloaded successfully',
                    });
                },
                error: (error) => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to download SP documents',
                    });
                },
            });
    }
}

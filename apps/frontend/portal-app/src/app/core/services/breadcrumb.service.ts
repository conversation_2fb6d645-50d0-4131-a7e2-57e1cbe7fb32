import { Injectable } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, NavigationEnd, ParamMap, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, filter, Observable } from 'rxjs';
import { IBreadcrumb } from '../interfaces/breadcrumb.interface';

@Injectable({
    providedIn: 'root',
})
export class BreadcrumbService {
    private breadcrumbsSubject = new BehaviorSubject<IBreadcrumb[]>([]);

    /**
     * Observable to get current breadcrumbs
     */
    breadcrumbs$: Observable<IBreadcrumb[]> = this.breadcrumbsSubject.asObservable();

    constructor(private router: Router, private route: ActivatedRoute, private translateService: TranslateService) {
        this.initRouterListener();
    }

    /**
     * Initialize router event listener to update breadcrumbs on navigation
     */
    private initRouterListener(): void {
        this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe(() => {
            const breadcrumbs = this.buildBreadcrumbsFromRoute(this.router.routerState.snapshot.root);
            this.breadcrumbsSubject.next(breadcrumbs);
        });
    }

    /**
     * Manually set breadcrumbs, overriding route data
     */
    setBreadcrumbs(breadcrumbs: IBreadcrumb[]): void {
        this.breadcrumbsSubject.next(this.translateAndResolveParams(breadcrumbs));
    }

    /**
     * Add a breadcrumb to the end of the current list
     */
    addBreadcrumb(breadcrumb: IBreadcrumb): void {
        const currentBreadcrumbs = this.breadcrumbsSubject.value;
        const resolvedBreadcrumb = this.translateAndResolveSingleBreadcrumb(breadcrumb);
        this.breadcrumbsSubject.next([...currentBreadcrumbs, resolvedBreadcrumb]);
    }

    /**
     * Remove the last breadcrumb from the list
     */
    removeLastBreadcrumb(): void {
        const currentBreadcrumbs = this.breadcrumbsSubject.value;
        if (currentBreadcrumbs.length > 0) {
            this.breadcrumbsSubject.next(currentBreadcrumbs.slice(0, -1));
        }
    }

    /**
     * Update a specific breadcrumb by index
     */
    updateBreadcrumb(index: number, breadcrumb: Partial<IBreadcrumb>): void {
        const currentBreadcrumbs = [...this.breadcrumbsSubject.value];
        if (index >= 0 && index < currentBreadcrumbs.length) {
            currentBreadcrumbs[index] = {
                ...currentBreadcrumbs[index],
                ...breadcrumb,
            };

            // Translate and resolve parameters if label is updated
            if (breadcrumb.label) {
                currentBreadcrumbs[index].label = this.resolveLabel(breadcrumb.label, this.route.snapshot.paramMap);
            }

            // Resolve parameters in URL if URL is updated
            if (breadcrumb.url) {
                currentBreadcrumbs[index].url = this.resolveRouteParams(breadcrumb.url, this.route.snapshot.paramMap);
            }

            this.breadcrumbsSubject.next(currentBreadcrumbs);
        }
    }

    /**
     * Set breadcrumb visibility
     */
    setBreadcrumbVisibility(index: number, visible: boolean): void {
        const currentBreadcrumbs = [...this.breadcrumbsSubject.value];
        if (index >= 0 && index < currentBreadcrumbs.length) {
            currentBreadcrumbs[index] = {
                ...currentBreadcrumbs[index],
                visible,
            };
            this.breadcrumbsSubject.next(currentBreadcrumbs);
        }
    }

    /**
     * Clear all breadcrumbs
     */
    clearBreadcrumbs(): void {
        this.breadcrumbsSubject.next([]);
    }

    /**
     * Build breadcrumbs from route data
     */
    private buildBreadcrumbsFromRoute(route: ActivatedRouteSnapshot): IBreadcrumb[] {
        let breadcrumbs: IBreadcrumb[] = [];

        // Get breadcrumbs from current route
        if (route.data['breadcrumb']) {
            breadcrumbs = this.translateAndResolveParams(route.data['breadcrumb'], route.paramMap);
        }

        // If not found, check child routes
        if (route.firstChild) {
            const childBreadcrumbs = this.buildBreadcrumbsFromRoute(route.firstChild);
            if (childBreadcrumbs.length > 0) {
                breadcrumbs = childBreadcrumbs;
            }
        }

        return breadcrumbs;
    }

    /**
     * Translate breadcrumb labels and resolve route parameters
     */
    private translateAndResolveParams(
        breadcrumbs: IBreadcrumb[] | IBreadcrumb | string | undefined,
        params?: ParamMap
    ): IBreadcrumb[] {
        if (!breadcrumbs) {
            return [];
        }

        if (Array.isArray(breadcrumbs)) {
            return breadcrumbs.map((breadcrumb) => this.translateAndResolveSingleBreadcrumb(breadcrumb, params));
        }

        if (typeof breadcrumbs === 'string') {
            const singleBreadcrumb: IBreadcrumb = { label: breadcrumbs, url: '' };
            return [this.translateAndResolveSingleBreadcrumb(singleBreadcrumb, params)];
        }

        return [this.translateAndResolveSingleBreadcrumb(breadcrumbs as IBreadcrumb, params)];
    }

    /**
     * Translate and resolve a single breadcrumb's parameters
     */
    private translateAndResolveSingleBreadcrumb(breadcrumb: IBreadcrumb, params?: ParamMap): IBreadcrumb {
        const routeParams = params || this.route.snapshot.paramMap;
        return {
            ...breadcrumb,
            label: this.resolveLabel(breadcrumb.label, routeParams),
            url: this.resolveRouteParams(breadcrumb.url, routeParams),
            visible: breadcrumb.visible !== false,
        };
    }

    /**
     * Replace route parameters in URL with actual values
     * Converts patterns like '/partner/:contactNo/projects/:projectNo' to actual values
     */
    private resolveRouteParams(url: string, params: ParamMap): string {
        if (!url || !params) return url;

        let resolvedUrl = url;

        // Replace route parameters (e.g., :contactNo, :projectNo)
        const paramPlaceholders = url.match(/:[a-zA-Z0-9]+/g);
        if (paramPlaceholders) {
            paramPlaceholders.forEach((placeholder) => {
                const paramName = placeholder.substring(1); // Remove the leading ':'
                const paramValue = params.get(paramName);
                if (paramValue) {
                    resolvedUrl = resolvedUrl.replace(placeholder, paramValue);
                }
            });
        }

        return resolvedUrl;
    }

    /**
     * Process a label by:
     * 1. Replacing route parameter placeholders in the label
     * 2. Translating it if it's a translation key
     */
    private resolveLabel(label: string, params: ParamMap): string {
        if (!label) return label;
        return label.replace(/{{\s*([^}]+?)\s*}}/g, (_match, key) => {
            const trimmedKey = key.trim();
            const paramValue = params.get(trimmedKey);
            if (paramValue != null) {
                return paramValue;
            }
            return this.translateService.instant(trimmedKey);
        });
    }
}

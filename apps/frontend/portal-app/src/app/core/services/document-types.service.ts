import { Injectable } from '@angular/core';
import { AccountTypeEnum, DocumentType } from '@api-clients/document';
import { IDropdownItem } from '@core/interfaces/dropdown-item.interface';
import { TranslateService } from '@ngx-translate/core';
import { map, Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class DocumentTypesService {
    constructor(private translateService: TranslateService) {}

    getDocumentTypesDropdown(): Observable<IDropdownItem[]> {
        return this.translateService.get('UPLOAD_DOCUMENT_PAGE.ATTACHMENTS.DOCUMENT_TYPE_SELECT').pipe(
            map((res) => [
                { label: res.DOCUMENT_TYPE_1, value: DocumentType.Invoice },
                { label: res.DOCUMENT_TYPE_2, value: DocumentType.CompletionCert },
                { label: res.DOCUMENT_TYPE_3, value: DocumentType.DeliveryNote },
                { label: res.DOCUMENT_TYPE_4, value: DocumentType.Waybill },
                { label: res.DOCUMENT_TYPE_5, value: DocumentType.Order },
                { label: res.DOCUMENT_TYPE_6, value: DocumentType.Contract },
                { label: res.DOCUMENT_TYPE_9, value: DocumentType.ReceiptConfirmation },
                { label: res.DOCUMENT_TYPE_10, value: DocumentType.Other },
            ])
        );
    }

    getInvoiceTypesDropdown(): Observable<IDropdownItem[]> {
        return this.translateService.get('UPLOAD_DOCUMENT_PAGE.DETAILS.INVOICE_SELECT').pipe(
            map((res) => [
                { label: res.INVOICE_TYPE_1, value: AccountTypeEnum.TigInvoice },
                { label: res.INVOICE_TYPE_2, value: AccountTypeEnum.NonTigAccount },
            ])
        );
    }
}

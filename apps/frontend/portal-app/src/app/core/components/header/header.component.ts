import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserProfileService } from '@api-clients/useradmin';
import { RoleName } from '@core/enums/rolename.enum';
import { IHeaderItemHelper } from '@core/interfaces/header-item-helper.interface';
import { IUser } from '@core/interfaces/user.interface';
import { AuthService } from '@core/services/auth.service';
import { AppBaseSubscriptionComponent } from '@shared/components/app-base-subscription.component';
import { distinctUntilChanged, take, takeUntil } from 'rxjs';

@Component({
    selector: 'partner-portal-header',
    templateUrl: './header.component.html',
    styleUrls: ['header.component.scss'],
})
export class HeaderComponent extends AppBaseSubscriptionComponent implements OnInit {
    constructor(
        private authService: AuthService,
        private userProfileService: UserProfileService,
        private router: Router
    ) {
        super();
    }
    headerItems: IHeaderItemHelper[] = [];
    sideBarVisible = false;
    profileName: string;
    companyName: string;
    isAdmin = false;
    isPartner = false;
    isHeadOfEngineer = false;
    ngOnInit(): void {
        this.authService.isLoggedIn$.pipe(takeUntil(this.destroy$), distinctUntilChanged()).subscribe((isLoggedIn) => {
            if (isLoggedIn) {
                this.authService.currentUser$.pipe(take(1)).subscribe((user) => {
                    this.userProfileService
                        .getProfileData()
                        .pipe(takeUntil(this.destroy$))
                        .subscribe((res) => {
                            if (user.roles?.includes(RoleName.Partner)) {
                                this.profileName = res.partner?.name;
                                this.companyName = res.partner?.companyName;
                                this.isPartner = true;
                            } else {
                                this.profileName = res.marketEmployee?.name;
                                this.companyName = res.marketEmployee?.companyName;
                                this.isHeadOfEngineer = user.roles?.includes(RoleName.HeadOfEngineer);
                            }
                            if (res.isAdminUser) {
                                this.profileName = 'Admin';
                                this.companyName = res.email;
                                this.isAdmin = true;
                            }
                            this.createHeaderItems(user, res.email);
                        });
                });
            }
        });
    }

    createHeaderItems(user: IUser, email: string) {
        if (user.roles?.includes(RoleName.Partner) && user.documentModule) {
            this.headerItems = [
                /* { content: 'PRIMARY_NAVIGATION.PROFORMA_INVOICE', link: '', isActive: false },
            { content: 'PRIMARY_NAVIGATION.COMPLETION_CERT_LIST', link: '', isActive: false },
            { content: 'PRIMARY_NAVIGATION.SUBMIT_REQUEST', link: '', isActive: false },
            { content: 'PRIMARY_NAVIGATION.REQUEST_LIST', link: '', isActive: false }, */
                {
                    content: 'PRIMARY_NAVIGATION.UPLOAD_DOCUMENT',
                    link: '/partner/document/create',
                    visibleToRoles: [RoleName.Partner],
                },
                {
                    content: 'PRIMARY_NAVIGATION.UPLOAD_DOCUMENT_LIST',
                    link: '/partner/documents',
                    visibleToRoles: [RoleName.Partner],
                },
            ];
        }
        if (user.roles?.includes(RoleName.Administrator)) {
            this.headerItems = [
                {
                    content: 'PRIMARY_NAVIGATION.CALENDAR_HANDLING',
                    link: '/admin/calendar-handling',
                    visibleToRoles: [RoleName.Administrator],
                },
            ];

            if (email.includes('@ibtadmin.hu')) {
                this.headerItems.push({
                    content: 'Document Status Dashboard',
                    link: '/admin/document-status-dashboard',
                    visibleToRoles: [RoleName.Administrator],
                });
            }
        }
        if (user.roles?.includes(RoleName.Secretary)) {
            this.headerItems = [
                {
                    content: 'PRIMARY_NAVIGATION.UPLOAD_DOCUMENT',
                    link: '/secretary/document/create',
                    visibleToRoles: [RoleName.Secretary],
                },
                {
                    content: 'PRIMARY_NAVIGATION.SECRETARY_UPLOAD_DOCUMENT_LIST',
                    link: '/secretary/documents-list',
                    visibleToRoles: [RoleName.Secretary],
                },
                {
                    content: 'PRIMARY_NAVIGATION.PARTNER_DOCUMENTS',
                    link: '/secretary/documents-list-partner',
                    visibleToRoles: [RoleName.Secretary],
                },
            ];
        }
    }
    public get RoleName() {
        return RoleName;
    }

    clickOutSide() {
        this.sideBarVisible = false;
    }

    toggleSideBar() {
        this.sideBarVisible = !this.sideBarVisible;
    }

    isActive(route: string): boolean {
        return this.router.isActive(route, true);
    }

    doLogOut() {
        this.authService.logout();
    }
}

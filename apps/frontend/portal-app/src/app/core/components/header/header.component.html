<header
    class="bg-gray1000 px-3 lg:px-7 shadow-2 flex lg:justify-content-between relative lg:static gap-3"
    style="min-height: 84px"
>
    <div class="cursor-pointer inline-block lg:hidden align-self-center" (click)="toggleSideBar()">
        <div class="hamburger" [ngClass]="sideBarVisible ? 'open' : ''">
            <div></div>
        </div>
    </div>
    <ul
        class="mx-auto lg:m-0 list-none p-0 flex select-none align-items-center justify-content-center flex-row cursor-pointer w-full"
    >
        <li routerLink="/">
            <img
                src="assets/images/market_logo_w_text.svg"
                alt="market logo"
                class="w-6rem mr-0 lg:mr-3 align-self-center"
            />
        </li>
        <li routerLink="/">
            <p class="portal-header-text">{{ 'COMMON.SITENAME' | translate }}</p>
        </li>

        <li class="ml-4 w-full hidden lg:flex justify-content-start">
            <a
                *ngIf="isPartner"
                class="mr-3 p-1 bg-green600 border-round g-body-2 text-black-alpha-90 font-medium hover:bg-green-700 transition-all transition-duration-500 transition-ease-out"
                routerLink="partner/tutorial"
                [routerLinkActive]="['bg-hovered-color']"
                [routerLinkActiveOptions]="{ exact: true }"
                >{{ 'COMMON.TUTORIAL' | translate }}</a
            >
            <ng-container *ngFor="let item of headerItems">
                <a
                    *ifRoles="item.visibleToRoles"
                    class="mr-3 p-1 bg-green600 border-round g-body-2 text-black-alpha-90 font-medium hover:bg-green-700 transition-all transition-duration-500 transition-ease-out"
                    [routerLink]="item.link"
                    [routerLinkActive]="['bg-hovered-color']"
                    >{{ item.content | translate }}</a
                >
            </ng-container>
        </li>
    </ul>

    <div class="hidden p-0 m-0 lg:flex select-none flex-column align-items-center lg:flex-row">
        <header-dropdown [companyName]="companyName" [profileName]="profileName" (logoutPressed)="doLogOut()">
        </header-dropdown>
    </div>
</header>
<p-sidebar [(visible)]="sideBarVisible" [appendTo]="sideBarDiv" [modal]="false" [showCloseIcon]="false">
    <ng-template pTemplate="content">
        <div class="mx-2 mb-4">
            <header-dropdown-mobile
                [companyName]="companyName"
                [profileName]="profileName"
                (logoutPressed)="doLogOut()"
            ></header-dropdown-mobile>
        </div>
        <div
            *ngFor="let item of headerItems"
            class="mx-2 my-1 h-3rem border-round flex align-items-center transition-all transition-duration-500 transition-ease-out hover:bg-green-600 text-50 hover:text-color"
            [routerLink]="item.link"
            [routerLinkActive]="['text-primary-500']"
            [routerLinkActiveOptions]="{ exact: true }"
            (click)="toggleSideBar()"
        >
            <p class="ml-3 headline-6">{{ item.content | translate }}</p>
        </div>
    </ng-template>
</p-sidebar>

<div class="lg:hidden" clickOutside [disabled]="!sideBarVisible" (clickOutside)="clickOutSide()" #sideBarDiv></div>

import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'badge',
    template: ` <label
        *ngIf="status"
        [ngClass]="autoMargin ? 'mt-2 lg:mr-6 lg:mt-0' : ''"
        class="flex justify-content-center gap-2 w-8rem min-w-max text-center text-white border-round-3xl py-2 px-3 g-button-font fadein animation-duration-250"
        [ngClass]="classes"
        ><img *ngIf="icon" src="assets/images/{{ icon }}" /> {{ label }}
    </label>`,
})
export class BadgeComponent implements OnInit {
    constructor(private translateService: TranslateService) {}

    @Input() status: string;

    @Input() autoMargin = true;

    @Input() overrideClasses?: string;

    private statusArray: Status[];

    ngOnInit(): void {
        const translateKeys: string[] = [
            'BADGE_LABEL.VÁLLALKOZÁS',
            'BADGE_LABEL.KIVITELEZÉS',
            'BADGE_LABEL.ÁTADÁS KIVITELEZÉSNEK',
            'BADGE_LABEL.ÁTADÁS GARANCIÁLIS IDŐSZAKRA',
            'BADGE_LABEL.GARANCIÁLIS',
            'BADGE_LABEL.NYITOTT',
            'BADGE_LABEL.SZERKESZTÉS ALATT',
            'BADGE_LABEL.BEKÜLDVE',
            'BADGE_LABEL.FOLYAMATBAN',
            'BADGE_LABEL.VISSZAUTASÍTVA',
            'BADGE_LABEL.JÓVÁHAGYVA',
            'BADGE_LABEL.ALÁÍRT',
            'BADGE_LABEL.SZERZŐDÉSMÓDOSÍTÁS',
            'BADGE_LABEL.ELBÍRÁLANDÓ',
            'BADGE_LABEL.ELBÍRÁLT',
            'BADGE_LABEL.LEZÁRT',
            'BADGE_LABEL.TÖRÖLVE',
            'BADGE_LABEL.AKTÍV',
            'BADGE_LABEL.BCCHECKFAILED',
            'BADGE_LABEL.ELŐFELTÉTELEK JÓVÁHAGYÁSA FOLYAMATBAN',
            'ENUMERATIONS.ELOWORKFLOWSTATUS.18 DOKUMENTUM KÉSZÍTÉS ALATT',
            'ENUMERATIONS.ELOWORKFLOWSTATUS.21 IGAZOLÁS ALATT',
            'ENUMERATIONS.ELOWORKFLOWSTATUS.30 JÓVÁHAGYÁS ALATT',
            'ENUMERATIONS.ELOWORKFLOWSTATUS.40 JÓVÁHAGYVA',
            'ENUMERATIONS.ELOWORKFLOWSTATUS.60 ELKÜLDVE',
            'ENUMERATIONS.ELOWORKFLOWSTATUS.80 ELUTASÍTVA',
            'COMPLETION_CERT_DETAILS.END_TIG_REQUEST.APPROVED',
            'COMPLETION_CERT_DETAILS.END_TIG_REQUEST.IN_PROGRESS',
            'DOCUMENT_LIST_PAGE.BADGE_STATUS_DRAFT',
            'DOCUMENT_LIST_PAGE.BADGE_STATUS_SUBMITTED',
            'DOCUMENT_LIST_PAGE.BADGE_STATUS_SUCCESS',
            'DOCUMENT_LIST_PAGE.BADGE_STATUS_FAILED',
            'DOCUMENT_LIST_PAGE.BADGE_STATUS_DEFAULT',
            'DOCUMENT_LIST_PAGE.BADGE_STATUS_PROCESSING',
        ];
        this.translateService.stream(translateKeys).subscribe((translations) => {
            this.statusArray = [
                {
                    id: 'Vállalkozás',
                    label: translations['BADGE_LABEL.VÁLLALKOZÁS'],
                    classes: 'bg-yellow-500',
                    icon: '',
                },
                {
                    id: 'Kivitelezés',
                    label: translations['BADGE_LABEL.KIVITELEZÉS'],
                    classes: 'bg-green-low-color',
                    icon: '',
                },
                {
                    id: 'Átadás kivitelezésnek',
                    label: translations['BADGE_LABEL.ÁTADÁS KIVITELEZÉSNEK'],
                    classes: 'bg-disabled-color',
                    icon: '',
                },
                {
                    id: 'Átadás garanciális időszakra',
                    label: translations['BADGE_LABEL.ÁTADÁS GARANCIÁLIS IDŐSZAKRA'],
                    classes: 'bg-disabled-color',
                    icon: '',
                },
                {
                    id: 'Garanciális',
                    label: translations['BADGE_LABEL.GARANCIÁLIS'],
                    classes: 'bg-link-color-blue',
                    icon: '',
                },
                {
                    id: 'Nyitott',
                    label: translations['BADGE_LABEL.NYITOTT'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: 'Szerkesztés alatt',
                    label: translations['BADGE_LABEL.SZERKESZTÉS ALATT'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: 'Beküldve',
                    label: translations['BADGE_LABEL.BEKÜLDVE'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: 'Folyamatban',
                    label: translations['BADGE_LABEL.FOLYAMATBAN'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: 'Visszautasítva',
                    label: translations['BADGE_LABEL.VISSZAUTASÍTVA'],
                    classes: 'bg-yellow-500',
                    icon: '',
                },
                {
                    id: 'Jóváhagyva',
                    label: translations['BADGE_LABEL.JÓVÁHAGYVA'],
                    classes: 'bg-green-low-color',
                    icon: '',
                },
                { id: 'Aláírt', label: translations['BADGE_LABEL.ALÁÍRT'], classes: 'bg-green-low-color', icon: '' },
                {
                    id: 'Szerződésmódosítás',
                    label: translations['BADGE_LABEL.SZERZŐDÉSMÓDOSÍTÁS'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: 'Elbírálandó',
                    label: translations['BADGE_LABEL.ELBÍRÁLANDÓ'],
                    classes: 'bg-red-critical-color',
                    icon: '',
                },
                { id: 'Elbírált', label: translations['BADGE_LABEL.ELBÍRÁLT'], classes: 'bg-green500', icon: '' },
                {
                    id: 'Lezárt',
                    label: translations['BADGE_LABEL.LEZÁRT'],
                    classes: 'bg-link-pressed-color',
                    icon: 'lock_icon.svg',
                },
                {
                    id: 'Törölve',
                    label: translations['BADGE_LABEL.TÖRÖLVE'],
                    classes: 'bg-black-alpha-20',
                    icon: 'trash_can_icon.svg',
                },
                {
                    id: 'Aktív',
                    label: translations['BADGE_LABEL.AKTÍV'],
                    classes: 'bg-link-pressed-color',
                    icon: 'check_file_icon.svg',
                },
                {
                    id: 'bcCheckFailed',
                    label: translations['BADGE_LABEL.BCCHECKFAILED'],
                    classes: 'bg-link-pressed-color',
                    icon: '',
                },
                {
                    id: 'Előfeltételek jóváhagyása folyamatban',
                    label: translations['BADGE_LABEL.ELŐFELTÉTELEK JÓVÁHAGYÁSA FOLYAMATBAN'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: '18 Dokumentum készítés alatt',
                    label: translations['ENUMERATIONS.ELOWORKFLOWSTATUS.18 DOKUMENTUM KÉSZÍTÉS ALATT'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: '21 Igazolás alatt',
                    label: translations['ENUMERATIONS.ELOWORKFLOWSTATUS.21 IGAZOLÁS ALATT'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: '30 Jóváhagyás alatt',
                    label: translations['ENUMERATIONS.ELOWORKFLOWSTATUS.30 JÓVÁHAGYÁS ALATT'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: '40 Jóváhagyva',
                    label: translations['ENUMERATIONS.ELOWORKFLOWSTATUS.40 JÓVÁHAGYVA'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: '60 Elküldve',
                    label: translations['ENUMERATIONS.ELOWORKFLOWSTATUS.60 ELKÜLDVE'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: '80 Elutasítva',
                    label: translations['ENUMERATIONS.ELOWORKFLOWSTATUS.80 ELUTASÍTVA'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
                {
                    id: 'Approved',
                    label: translations['COMPLETION_CERT_DETAILS.END_TIG_REQUEST.APPROVED'],
                    classes: 'bg-green-low-color',
                    icon: '',
                },
                {
                    id: 'Created',
                    label: translations['COMPLETION_CERT_DETAILS.END_TIG_REQUEST.IN_PROGRESS'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: 'PrimarySent',
                    label: translations['COMPLETION_CERT_DETAILS.END_TIG_REQUEST.IN_PROGRESS'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: 'SecondarySent',
                    label: translations['COMPLETION_CERT_DETAILS.END_TIG_REQUEST.IN_PROGRESS'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: 'Draft',
                    label: translations['DOCUMENT_LIST_PAGE.BADGE_STATUS_DRAFT'],
                    classes: 'bg-gray-400',
                    icon: '',
                },
                {
                    id: 'Submitted',
                    label: translations['DOCUMENT_LIST_PAGE.BADGE_STATUS_SUBMITTED'],
                    classes: 'bg-green-low-color',
                    icon: '',
                },
                {
                    id: 'Success',
                    label: translations['DOCUMENT_LIST_PAGE.BADGE_STATUS_SUCCESS'],
                    classes: 'bg-green-low-color',
                    icon: '',
                },
                {
                    id: 'Failed',
                    label: translations['DOCUMENT_LIST_PAGE.BADGE_STATUS_FAILED'],
                    classes: 'bg-red-critical-color',
                    icon: '',
                },
                {
                    id: 'Default',
                    label: translations['DOCUMENT_LIST_PAGE.BADGE_STATUS_DEFAULT'],
                    classes: 'bg-orange-high-color',
                    icon: '',
                },
                {
                    id: 'Processing',
                    label: translations['DOCUMENT_LIST_PAGE.BADGE_STATUS_PROCESSING'],
                    classes: 'bg-blue-color',
                    icon: '',
                },
            ];
        });
    }

    get label() {
        return this.statusArray.find((s) => s.id === this.status)?.label ?? this.status;
    }

    get classes() {
        return (
            this.overrideClasses ?? this.statusArray.find((s) => s.id === this.status)?.classes ?? 'bg-disabled-color'
        );
    }
    get icon() {
        return this.statusArray.find((s) => s.id === this.status)?.icon;
    }
}

interface Status {
    id: string;
    label: string;
    classes: string;
    icon: string;
}

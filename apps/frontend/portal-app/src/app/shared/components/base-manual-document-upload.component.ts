import { FileInfo } from '@api-clients/contracts';
import { AccountTypeEnum, DocumentType } from '@api-clients/document';
import { IPartnerDocumentAttachment } from '@core/interfaces/document-attachment.interface';
import { IDropdownItem } from '@core/interfaces/dropdown-item.interface';
import { IUploadDialogData } from '@core/interfaces/upload-dialog.interface';
import { UploadDialogComponent } from '@shared/components/upload-dialog/upload-dialog.component';
import { BehaviorSubject, take, takeUntil } from 'rxjs';
import { BaseDocumentUploadComponent } from './base-document-upload.component';

export abstract class BaseManualDocumentUploadComponent extends BaseDocumentUploadComponent {
    // Abstract properties
    abstract documentAttachments$: BehaviorSubject<IPartnerDocumentAttachment[]>;
    abstract documentTypes: IDropdownItem[];
    abstract selectedInvoiceType: AccountTypeEnum | null;
    abstract fileCountLimit: number;
    abstract fileSizeLimit: number;
    abstract permittedExtensions: string[];

    /**
     * Add a new attachment to the list
     */
    protected addNewAttachment(): void {
        const currentAttachments = this.documentAttachments$.value;
        currentAttachments.push({
            documentType: null,
            file: null,
            availableDocumentTypes: [...this.documentTypes],
            spIdentifier: null,
        });
        this.documentAttachments$.next(currentAttachments);

        // Update available document types
        this.updateAvailableDocumentTypes();
    }

    /**
     * Remove an attachment from the list
     */
    protected removeAttachment(index: number): void {
        const currentAttachments = this.documentAttachments$.value;
        if (currentAttachments[index].file) {
            this.documentAttachmentService
                .revertFile(this.contextId, currentAttachments[index].file.fileId)
                .pipe(take(1))
                .subscribe();
        }
        currentAttachments.splice(index, 1);
        this.documentAttachments$.next(currentAttachments);

        // Always ensure at least one attachment row is available
        if (currentAttachments.length === 0) {
            this.addNewAttachment();
        } else {
            // Revalidate document type options after deletion
            this.updateAvailableDocumentTypes();
        }
    }

    /**
     * Handle document type change for an attachment
     */
    protected onDocumentTypeChange(index: number): void {
        const currentAttachments = this.documentAttachments$.value;
        if (currentAttachments[index].file) {
            if (currentAttachments[index].file?.fileId) {
                this.documentAttachmentService
                    .revertFile(this.contextId, currentAttachments[index].file?.fileId)
                    .pipe(take(1))
                    .subscribe();
            }
            currentAttachments[index].file = null;
            this.documentAttachments$.next([...currentAttachments]);
        }

        // Update available document types for all attachments
        this.updateAvailableDocumentTypes();
    }

    /**
     * Handle invoice type change
     */
    protected onInvoiceTypeChange(invoiceType: AccountTypeEnum): void {
        this.selectedInvoiceType = invoiceType;
        this.updateAvailableDocumentTypes();
    }

    /**
     * Dynamically filter document types based on what's already selected
     */
    protected updateAvailableDocumentTypes(): void {
        const currentAttachments = this.documentAttachments$.value;

        // Count documents by type
        const invoiceCount = currentAttachments.filter((a) => this.isInvoiceType(a.documentType)).length;
        const certificateCount = currentAttachments.filter(
            (a) => a.documentType === DocumentType.CompletionCert
        ).length;

        // Create a filtered version of document types for each attachment
        currentAttachments.forEach((attachment) => {
            // Skip if this attachment already has a document type selected
            if (attachment.documentType !== null) {
                return;
            }

            // Create filtered document types list - start with all document types
            let filteredDocumentTypes = [...this.documentTypes];

            // If we already have at least 1 invoice AND at least 1 certificate, then:
            if (invoiceCount >= 1 && certificateCount >= 1) {
                // If there's more than 1 invoice, don't allow more certificates
                if (invoiceCount > 1) {
                    filteredDocumentTypes = filteredDocumentTypes.filter(
                        (dt) => dt.value !== DocumentType.CompletionCert
                    );
                }
                // If there's more than 1 certificate, don't allow more invoices
                else if (certificateCount > 1) {
                    filteredDocumentTypes = filteredDocumentTypes.filter(
                        (dt) => !this.isInvoiceType(dt.value as string)
                    );
                }
            }

            // Store the filtered types in the attachment
            attachment.availableDocumentTypes = filteredDocumentTypes;
        });

        if (this.selectedInvoiceType !== AccountTypeEnum.TigInvoice) {
            currentAttachments.forEach((attachment) => {
                attachment.availableDocumentTypes = this.documentTypes.filter(
                    (dt) => dt.value !== DocumentType.CompletionCert
                );
                if (attachment.documentType === DocumentType.CompletionCert) {
                    attachment.documentType = null;
                }
            });
        }
        // Update the attachments
        this.documentAttachments$.next([...currentAttachments]);
    }

    /**
     * Open file upload modal for a specific attachment index
     */
    protected openFileuploadModal(index: number): void {
        const ref = this.dialogService.open(UploadDialogComponent, {
            width: '40rem',
            showHeader: false,
            dismissableMask: false,
            data: <IUploadDialogData>{
                accept: this.permittedExtensions,
                fileLimit: 1, // Only one file per document type
                maxFileSizeInBytes: this.fileSizeLimit,
                contextObjectId: this.contextId,
                alreadyUploadedFileCount: 0,
                multiple: false,
                uploadFileFn: (contextObjectId: string, files: File[]) =>
                    this.documentAttachmentService.uploadFile(contextObjectId, files, 'events', true),
                revertFileFn: (contextObjectId: string, fileId: number) =>
                    this.documentAttachmentService.revertFile(contextObjectId, fileId),
            },
        });
        ref.onClose.pipe(takeUntil(this.destroy$)).subscribe((res: FileInfo[]) => {
            if (res && res.length > 0) {
                const currentAttachments = this.documentAttachments$.value;
                currentAttachments[index].file = res[0];
                this.documentAttachments$.next([...currentAttachments]);
            }
        });
    }

    /**
     * Validate the form based on submit requirements
     */
    protected isFormValid(submit: boolean): boolean {
        const attachments = this.documentAttachments$.value;

        // Must have at least one attachment regardless of document type
        if (attachments.length === 0) {
            return false;
        }

        // Must have an document type selected
        if (this.selectedInvoiceType == null) {
            return false;
        }

        // Must have document type selected for each attachment
        if (attachments.some((attachment) => attachment.documentType === null || attachment.file === null)) {
            return false;
        }

        if (submit) {
            // For TigInvoice, we need specific document types
            if (this.selectedInvoiceType === AccountTypeEnum.TigInvoice) {
                const hasInvoice = attachments.some((attachment) => this.isInvoiceType(attachment.documentType));

                const hasCompletionCert = attachments.some(
                    (attachment) => attachment.documentType === DocumentType.CompletionCert
                );

                return hasInvoice && hasCompletionCert;
            } else {
                // Other document types require at least one invoice
                const hasInvoice = attachments.some((attachment) => this.isInvoiceType(attachment.documentType));

                return hasInvoice;
            }
        }

        // For other document types, just need at least one attachment
        return attachments.length > 0;
    }

    /**
     * Check if a document type is an invoice type
     */
    protected isInvoiceType(type: DocumentType | string): boolean {
        return (
            type === DocumentType.Invoice ||
            type === DocumentType.CorrectionInvoice ||
            type === DocumentType.AdvanceInvoice
        );
    }

    /**
     * Check if the last attachment in the list is complete (has both type and file)
     */
    protected isLastAttachmentComplete(attachments: IPartnerDocumentAttachment[]): boolean {
        if (attachments.length === 0) {
            return true;
        }

        const lastAttachment = attachments[attachments.length - 1];
        return lastAttachment.documentType !== null && lastAttachment.file !== null;
    }
}

import { Router } from '@angular/router';
import { FileInfo } from '@api-clients/contracts';
import { AttachmentService as DocumentAttachmentService } from '@api-clients/document';
import { IFeedbackDialogData } from '@core/interfaces/feedback-dialog.interface';
import { IProblemDetails } from '@core/interfaces/problem-details.interface';
import { downloadBlob, openBlobInBrowser } from '@core/utils/common';
import { TranslateService } from '@ngx-translate/core';
import { AppBaseSubscriptionComponent } from '@shared/components/app-base-subscription.component';
import { FeedbackDialogComponent } from '@shared/components/feedback-dialog/feedback-dialog.component';
import { DialogService } from 'primeng/dynamicdialog';
import { takeUntil } from 'rxjs';

export abstract class BaseDocumentUploadComponent extends AppBaseSubscriptionComponent {
    // Abstract properties
    protected abstract contextId: string | null;
    protected abstract documentAttachmentService: DocumentAttachmentService;
    protected abstract router: Router;
    protected abstract translateService: TranslateService;
    protected abstract dialogService: DialogService;

    /**
     * Reloads the current page by navigating away and back
     */
    protected reloadPage(): void {
        const currentUrl = this.router.url;
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            this.router.navigate([currentUrl]);
        });
    }

    /**
     * Shows a feedback dialog with the provided options
     */
    protected showFeedbackDialog(options: {
        messageKey: string;
        description?: string;
        buttonTextKey: string;
        isSuccess: boolean;
        onButton1Click?: () => void;
        button2TextKey?: string;
        onButton2Click?: () => void;
    }): void {
        this.dialogService.open(FeedbackDialogComponent, {
            width: '40rem',
            showHeader: false,
            dismissableMask: true,
            data: <IFeedbackDialogData>{
                message: this.translateService.instant(options.messageKey),
                description: options.description,
                buttonText: options.buttonTextKey ? this.translateService.instant(options.buttonTextKey) : null,
                onButton1Click: options.onButton1Click,
                isSuccess: options.isSuccess,
                button2Text: options.button2TextKey ? this.translateService.instant(options.button2TextKey) : null,
                onButton2Click: options.onButton2Click,
            },
        });
    }

    /**
     * Downloads a file attachment
     */
    protected downloadAttachment(attachment: FileInfo): void {
        this.documentAttachmentService
            .downloadFile(this.contextId, attachment.fileId)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (response: Blob) => {
                    downloadBlob(response, attachment.fileName, attachment.mimeType);
                },
                error: (err: IProblemDetails) => {
                    this.dialogService.open(FeedbackDialogComponent, {
                        width: '40rem',
                        showHeader: false,
                        dismissableMask: true,
                        data: <IFeedbackDialogData>{
                            message: this.translateService.instant('ATTACHMENTS.UNSUCCESSFUL_DOWNLOAD'),
                            description: err.detail,
                            buttonText: this.translateService.instant('COMMON.BACK'),
                            isSuccess: false,
                        },
                    });
                },
            });
    }

    /**
     * Views a file attachment in the browser
     */
    protected viewAttachment(attachment: FileInfo): void {
        this.documentAttachmentService
            .downloadFile(this.contextId, attachment.fileId)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (response: Blob) => {
                    openBlobInBrowser(response, attachment.mimeType, attachment.fileName);
                },
                error: (err: IProblemDetails) => {
                    this.dialogService.open(FeedbackDialogComponent, {
                        width: '40rem',
                        showHeader: false,
                        dismissableMask: true,
                        data: <IFeedbackDialogData>{
                            message: this.translateService.instant('ATTACHMENTS.UNSUCCESSFUL_DOWNLOAD'),
                            description: err.detail,
                            buttonText: this.translateService.instant('COMMON.BACK'),
                            isSuccess: false,
                        },
                    });
                },
            });
    }
}

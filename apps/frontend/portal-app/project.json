{"name": "frontend-portal-app", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/frontend/portal-app/src", "prefix": "partner-portal", "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"aot": true, "outputHashing": "all", "outputPath": "dist/apps/frontend/portal-app", "index": "apps/frontend/portal-app/src/index.html", "main": "apps/frontend/portal-app/src/main.ts", "polyfills": "apps/frontend/portal-app/src/polyfills.ts", "tsConfig": "apps/frontend/portal-app/tsconfig.app.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["apps/frontend/portal-app/src/scss/globals"]}, "styles": ["node_modules/primeflex/primeflex.css", "apps/frontend/portal-app/src/theme.css", "node_modules/primeng/resources/primeng.min.css", "apps/frontend/portal-app/src/scss/styles.scss", "node_modules/primeicons/primeicons.css", "apps/frontend/portal-app/src/assets/styles/flags.css"], "scripts": [], "allowedCommonJsDependencies": ["crypto-js", "moment"]}, "configurations": {"release.DEV": {"assets": ["apps/frontend/portal-app/src/assets", "apps/frontend/portal-app/src/web.config"], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "apps/frontend/portal-app/src/environments/environment.ts", "with": "apps/frontend/portal-app/src/environments/environment.release.DEV.ts"}], "outputHashing": "all", "aot": true}, "release.UAT": {"assets": ["apps/frontend/portal-app/src/assets", "apps/frontend/portal-app/src/web.config"], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "apps/frontend/portal-app/src/environments/environment.ts", "with": "apps/frontend/portal-app/src/environments/environment.release.UAT.ts"}], "outputHashing": "all", "aot": true}, "release.PROD": {"assets": ["apps/frontend/portal-app/src/assets", "apps/frontend/portal-app/src/web.config"], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "apps/frontend/portal-app/src/environments/environment.ts", "with": "apps/frontend/portal-app/src/environments/environment.release.PROD.ts"}], "outputHashing": "all", "aot": true}, "development": {"assets": ["apps/frontend/portal-app/src/assets"], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "release.UAT"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "frontend-portal-app:build:production"}, "development": {"browserTarget": "frontend-portal-app:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "apps/frontend/portal-app/proxy.conf.json"}}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "frontend-portal-app:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["apps/frontend/portal-app/**/*.ts", "apps/frontend/portal-app/**/*.html"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/frontend/portal-app/jest.config.ts", "passWithNoTests": true}}}, "tags": []}
{
    "UseFakeDocumentProcessingService": false,
    "Model": "V1.0.0",
    "SplitMode": "none", // auto, none
    "Serilog": {
        "Using": ["Serilog.Sinks.File"],
        "MinimumLevel": {
            "Default": "Information",
            "Override": {
                "Microsoft": "Warning",
                "System": "Warning"
            }
        },
        "WriteTo": [
            {
                "Name": "File",
                "Args": {
                    "path": "logs/benchmark-.log",
                    "rollingInterval": "Day",
                    "retainedFileCountLimit": 30,
                    "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext} {Message:lj} {NewLine}{Exception}"
                }
            }
        ],
        "Enrich": ["FromLogContext"],
        "Properties": {
            "ApplicationName": "PartnerPortal.AiBenchmark.Console"
        }
    },
    "AzureAI": {
        "DocumentIntelligence": {
            "Endpoint": "https://marketinvoiceflow.cognitiveservices.azure.com/",
            "Key": "Encrypted:PP_DOCUMENT_INTELLIGENCE_KEY"
        }
    },
    "Mode": "Classification"
}

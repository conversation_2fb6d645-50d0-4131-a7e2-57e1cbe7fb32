using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services;

public interface ICoreService
{
    Task<string> SetTestDataPathAsync(string path);
    List<string> GetFiles(string folderPath);
    void ConvertToFlatFolderStructure(string folderPath);
}

public record AnalysisProgress(int ProcessedFiles, int TotalFiles, string CurrentFile);

public class CoreService : ICoreService
{
    private readonly IResultsService _resultsService;
    private readonly IDocumentProcessingService _documentService;
    private readonly ILogger<CoreService> _logger;
    private readonly IConfigurationService _configService;

    public CoreService(
        IResultsService resultsService,
        IDocumentProcessingService documentService,
        ILogger<CoreService> logger,
        IConfigurationService configService
    )
    {
        _resultsService = resultsService;
        _documentService = documentService;
        _logger = logger;
        _configService = configService;
    }

    public async Task<string> SetTestDataPathAsync(string path)
    {
        _logger.LogInformation("Setting test data path to: {Path}", path);

        var cleanPath = path.Trim('"', '\'');

        if (!Directory.Exists(cleanPath))
        {
            _logger.LogWarning("Directory does not exist: {Path}", cleanPath);
            return $"Directory does not exist: {cleanPath}";
        }

        var testFiles = GetFiles(cleanPath);
        var config = await _configService.LoadConfigurationAsync();
        var updatedConfig = config with { ClassificationTestDataPath = cleanPath };
        await _configService.SaveConfigurationAsync(updatedConfig);

        _logger.LogInformation("Test data path set successfully. Found {FileCount} supported files", testFiles.Count);

        return testFiles.Count == 0
            ? $"Path set successfully, but no supported files found. Found {testFiles.Count} file(s)."
            : $"Path set successfully. Found {testFiles.Count} supported file(s).";
    }

    public List<string> GetFiles(string folderPath)
    {
        if (string.IsNullOrEmpty(folderPath) || !Directory.Exists(folderPath))
        {
            _logger.LogDebug("Invalid folder path or folder does not exist: {Path}", folderPath);
            return [];
        }

        var supportedExtensions = new[] { ".pdf", ".png", ".jpg", ".jpeg", ".tiff", ".bmp", ".tif" };

        var files = Directory
            .GetFiles(folderPath, "*", SearchOption.TopDirectoryOnly)
            .Where(file => supportedExtensions.Contains(Path.GetExtension(file).ToLowerInvariant()))
            .OrderBy(f => Path.GetFileName(f))
            .ToList();

        _logger.LogDebug("Found {FileCount} supported files in {Path}", files.Count, folderPath);
        return files;
    }

    public void ConvertToFlatFolderStructure(string folderPath)
    {
        var cleanPath = folderPath.Trim('"', '\'');
        if (string.IsNullOrEmpty(cleanPath) || !Directory.Exists(cleanPath))
        {
            _logger.LogError("Invalid folder path: {Path}", cleanPath);
            throw new DirectoryNotFoundException($"Directory not found: {cleanPath}");
        }

        var supportedExtensions = new[] { ".pdf", ".png", ".jpg", ".jpeg", ".tiff", ".bmp", ".tif" };

        var allFiles = Directory
            .GetFiles(cleanPath, "*", SearchOption.AllDirectories)
            .Where(file => supportedExtensions.Contains(Path.GetExtension(file).ToLowerInvariant()))
            .ToList();

        _logger.LogInformation("Found {FileCount} files to process in {Path}", allFiles.Count, cleanPath);

        var processedFiles = 0;
        var errors = new List<string>();

        foreach (var file in allFiles)
        {
            try
            {
                var relativePath = Path.GetRelativePath(cleanPath, file);

                if (
                    !relativePath.Contains(Path.DirectorySeparatorChar)
                    && !relativePath.Contains(Path.AltDirectorySeparatorChar)
                )
                {
                    _logger.LogDebug("Skipping file already in root: {File}", file);
                    continue;
                }

                var newFileName = relativePath
                    .Replace(Path.DirectorySeparatorChar, '_')
                    .Replace(Path.AltDirectorySeparatorChar, '_')
                    .Replace('/', '_')
                    .Replace('\\', '_');

                newFileName = newFileName.Replace("_", "__");

                var newFilePath = Path.Combine(cleanPath, newFileName);

                if (File.Exists(newFilePath))
                {
                    var counter = 1;
                    var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(newFileName);
                    var extension = Path.GetExtension(newFileName);

                    do
                    {
                        newFileName = $"{fileNameWithoutExtension}__({counter}){extension}";
                        newFilePath = Path.Combine(cleanPath, newFileName);
                        counter++;
                    } while (File.Exists(newFilePath));
                }

                File.Move(file, newFilePath);
                processedFiles++;

                _logger.LogDebug("Moved: {Source} -> {Target}", file, newFilePath);
            }
            catch (Exception ex)
            {
                var error = $"Failed to move {file}: {ex.Message}";
                errors.Add(error);
                _logger.LogError(ex, "Failed to move file: {File}", file);
            }
        }

        try
        {
            RemoveEmptyDirectories(cleanPath);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to remove some empty directories");
        }

        _logger.LogInformation(
            "Conversion completed. Processed {ProcessedFiles} files with {ErrorCount} errors",
            processedFiles,
            errors.Count
        );

        if (errors.Count > 0)
        {
            var errorMessage = string.Join(Environment.NewLine, errors);
            throw new InvalidOperationException($"Some files failed to move:{Environment.NewLine}{errorMessage}");
        }
    }

    private static void RemoveEmptyDirectories(string rootPath)
    {
        var directories = Directory
            .GetDirectories(rootPath, "*", SearchOption.AllDirectories)
            .OrderByDescending(d => d.Length)
            .ToList();

        foreach (var directory in directories)
        {
            try
            {
                if (!Directory.EnumerateFileSystemEntries(directory).Any())
                {
                    Directory.Delete(directory);
                }
            }
            catch { }
        }
    }
}

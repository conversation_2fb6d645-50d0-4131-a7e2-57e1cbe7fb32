using System.Diagnostics;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services;

public interface IResultsService
{
    Task<BenchmarkResults> LoadResultsAsync(string testDataPath);
    Task SaveResultsAsync(string testDataPath, BenchmarkResults results);
    Task AddOrUpdateGroundTruthAsync(string testDataPath, string fileName, List<PageRange> pageRanges);
    Task AddOrUpdateAiPredictionsAsync(
        string testDataPath,
        string fileName,
        List<AiPrediction> predictions,
        string? modelVersion = null
    );
    Task<AnalysisResult> RunClassificationAsync(IProgress<AnalysisProgress>? progress);
}

public class ResultsService : IResultsService
{
    private readonly ILogger<ResultsService> _logger;
    private readonly IConfigurationService _configService;
    private readonly IDocumentProcessingService _documentService;
    private readonly string _model;

    public ResultsService(
        ILogger<ResultsService> logger,
        IConfigurationService configService,
        IDocumentProcessingService documentService,
        IConfiguration config
    )
    {
        _logger = logger;
        _configService = configService;
        _documentService = documentService;
        _model = config.GetValue<string>("Model") ?? throw new Exception("Model is not set");
    }

    private const string ResultsFileName = "benchmark-results.json";
    private readonly JsonSerializerOptions _jsonSerializerOptions = new()
    {
        WriteIndented = true,
        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
    };

    public async Task<BenchmarkResults> LoadResultsAsync(string testDataPath)
    {
        if (string.IsNullOrEmpty(testDataPath) || !Directory.Exists(testDataPath))
            return new BenchmarkResults();

        var resultsPath = Path.Combine(testDataPath, ResultsFileName);

        if (!File.Exists(resultsPath))
            return new BenchmarkResults();

        try
        {
            var json = await File.ReadAllTextAsync(resultsPath);
            return JsonSerializer.Deserialize<BenchmarkResults>(json, _jsonSerializerOptions) ?? new BenchmarkResults();
        }
        catch (Exception)
        {
            return new BenchmarkResults();
        }
    }

    public async Task SaveResultsAsync(string testDataPath, BenchmarkResults results)
    {
        if (string.IsNullOrEmpty(testDataPath) || !Directory.Exists(testDataPath))
            return;

        var resultsPath = Path.Combine(testDataPath, ResultsFileName);
        var json = JsonSerializer.Serialize(results, _jsonSerializerOptions);
        await File.WriteAllTextAsync(resultsPath, json);
    }

    public async Task AddOrUpdateGroundTruthAsync(string testDataPath, string fileName, List<PageRange> pageRanges)
    {
        var results = await LoadResultsAsync(testDataPath);

        var existingResult = results.Results.FirstOrDefault(r => r.FileName == fileName);
        if (existingResult == null)
        {
            existingResult = new FileAnalysisResult(fileName);
            results.Results.Add(existingResult);
        }

        existingResult.GroundTruthDocuments = [.. pageRanges.Select(pr => new GroundTruthDocument(pr))];

        await SaveResultsAsync(testDataPath, results);
    }

    public async Task AddOrUpdateAiPredictionsAsync(
        string testDataPath,
        string fileName,
        List<AiPrediction> predictions,
        string? modelVersion = null
    )
    {
        var results = await LoadResultsAsync(testDataPath);

        var existingResult = results.Results.FirstOrDefault(r => r.FileName == fileName);
        if (existingResult == null)
        {
            existingResult = new FileAnalysisResult(fileName);
            results.Results.Add(existingResult);
        }

        existingResult.AiPredictions = predictions;
        existingResult.ModelVersion = modelVersion;
        existingResult.AnalysisRunTimestamp = DateTime.Now;

        await SaveResultsAsync(testDataPath, results);
    }

    public async Task<AnalysisResult> RunClassificationAsync(IProgress<AnalysisProgress>? progress)
    {
        _logger.LogInformation("Starting AI classification analysis execution");

        var config = await _configService.LoadConfigurationAsync();

        if (string.IsNullOrEmpty(config.ClassificationTestDataPath))
        {
            _logger.LogError("Test data path is not configured");
            throw new InvalidOperationException("Test data path is not configured.");
        }

        var testFiles = await LoadResultsAsync(config.ClassificationTestDataPath);
        var filesWithGroundTruth = testFiles
            .Results.Where(r => r.GroundTruthDocuments.Count > 0)
            .Select(r => r.FileName)
            .ToList();

        if (filesWithGroundTruth.Count == 0)
        {
            _logger.LogError("No test files found in configured path");
            throw new InvalidOperationException("No test files found in configured path.");
        }

        _logger.LogInformation(
            "Processing {FileCount} files with Azure Document Intelligence",
            filesWithGroundTruth.Count
        );

        var stopwatch = Stopwatch.StartNew();
        int processedFiles = 0;
        int failedFiles = 0;

        for (int i = 0; i < filesWithGroundTruth.Count; i++)
        {
            var filePath = Path.Combine(config.ClassificationTestDataPath, filesWithGroundTruth[i]);
            var fileName = filesWithGroundTruth[i];

            progress?.Report(new AnalysisProgress(processedFiles, filesWithGroundTruth.Count, fileName));

            try
            {
                _logger.LogInformation(
                    "Processing file: {FilePath} ({Current}/{Total})",
                    filePath,
                    i + 1,
                    filesWithGroundTruth.Count
                );

                var predictions = await _documentService.ClassifyDocumentAsync(filePath, _model);

                await AddOrUpdateAiPredictionsAsync(config.ClassificationTestDataPath, fileName, predictions, _model);

                processedFiles++;
                _logger.LogInformation(
                    "Successfully processed {FilePath} with {PredictionCount} predictions ({Processed}/{Total})",
                    filePath,
                    predictions.Count,
                    processedFiles,
                    filesWithGroundTruth.Count
                );
            }
            catch (Exception ex)
            {
                failedFiles++;
                _logger.LogError(ex, "Failed to process file: {FilePath}", filePath);
            }
        }

        progress?.Report(new AnalysisProgress(processedFiles, filesWithGroundTruth.Count, "Analysis Complete"));

        stopwatch.Stop();

        var result = new AnalysisResult(
            ProcessedFiles: processedFiles,
            FailedFiles: failedFiles,
            TotalFiles: filesWithGroundTruth.Count,
            ProcessingTime: stopwatch.Elapsed
        );

        _logger.LogInformation(
            "Analysis completed. Processed {ProcessedFiles}/{TotalFiles} files in {ProcessingTime}",
            result.ProcessedFiles,
            result.TotalFiles,
            result.ProcessingTime
        );

        return result;
    }
}

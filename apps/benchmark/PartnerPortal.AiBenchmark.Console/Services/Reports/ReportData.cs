using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services.Reports;

public class AutoSplitComparisonReportData : IReportData
{
    public string ReportType => "Comparison";
    public ReportSummary Summary { get; set; } = null!;
    public List<ComparisonResult> Comparisons { get; set; } = [];
    public List<HighestConfidenceInvoiceAccuracyResult> HighestConfidenceInvoiceFailures { get; set; } = [];
    public List<FirstPredictionInvoiceFailure> FirstPredictionInvoiceFailures { get; set; } = [];
    public int TotalFilesWithMultipleInvoices { get; set; }
    public double HighestConfidenceInvoiceAccuracy { get; set; }
    public double FirstPredictionInvoiceAccuracy { get; set; }
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public string TestDataPath { get; set; } = string.Empty;
    public string? ModelVersion { get; set; }
}

public class NoSplitComparisonReportData : IReportData
{
    public string ReportType => "NoSplitComparison";
    public ReportSummary Summary { get; set; } = null!;
    public List<NoSplitComparisonResult> Comparisons { get; set; } = [];
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public string TestDataPath { get; set; } = string.Empty;
    public string? ModelVersion { get; set; }
}

public class ConfidenceAnalysisReportData : IReportData
{
    public string ReportType => "ConfidenceAnalysis";
    public ConfidenceAnalysisSummary Summary { get; set; } = null!;
    public List<ConfidenceAnalysisResult> Results { get; set; } = [];
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public string TestDataPath { get; set; } = string.Empty;
}

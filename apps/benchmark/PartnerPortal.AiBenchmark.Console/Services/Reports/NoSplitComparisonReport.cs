using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Models;
using Spectre.Console;

namespace PartnerPortal.AiBenchmark.Console.Services.Reports;

public class NoSplitComparisonReport : IReport
{
    private readonly ILogger<NoSplitComparisonReport> _logger;
    private readonly IResultsService _resultsService;
    private readonly IEnumerable<IReportGenerator<NoSplitComparisonReportData>> _generators;
    private readonly IConfigurationService _configService;

    public string ReportType => "NoSplitComparison";
    public string DisplayName => "📋 NoSplit Comparison Report (Console + Excel)";

    public NoSplitComparisonReport(
        ILogger<NoSplitComparisonReport> logger,
        IResultsService resultsService,
        IEnumerable<IReportGenerator<NoSplitComparisonReportData>> generators,
        IConfigurationService configService
    )
    {
        _logger = logger;
        _resultsService = resultsService;
        _generators = generators;
        _configService = configService;
    }

    public async Task<IReportData> GenerateDataAsync()
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            if (string.IsNullOrWhiteSpace(config.ClassificationTestDataPath))
            {
                throw new InvalidOperationException("Test data path is not configured.");
            }

            var results = await _resultsService.LoadResultsAsync(config.ClassificationTestDataPath);
            var comparisons = new List<NoSplitComparisonResult>();

            foreach (var fileResult in results.Results)
            {
                var fileComparison = GetNoSplitComparisonResult(fileResult);
                if (fileComparison != null)
                {
                    comparisons.Add(fileComparison);
                }
            }

            var summary = GenerateSummary(comparisons);

            // Extract model version from the first available result
            var modelVersion = results
                .Results.FirstOrDefault(r => !string.IsNullOrWhiteSpace(r.ModelVersion))
                ?.ModelVersion;

            return new NoSplitComparisonReportData
            {
                Summary = summary,
                Comparisons = comparisons,
                TestDataPath = config.ClassificationTestDataPath,
                ModelVersion = modelVersion,
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating NoSplit comparison report data");
            throw;
        }
    }

    public async Task ExecuteAsync()
    {
        try
        {
            AnsiConsole.MarkupLine("[blue]Generating NoSplit comparison report...[/]");

            var reportData = (NoSplitComparisonReportData)await GenerateDataAsync();

            var consoleGenerator = _generators.FirstOrDefault(g => g.GeneratorType == "Console");
            if (consoleGenerator != null)
            {
                await consoleGenerator.GenerateAsync(reportData);
            }

            var generateExcel = AnsiConsole.Confirm("Do you want to generate an Excel report?");
            if (generateExcel)
            {
                var excelGenerator = _generators.FirstOrDefault(g => g.GeneratorType == "Excel");
                if (excelGenerator != null)
                {
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var outputPath = Path.Combine(
                        reportData.TestDataPath,
                        $"nosplit_comparison_report_{timestamp}.xlsx"
                    );

                    var options = new ReportGenerationOptions { OutputPath = outputPath };
                    await excelGenerator.GenerateAsync(reportData, options);

                    AnsiConsole.MarkupLine($"[green]✅ Excel report saved to:[/] [yellow]{outputPath}[/]");
                }
            }
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Error generating report: {ex.Message}[/]");
            _logger.LogError(ex, "Error executing NoSplit comparison report");
        }
    }

    private static NoSplitComparisonResult? GetNoSplitComparisonResult(FileAnalysisResult fileResult)
    {
        var groundTruth = fileResult.GroundTruthDocuments.FirstOrDefault();
        var prediction = fileResult.AiPredictions.FirstOrDefault();

        if (groundTruth == null && prediction == null)
        {
            return null;
        }

        if (groundTruth == null)
        {
            var predictedType = Enum.TryParse<DocumentType>(prediction!.DocType, out var pt) ? pt : DocumentType.Other;

            return new NoSplitComparisonResult(
                fileResult.FileName,
                DocumentType.Other,
                predictedType,
                null,
                prediction.Confidence,
                false,
                "Extra Prediction"
            );
        }

        var groundTruthType = Enum.TryParse<DocumentType>(groundTruth.DocType, out var gt) ? gt : DocumentType.Other;

        if (prediction == null)
        {
            return new NoSplitComparisonResult(
                fileResult.FileName,
                groundTruthType,
                null,
                null,
                null,
                false,
                "Missing Prediction"
            );
        }

        var predictedDocType = Enum.TryParse<DocumentType>(prediction.DocType, out var pdt) ? pdt : DocumentType.Other;

        var isCorrect = groundTruthType == predictedDocType;
        var status = isCorrect ? "Correct" : "Incorrect";

        return new NoSplitComparisonResult(
            fileResult.FileName,
            groundTruthType,
            predictedDocType,
            prediction.ActualDocTypeFromResponse,
            prediction.Confidence,
            isCorrect,
            status
        );
    }

    private static ReportSummary GenerateSummary(List<NoSplitComparisonResult> comparisons)
    {
        var totalComparisons = comparisons.Count;
        var correctClassifications = comparisons.Count(c => c.Status == "Correct");
        var incorrectClassifications = comparisons.Count(c => c.Status == "Incorrect");
        var missingPredictions = comparisons.Count(c => c.Status == "Missing Prediction");
        var extraPredictions = comparisons.Count(c => c.Status == "Extra Prediction");

        var accuracyPercentage = totalComparisons > 0 ? (double)correctClassifications / totalComparisons * 100 : 0;

        var documentTypeStats = new Dictionary<DocumentType, DocumentTypeStats>();

        foreach (DocumentType docType in Enum.GetValues<DocumentType>())
        {
            var groundTruthsForType = comparisons
                .Where(c => c.GroundTruthType == docType && c.Status != "Extra Prediction")
                .ToList();

            if (groundTruthsForType.Count == 0)
                continue;

            var correctForType = groundTruthsForType.Count(c => c.Status == "Correct");
            var incorrectForType = groundTruthsForType.Count(c => c.Status == "Incorrect");
            var missingForType = groundTruthsForType.Count(c => c.Status == "Missing Prediction");

            var typeAccuracy =
                groundTruthsForType.Count > 0 ? (double)correctForType / groundTruthsForType.Count * 100 : 0;

            documentTypeStats[docType] = new DocumentTypeStats(
                groundTruthsForType.Count,
                correctForType,
                incorrectForType,
                missingForType,
                typeAccuracy
            );
        }

        return new ReportSummary(
            totalComparisons,
            correctClassifications,
            incorrectClassifications,
            missingPredictions,
            extraPredictions,
            accuracyPercentage,
            documentTypeStats
        );
    }
}

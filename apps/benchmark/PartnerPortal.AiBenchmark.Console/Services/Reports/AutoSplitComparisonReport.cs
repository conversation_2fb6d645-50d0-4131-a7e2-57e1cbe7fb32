using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Helpers;
using PartnerPortal.AiBenchmark.Console.Models;
using Spectre.Console;

namespace PartnerPortal.AiBenchmark.Console.Services.Reports;

public class AutoSplitComparisonReport : IReport
{
    private readonly ILogger<AutoSplitComparisonReport> _logger;
    private readonly IResultsService _resultsService;
    private readonly IEnumerable<IReportGenerator<AutoSplitComparisonReportData>> _generators;
    private readonly IConfigurationService _configService;

    public string ReportType => "Comparison";
    public string DisplayName => "📊 AutoSplit Comparison Report (Console + Excel)";

    public AutoSplitComparisonReport(
        ILogger<AutoSplitComparisonReport> logger,
        IResultsService resultsService,
        IEnumerable<IReportGenerator<AutoSplitComparisonReportData>> generators,
        IConfigurationService configService
    )
    {
        _logger = logger;
        _resultsService = resultsService;
        _generators = generators;
        _configService = configService;
    }

    public async Task<IReportData> GenerateDataAsync()
    {
        try
        {
            var config = await _configService.LoadConfigurationAsync();
            if (string.IsNullOrWhiteSpace(config.ClassificationTestDataPath))
            {
                throw new InvalidOperationException("Test data path is not configured.");
            }

            var results = await _resultsService.LoadResultsAsync(config.ClassificationTestDataPath);
            var comparisons = new List<ComparisonResult>();

            foreach (
                // Only include files with ground truth and predictions
                var fileResult in results.Results.Where(r =>
                    r.GroundTruthDocuments.Count > 0 && r.AiPredictions.Count > 0
                )
            )
            {
                var fileComparisons = GetComparisonResults(fileResult);
                comparisons.AddRange(fileComparisons);
            }

            var summary = GenerateSummary(comparisons);
            var (highestConfFailures, totalFiles, highestConfAccuracy) =
                GenerateHighestConfidenceInvoiceAccuracyAnalysis(results.Results);
            var (firstPredFailures, firstPredAccuracy) = GenerateFirstPredictionInvoiceFailures(results.Results);

            // Extract model version from the first available result
            var modelVersion = results
                .Results.FirstOrDefault(r => !string.IsNullOrWhiteSpace(r.ModelVersion))
                ?.ModelVersion;

            return new AutoSplitComparisonReportData
            {
                Summary = summary,
                Comparisons = comparisons,
                HighestConfidenceInvoiceFailures = highestConfFailures,
                FirstPredictionInvoiceFailures = firstPredFailures,
                TotalFilesWithMultipleInvoices = totalFiles,
                HighestConfidenceInvoiceAccuracy = highestConfAccuracy,
                FirstPredictionInvoiceAccuracy = firstPredAccuracy,
                TestDataPath = config.ClassificationTestDataPath,
                ModelVersion = modelVersion,
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating comparison report data");
            throw;
        }
    }

    public async Task ExecuteAsync()
    {
        try
        {
            AnsiConsole.MarkupLine("[blue]Generating comparison report...[/]");

            var reportData = (AutoSplitComparisonReportData)await GenerateDataAsync();

            // Always generate console report
            var consoleGenerator = _generators.FirstOrDefault(g => g.GeneratorType == "Console");
            if (consoleGenerator != null)
            {
                await consoleGenerator.GenerateAsync(reportData);
            }

            // Ask if user wants Excel report
            var generateExcel = AnsiConsole.Confirm("Do you want to generate an Excel report?");
            if (generateExcel)
            {
                var excelGenerator = _generators.FirstOrDefault(g => g.GeneratorType == "Excel");
                if (excelGenerator != null)
                {
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var outputPath = Path.Combine(
                        reportData.TestDataPath,
                        $"auto_split_comparison_report_{timestamp}.xlsx"
                    );

                    var options = new ReportGenerationOptions { OutputPath = outputPath };
                    await excelGenerator.GenerateAsync(reportData, options);

                    AnsiConsole.MarkupLine($"[green]✅ Excel report saved to:[/] [yellow]{outputPath}[/]");
                }
            }
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Error generating report: {ex.Message}[/]");
            _logger.LogError(ex, "Error executing comparison report");
        }
    }

    private static List<ComparisonResult> GetComparisonResults(FileAnalysisResult fileResult)
    {
        var comparisons = new List<ComparisonResult>();

        var groundTruths = fileResult.GroundTruthDocuments.ToList();
        var predictions = fileResult.AiPredictions.ToList();
        var matchedPredictions = new HashSet<int>();

        foreach (var groundTruth in groundTruths)
        {
            var groundTruthType = Enum.Parse<DocumentType>(groundTruth.DocType);

            // TODO: Remove this once we care about delivery note predictions
            if (groundTruthType == DocumentType.DeliveryNote)
            {
                groundTruthType = DocumentType.Other;
            }

            var matchingPredictionIndex = -1;
            for (int i = 0; i < predictions.Count; i++)
            {
                if (matchedPredictions.Contains(i))
                    continue;

                var prediction = predictions[i];
                if (ArrayHelper.ArraysEqual(groundTruth.PageRange, prediction.PageRange))
                {
                    matchingPredictionIndex = i;
                    break;
                }
            }

            if (matchingPredictionIndex >= 0)
            {
                var prediction = predictions[matchingPredictionIndex];
                var predictedType = Enum.Parse<DocumentType>(prediction.DocType);

                var isCorrect = groundTruthType == predictedType;
                var status = isCorrect ? "Correct" : "Incorrect";

                comparisons.Add(
                    new ComparisonResult(
                        fileResult.FileName,
                        groundTruthType,
                        groundTruth.PageRange,
                        predictedType,
                        prediction.PageRange,
                        prediction.Confidence,
                        isCorrect,
                        status
                    )
                );

                matchedPredictions.Add(matchingPredictionIndex);
            }
            else
            {
                comparisons.Add(
                    new ComparisonResult(
                        fileResult.FileName,
                        groundTruthType,
                        groundTruth.PageRange,
                        null,
                        null,
                        null,
                        false,
                        "Missing Prediction"
                    )
                );
            }
        }

        for (int i = 0; i < predictions.Count; i++)
        {
            if (!matchedPredictions.Contains(i))
            {
                var prediction = predictions[i];
                var predictedType = Enum.Parse<DocumentType>(prediction.DocType);

                comparisons.Add(
                    new ComparisonResult(
                        fileResult.FileName,
                        DocumentType.None,
                        [],
                        predictedType,
                        prediction.PageRange,
                        prediction.Confidence,
                        false,
                        "Extra Prediction"
                    )
                );
            }
        }

        return comparisons;
    }

    private static ReportSummary GenerateSummary(List<ComparisonResult> comparisons)
    {
        var totalComparisons = comparisons.Count;
        var correctClassifications = comparisons.Count(c => c.Status == "Correct");
        var incorrectClassifications = comparisons.Count(c => c.Status == "Incorrect");
        var missingPredictions = comparisons.Count(c => c.Status == "Missing Prediction");
        var extraPredictions = comparisons.Count(c => c.Status == "Extra Prediction");

        var accuracyPercentage = totalComparisons > 0 ? (double)correctClassifications / totalComparisons * 100 : 0;

        var documentTypeStats = new Dictionary<DocumentType, DocumentTypeStats>();
        var documentTypes = Enum.GetValues<DocumentType>();

        foreach (var docType in documentTypes)
        {
            var groundTruthCount = comparisons.Count(c =>
                c.GroundTruthType == docType && c.Status != "Extra Prediction"
            );
            var correctPredictions = comparisons.Count(c => c.GroundTruthType == docType && c.Status == "Correct");
            var incorrectPredictions = comparisons.Count(c => c.GroundTruthType == docType && c.Status == "Incorrect");
            var missingForType = comparisons.Count(c =>
                c.GroundTruthType == docType && c.Status == "Missing Prediction"
            );

            if (groundTruthCount > 0)
            {
                var typeAccuracy = (double)correctPredictions / groundTruthCount * 100;
                documentTypeStats[docType] = new DocumentTypeStats(
                    groundTruthCount,
                    correctPredictions,
                    incorrectPredictions,
                    missingForType,
                    typeAccuracy
                );
            }
        }

        return new ReportSummary(
            totalComparisons,
            correctClassifications,
            incorrectClassifications,
            missingPredictions,
            extraPredictions,
            accuracyPercentage,
            documentTypeStats
        );
    }

    private static (
        List<HighestConfidenceInvoiceAccuracyResult> failures,
        int totalFiles,
        double accuracy
    ) GenerateHighestConfidenceInvoiceAccuracyAnalysis(List<FileAnalysisResult> fileResults)
    {
        var failures = new List<HighestConfidenceInvoiceAccuracyResult>();
        int totalFilesWithMultipleInvoices = 0;
        int correctPredictions = 0;

        foreach (var fileResult in fileResults)
        {
            var invoicePredictions = fileResult
                .AiPredictions.Where(p => Enum.Parse<DocumentType>(p.DocType) == DocumentType.Invoice)
                .OrderByDescending(p => p.Confidence)
                .ToList();

            var groundTruthInvoices = fileResult
                .GroundTruthDocuments.Where(gt => Enum.Parse<DocumentType>(gt.DocType) == DocumentType.Invoice)
                .ToList();

            if (invoicePredictions.Count >= 2 && groundTruthInvoices.Count > 0)
            {
                totalFilesWithMultipleInvoices++;
                var highestConfidencePrediction = invoicePredictions.First();

                var matchingGroundTruth = groundTruthInvoices.FirstOrDefault(gt =>
                    ArrayHelper.PageRangesIntersect(gt.PageRange, highestConfidencePrediction.PageRange)
                );

                if (matchingGroundTruth != null)
                {
                    correctPredictions++;
                }
                else
                {
                    var groundTruthInvoice = groundTruthInvoices.First();

                    failures.Add(
                        new HighestConfidenceInvoiceAccuracyResult(
                            fileResult.FileName,
                            invoicePredictions.Count,
                            highestConfidencePrediction.PageRange,
                            highestConfidencePrediction.Confidence,
                            groundTruthInvoice.PageRange
                        )
                    );
                }
            }
        }

        double accuracy =
            totalFilesWithMultipleInvoices > 0 ? (double)correctPredictions / totalFilesWithMultipleInvoices * 100 : 0;

        return (failures, totalFilesWithMultipleInvoices, accuracy);
    }

    private static (
        List<FirstPredictionInvoiceFailure> failures,
        double accuracy
    ) GenerateFirstPredictionInvoiceFailures(List<FileAnalysisResult> fileResults)
    {
        var failures = new List<FirstPredictionInvoiceFailure>();
        int totalFilesWithMultipleInvoices = 0;
        int correctPredictions = 0;

        foreach (var fileResult in fileResults)
        {
            var invoicePredictions = fileResult
                .AiPredictions.Where(p => Enum.Parse<DocumentType>(p.DocType) == DocumentType.Invoice)
                .ToList();

            var groundTruthInvoices = fileResult
                .GroundTruthDocuments.Where(gt => Enum.Parse<DocumentType>(gt.DocType) == DocumentType.Invoice)
                .ToList();

            if (invoicePredictions.Count >= 2 && groundTruthInvoices.Count > 0)
            {
                totalFilesWithMultipleInvoices++;
                var firstPrediction = invoicePredictions.First();

                var matchingGroundTruth = groundTruthInvoices.FirstOrDefault(gt =>
                    ArrayHelper.PageRangesIntersect(gt.PageRange, firstPrediction.PageRange)
                );

                if (matchingGroundTruth != null)
                {
                    correctPredictions++;
                }
                else
                {
                    var groundTruthInvoice = groundTruthInvoices.First();

                    failures.Add(
                        new FirstPredictionInvoiceFailure(
                            fileResult.FileName,
                            invoicePredictions.Count,
                            firstPrediction.PageRange,
                            firstPrediction.Confidence,
                            groundTruthInvoice.PageRange
                        )
                    );
                }
            }
        }

        double accuracy =
            totalFilesWithMultipleInvoices > 0 ? (double)correctPredictions / totalFilesWithMultipleInvoices * 100 : 0;

        return (failures, accuracy);
    }
}

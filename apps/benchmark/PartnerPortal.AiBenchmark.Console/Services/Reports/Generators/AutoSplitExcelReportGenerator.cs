using System.Linq;
using ClosedXML.Excel;
using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services.Reports.Generators;

public class AutoSplitExcelReportGenerator : IReportGenerator<AutoSplitComparisonReportData>
{
    private readonly ILogger<AutoSplitExcelReportGenerator> _logger;

    public string GeneratorType => "Excel";

    public AutoSplitExcelReportGenerator(ILogger<AutoSplitExcelReportGenerator> logger)
    {
        _logger = logger;
    }

    public Task GenerateAsync(AutoSplitComparisonReportData data, ReportGenerationOptions? options = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(options?.OutputPath))
            {
                throw new ArgumentException("Output path is required for Excel report generation.");
            }

            using var workbook = new XLWorkbook();

            var summarySheet = workbook.Worksheets.Add("Summary");
            CreateSummarySheet(summarySheet, data);

            var detailsSheet = workbook.Worksheets.Add("Detailed Results");
            CreateDetailsSheet(detailsSheet, data.Comparisons);

            var docTypeSheet = workbook.Worksheets.Add("Document Type Analysis");
            CreateDocumentTypeSheet(docTypeSheet, data.Summary.DocumentTypeStats);

            var perFileSheet = workbook.Worksheets.Add("Per File Summary");
            CreatePerFileSummarySheet(perFileSheet, data.Comparisons);

            var highestConfidenceSheet = workbook.Worksheets.Add("Invoice Confidence Accuracy");
            CreateHighestConfidenceInvoiceAccuracySheet(highestConfidenceSheet, data);

            var firstPredictionSheet = workbook.Worksheets.Add("First Prediction Failures");
            CreateFirstPredictionFailuresSheet(firstPredictionSheet, data);

            workbook.SaveAs(options.OutputPath);
            _logger.LogInformation("Excel report saved to: {OutputPath}", options.OutputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Excel report");
            throw;
        }

        return Task.CompletedTask;
    }

    private static void CreateSummarySheet(IXLWorksheet sheet, AutoSplitComparisonReportData data)
    {
        sheet.Cell("A1").Value = "Classification Report Summary";
        sheet.Cell("A1").Style.Font.Bold = true;
        sheet.Cell("A1").Style.Font.FontSize = 16;

        sheet.Cell("A2").Value = "Model Version:";
        sheet.Cell("B2").Value = data.ModelVersion ?? "Unknown";

        sheet.Cell("A4").Value = "Overall Statistics";
        sheet.Cell("A4").Style.Font.Bold = true;

        sheet.Cell("A5").Value = "Total Comparisons:";
        sheet.Cell("B5").Value = data.Summary.TotalComparisons;

        sheet.Cell("A6").Value = "Correct Classifications:";
        sheet.Cell("B6").Value = data.Summary.CorrectClassifications;

        sheet.Cell("A7").Value = "Incorrect Classifications:";
        sheet.Cell("B7").Value = data.Summary.IncorrectClassifications;

        sheet.Cell("A8").Value = "Missing Predictions:";
        sheet.Cell("B8").Value = data.Summary.MissingPredictions;

        sheet.Cell("A9").Value = "Extra Predictions:";
        sheet.Cell("B9").Value = data.Summary.ExtraPredictions;

        sheet.Cell("A10").Value = "Overall Accuracy:";
        sheet.Cell("B10").Value = data.Summary.AccuracyPercentage / 100.0;
        sheet.Cell("B10").Style.NumberFormat.Format = "0.00%";
        sheet.Cell("B10").Style.Font.Bold = true;

        sheet.Columns().AdjustToContents();
    }

    private static void CreateDetailsSheet(IXLWorksheet sheet, List<ComparisonResult> comparisons)
    {
        // Headers
        sheet.Cell("A1").Value = "File Name";
        sheet.Cell("B1").Value = "Ground Truth Type";
        sheet.Cell("C1").Value = "Ground Truth Pages";
        sheet.Cell("D1").Value = "Predicted Type";
        sheet.Cell("E1").Value = "Predicted Pages";
        sheet.Cell("F1").Value = "Confidence";
        sheet.Cell("G1").Value = "Status";

        // Style headers
        var headerRange = sheet.Range("A1:G1");
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

        // Data
        for (int i = 0; i < comparisons.Count; i++)
        {
            var comparison = comparisons[i];
            var row = i + 2;

            sheet.Cell(row, 1).Value = comparison.FileName;
            sheet.Cell(row, 2).Value = comparison.GroundTruthType.ToString();
            sheet.Cell(row, 3).Value = FormatPageRange(comparison.GroundTruthPageRange);
            sheet.Cell(row, 4).Value = comparison.PredictedType?.ToString() ?? "N/A";
            sheet.Cell(row, 5).Value = FormatPageRange(comparison.PredictedPageRange);
            if (comparison.Confidence.HasValue)
            {
                sheet.Cell(row, 6).Value = comparison.Confidence.Value;
                sheet.Cell(row, 6).Style.NumberFormat.Format = "0.00";
            }
            else
            {
                sheet.Cell(row, 6).Value = 0.0;
            }
            sheet.Cell(row, 7).Value = comparison.Status;

            // Color code status
            var statusCell = sheet.Cell(row, 7);
            switch (comparison.Status)
            {
                case "Correct":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightGreen;
                    break;
                case "Incorrect":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightCoral;
                    break;
                case "Missing Prediction":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightYellow;
                    break;
                case "Extra Prediction":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightGray;
                    break;
            }
        }

        // Enable AutoFilter on header row for all populated rows
        var lastDataRow = comparisons.Count + 1;
        sheet.Range($"A1:G{lastDataRow}").SetAutoFilter();

        sheet.Columns().AdjustToContents();
    }

    private static void CreateDocumentTypeSheet(IXLWorksheet sheet, Dictionary<DocumentType, DocumentTypeStats> stats)
    {
        // Headers
        sheet.Cell("A1").Value = "Document Type";
        sheet.Cell("B1").Value = "Total Ground Truth";
        sheet.Cell("C1").Value = "Correct Predictions";
        sheet.Cell("D1").Value = "Incorrect Predictions";
        sheet.Cell("E1").Value = "Missing Predictions";
        sheet.Cell("F1").Value = "Accuracy %";

        // Style headers
        var headerRange = sheet.Range("A1:F1");
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

        // Data
        int row = 2;
        foreach (var kvp in stats)
        {
            sheet.Cell(row, 1).Value = kvp.Key.ToString();
            sheet.Cell(row, 2).Value = kvp.Value.TotalGroundTruth;
            sheet.Cell(row, 3).Value = kvp.Value.CorrectPredictions;
            sheet.Cell(row, 4).Value = kvp.Value.IncorrectPredictions;
            sheet.Cell(row, 5).Value = kvp.Value.MissingPredictions;
            sheet.Cell(row, 6).Value = $"{kvp.Value.AccuracyPercentage:F2}%";
            row++;
        }

        sheet.Columns().AdjustToContents();
    }

    private static void CreatePerFileSummarySheet(IXLWorksheet sheet, List<ComparisonResult> comparisons)
    {
        // Headers
        sheet.Cell("A1").Value = "File Name";
        sheet.Cell("B1").Value = "Correct";
        sheet.Cell("C1").Value = "Incorrect";
        sheet.Cell("D1").Value = "Missing";
        sheet.Cell("E1").Value = "Extra";
        sheet.Cell("F1").Value = "File Status"; // Correct if all ranges correct, otherwise Incorrect

        var headerRange = sheet.Range("A1:F1");
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

        var groups = comparisons.GroupBy(c => c.FileName).OrderBy(g => g.Key);

        int row = 2;
        foreach (var group in groups)
        {
            var correct = group.Count(c => c.Status == "Correct");
            var incorrect = group.Count(c => c.Status == "Incorrect");
            var missing = group.Count(c => c.Status == "Missing Prediction");
            var extra = group.Count(c => c.Status == "Extra Prediction");

            var fileIsCorrect = group.All(c => c.Status == "Correct");
            var fileStatus = fileIsCorrect ? "Correct" : "Incorrect";

            sheet.Cell(row, 1).Value = group.Key;
            sheet.Cell(row, 2).Value = correct;
            sheet.Cell(row, 3).Value = incorrect;
            sheet.Cell(row, 4).Value = missing;
            sheet.Cell(row, 5).Value = extra;
            sheet.Cell(row, 6).Value = fileStatus;

            var statusCell = sheet.Cell(row, 6);
            statusCell.Style.Fill.BackgroundColor = fileIsCorrect ? XLColor.LightGreen : XLColor.LightCoral;

            row++;
        }

        var lastDataRow = Math.Max(2, comparisons.Select(c => c.FileName).Distinct().Count() + 1);
        sheet.Range($"A1:F{lastDataRow}").SetAutoFilter();

        sheet.Columns().AdjustToContents();
    }

    private static void CreateHighestConfidenceInvoiceAccuracySheet(
        IXLWorksheet sheet,
        AutoSplitComparisonReportData data
    )
    {
        // Summary section first
        sheet.Cell("A1").Value = "Highest Confidence Invoice Accuracy Analysis";
        sheet.Cell("A1").Style.Font.Bold = true;
        sheet.Cell("A1").Style.Font.FontSize = 16;

        sheet.Cell("A3").Value = "Overall Results";
        sheet.Cell("A3").Style.Font.Bold = true;
        sheet.Cell("A3").Style.Font.FontSize = 14;

        sheet.Cell("A5").Value = "Total files with multiple invoice predictions:";
        sheet.Cell("B5").Value = data.TotalFilesWithMultipleInvoices;

        sheet.Cell("A6").Value = "Files where highest confidence is CORRECT:";
        sheet.Cell("B6").Value = data.TotalFilesWithMultipleInvoices - data.HighestConfidenceInvoiceFailures.Count;

        sheet.Cell("A7").Value = "Files where highest confidence FAILED:";
        sheet.Cell("B7").Value = data.HighestConfidenceInvoiceFailures.Count;

        sheet.Cell("A8").Value = "Highest Confidence Invoice Accuracy:";
        sheet.Cell("B8").Value = data.HighestConfidenceInvoiceAccuracy / 100.0;
        sheet.Cell("B8").Style.NumberFormat.Format = "0.00%";
        sheet.Cell("B8").Style.Font.Bold = true;
        sheet.Cell("B8").Style.Font.FontSize = 14;

        if (data.HighestConfidenceInvoiceFailures.Count > 0)
        {
            var failuresStartRow = 11;
            sheet.Cell(failuresStartRow, "A").Value = "Files Where Highest Confidence Invoice is Incorrect";
            sheet.Cell(failuresStartRow, "A").Style.Font.Bold = true;
            sheet.Cell(failuresStartRow, "A").Style.Font.FontSize = 14;

            var headerRow = failuresStartRow + 2;
            sheet.Cell(headerRow, "A").Value = "File Name";
            sheet.Cell(headerRow, "B").Value = "Total Invoice Predictions";
            sheet.Cell(headerRow, "C").Value = "Highest Confidence Pages";
            sheet.Cell(headerRow, "D").Value = "Highest Confidence";
            sheet.Cell(headerRow, "E").Value = "Ground Truth Pages";

            var headerRange = sheet.Range($"A{headerRow}:E{headerRow}");
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

            for (int i = 0; i < data.HighestConfidenceInvoiceFailures.Count; i++)
            {
                var failure = data.HighestConfidenceInvoiceFailures[i];
                var row = headerRow + 1 + i;

                sheet.Cell(row, 1).Value = failure.FileName;
                sheet.Cell(row, 2).Value = failure.TotalInvoicePredictions;
                sheet.Cell(row, 3).Value = FormatPageRange(failure.HighestConfidenceInvoicePageRange);
                sheet.Cell(row, 4).Value = failure.HighestConfidenceInvoiceConfidence;
                sheet.Cell(row, 4).Style.NumberFormat.Format = "0.00";
                sheet.Cell(row, 5).Value = FormatPageRange(failure.GroundTruthInvoicePageRange);

                var rowRange = sheet.Range($"A{row}:E{row}");
                rowRange.Style.Fill.BackgroundColor = XLColor.LightCoral;
            }

            var lastDataRow = headerRow + data.HighestConfidenceInvoiceFailures.Count;
            sheet.Range($"A{headerRow}:E{lastDataRow}").SetAutoFilter();
        }
        else
        {
            sheet.Cell("A11").Value =
                "🎉 All files with multiple invoice predictions have correct highest confidence matches!";
            sheet.Cell("A11").Style.Font.Bold = true;
            sheet.Cell("A11").Style.Font.FontColor = XLColor.DarkGreen;
            sheet.Cell("A11").Style.Font.FontSize = 12;
        }

        sheet.Columns().AdjustToContents();
    }

    private static void CreateFirstPredictionFailuresSheet(IXLWorksheet sheet, AutoSplitComparisonReportData data)
    {
        sheet.Cell("A1").Value = "First Prediction Strategy Failures";
        sheet.Cell("A1").Style.Font.Bold = true;
        sheet.Cell("A1").Style.Font.FontSize = 16;

        sheet.Cell("A3").Value = "Summary";
        sheet.Cell("A3").Style.Font.Bold = true;
        sheet.Cell("A3").Style.Font.FontSize = 14;

        sheet.Cell("A5").Value = "Total files with multiple invoice predictions:";
        sheet.Cell("B5").Value = data.TotalFilesWithMultipleInvoices;

        sheet.Cell("A6").Value = "First Prediction Strategy Accuracy:";
        sheet.Cell("B6").Value = data.FirstPredictionInvoiceAccuracy / 100.0;
        sheet.Cell("B6").Style.NumberFormat.Format = "0.00%";
        sheet.Cell("B6").Style.Font.Bold = true;

        sheet.Cell("A7").Value = "Files where first prediction FAILED:";
        sheet.Cell("B7").Value = data.FirstPredictionInvoiceFailures.Count;

        if (data.FirstPredictionInvoiceFailures.Count > 0)
        {
            var failuresStartRow = 10;
            sheet.Cell(failuresStartRow, "A").Value = "Files Where Taking First Invoice Prediction is Wrong";
            sheet.Cell(failuresStartRow, "A").Style.Font.Bold = true;
            sheet.Cell(failuresStartRow, "A").Style.Font.FontSize = 14;

            // Headers
            var headerRow = failuresStartRow + 2;
            sheet.Cell(headerRow, "A").Value = "File Name";
            sheet.Cell(headerRow, "B").Value = "Total Invoice Predictions";
            sheet.Cell(headerRow, "C").Value = "First Prediction Pages";
            sheet.Cell(headerRow, "D").Value = "First Prediction Confidence";
            sheet.Cell(headerRow, "E").Value = "Ground Truth Pages";

            // Style headers
            var headerRange = sheet.Range($"A{headerRow}:E{headerRow}");
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

            // Data rows
            for (int i = 0; i < data.FirstPredictionInvoiceFailures.Count; i++)
            {
                var failure = data.FirstPredictionInvoiceFailures[i];
                var row = headerRow + 1 + i;

                sheet.Cell(row, 1).Value = failure.FileName;
                sheet.Cell(row, 2).Value = failure.TotalInvoicePredictions;
                sheet.Cell(row, 3).Value = FormatPageRange(failure.FirstPredictionInvoicePageRange);
                sheet.Cell(row, 4).Value = failure.FirstPredictionInvoiceConfidence;
                sheet.Cell(row, 4).Style.NumberFormat.Format = "0.00";
                sheet.Cell(row, 5).Value = FormatPageRange(failure.GroundTruthInvoicePageRange);

                // Highlight incorrect matches
                var rowRange = sheet.Range($"A{row}:E{row}");
                rowRange.Style.Fill.BackgroundColor = XLColor.LightCoral;
            }

            // Enable AutoFilter for failures table
            var lastDataRow = headerRow + data.FirstPredictionInvoiceFailures.Count;
            sheet.Range($"A{headerRow}:E{lastDataRow}").SetAutoFilter();
        }
        else
        {
            sheet.Cell("A10").Value = "🎉 All files: First prediction strategy works perfectly!";
            sheet.Cell("A10").Style.Font.Bold = true;
            sheet.Cell("A10").Style.Font.FontColor = XLColor.DarkGreen;
            sheet.Cell("A10").Style.Font.FontSize = 12;
        }

        sheet.Columns().AdjustToContents();
    }

    private static string FormatPageRange(int[]? pageRange)
    {
        if (pageRange == null || pageRange.Length == 0)
            return "N/A";
        if (pageRange.Length == 2)
        {
            return $"{pageRange[0]}-{pageRange[1]}";
        }
        return string.Join(",", pageRange);
    }
}

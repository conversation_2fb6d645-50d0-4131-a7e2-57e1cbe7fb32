using ClosedXML.Excel;
using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services.Reports.Generators;

public class NoSplitExcelReportGenerator : IReportGenerator<NoSplitComparisonReportData>
{
    private readonly ILogger<NoSplitExcelReportGenerator> _logger;

    public string GeneratorType => "Excel";

    public NoSplitExcelReportGenerator(ILogger<NoSplitExcelReportGenerator> logger)
    {
        _logger = logger;
    }

    public Task GenerateAsync(NoSplitComparisonReportData data, ReportGenerationOptions? options = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(options?.OutputPath))
            {
                throw new ArgumentException("Output path is required for Excel report generation.");
            }

            using var workbook = new XLWorkbook();

            var summarySheet = workbook.Worksheets.Add("Summary");
            CreateSummarySheet(summarySheet, data);

            var detailsSheet = workbook.Worksheets.Add("Detailed Results");
            CreateDetailsSheet(detailsSheet, data.Comparisons);

            var docTypeSheet = workbook.Worksheets.Add("Document Type Analysis");
            CreateDocumentTypeSheet(docTypeSheet, data.Summary.DocumentTypeStats);

            workbook.SaveAs(options.OutputPath);
            _logger.LogInformation("NoSplit Excel report saved to: {OutputPath}", options.OutputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating NoSplit Excel report");
            throw;
        }

        return Task.CompletedTask;
    }

    private static void CreateSummarySheet(IXLWorksheet sheet, NoSplitComparisonReportData data)
    {
        sheet.Cell("A1").Value = "NoSplit Classification Report Summary";
        sheet.Cell("A1").Style.Font.Bold = true;
        sheet.Cell("A1").Style.Font.FontSize = 16;

        sheet.Cell("A3").Value = "Generated At:";
        sheet.Cell("B3").Value = data.GeneratedAt;
        sheet.Cell("B3").Style.DateFormat.Format = "yyyy-mm-dd hh:mm:ss";

        sheet.Cell("A4").Value = "Test Data Path:";
        sheet.Cell("B4").Value = data.TestDataPath;

        sheet.Cell("A5").Value = "Model Version:";
        sheet.Cell("B5").Value = data.ModelVersion ?? "Unknown";

        sheet.Cell("A7").Value = "Overall Statistics";
        sheet.Cell("A7").Style.Font.Bold = true;

        sheet.Cell("A9").Value = "Total Documents:";
        sheet.Cell("B9").Value = data.Summary.TotalComparisons;

        sheet.Cell("A10").Value = "Correct Classifications:";
        sheet.Cell("B10").Value = data.Summary.CorrectClassifications;

        sheet.Cell("A11").Value = "Incorrect Classifications:";
        sheet.Cell("B11").Value = data.Summary.IncorrectClassifications;

        sheet.Cell("A12").Value = "Missing Predictions:";
        sheet.Cell("B12").Value = data.Summary.MissingPredictions;

        sheet.Cell("A13").Value = "Extra Predictions:";
        sheet.Cell("B13").Value = data.Summary.ExtraPredictions;

        sheet.Cell("A15").Value = "Overall Accuracy (correctClassifications / totalComparisons):";
        sheet.Cell("B15").Value = data.Summary.AccuracyPercentage / 100.0;
        sheet.Cell("B15").Style.NumberFormat.Format = "0.00%";
        sheet.Cell("B15").Style.Font.Bold = true;

        sheet.Columns().AdjustToContents();
    }

    private static void CreateDetailsSheet(IXLWorksheet sheet, List<NoSplitComparisonResult> comparisons)
    {
        // Headers
        sheet.Cell("A1").Value = "File Name";
        sheet.Cell("B1").Value = "Ground Truth Type";
        sheet.Cell("C1").Value = "AI Prediction Type";
        sheet.Cell("D1").Value = "Confidence";
        sheet.Cell("E1").Value = "Status";
        sheet.Cell("F1").Value = "Actual Doc Type From Response";

        // Style headers
        var headerRange = sheet.Range("A1:F1");
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

        // Data
        for (int i = 0; i < comparisons.Count; i++)
        {
            var comparison = comparisons[i];
            var row = i + 2;

            sheet.Cell(row, 1).Value = comparison.FileName;
            sheet.Cell(row, 2).Value = comparison.GroundTruthType.ToString();
            sheet.Cell(row, 3).Value = comparison.PredictedType?.ToString() ?? "N/A";

            if (comparison.Confidence.HasValue)
            {
                sheet.Cell(row, 4).Value = comparison.Confidence.Value;
                sheet.Cell(row, 4).Style.NumberFormat.Format = "0.00";
            }
            else
            {
                sheet.Cell(row, 4).Value = "N/A";
            }

            sheet.Cell(row, 5).Value = comparison.Status;

            sheet.Cell(row, 6).Value = comparison.ActualDocTypeFromResponse;

            // Color code status
            var statusCell = sheet.Cell(row, 5);
            switch (comparison.Status)
            {
                case "Correct":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightGreen;
                    break;
                case "Incorrect":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightCoral;
                    break;
                case "Missing Prediction":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightYellow;
                    break;
                case "Extra Prediction":
                    statusCell.Style.Fill.BackgroundColor = XLColor.LightGray;
                    break;
            }
        }

        sheet.Columns().AdjustToContents();
    }

    private static void CreateDocumentTypeSheet(IXLWorksheet sheet, Dictionary<DocumentType, DocumentTypeStats> stats)
    {
        // Headers
        sheet.Cell("A1").Value = "Document Type";
        sheet.Cell("B1").Value = "Total Ground Truth";
        sheet.Cell("C1").Value = "Correct Predictions";
        sheet.Cell("D1").Value = "Incorrect Predictions";
        sheet.Cell("E1").Value = "Missing Predictions";
        sheet.Cell("F1").Value = "Accuracy %";

        // Style headers
        var headerRange = sheet.Range("A1:F1");
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

        // Data
        int row = 2;
        foreach (var kvp in stats.OrderByDescending(x => x.Value.AccuracyPercentage))
        {
            sheet.Cell(row, 1).Value = kvp.Key.ToString();
            sheet.Cell(row, 2).Value = kvp.Value.TotalGroundTruth;
            sheet.Cell(row, 3).Value = kvp.Value.CorrectPredictions;
            sheet.Cell(row, 4).Value = kvp.Value.IncorrectPredictions;
            sheet.Cell(row, 5).Value = kvp.Value.MissingPredictions;
            sheet.Cell(row, 6).Value = kvp.Value.AccuracyPercentage / 100.0;
            sheet.Cell(row, 6).Style.NumberFormat.Format = "0.00%";

            // Color code accuracy
            var accuracyCell = sheet.Cell(row, 6);
            if (kvp.Value.AccuracyPercentage >= 80)
                accuracyCell.Style.Fill.BackgroundColor = XLColor.LightGreen;
            else if (kvp.Value.AccuracyPercentage >= 60)
                accuracyCell.Style.Fill.BackgroundColor = XLColor.LightYellow;
            else
                accuracyCell.Style.Fill.BackgroundColor = XLColor.LightCoral;

            row++;
        }

        sheet.Columns().AdjustToContents();
    }
}

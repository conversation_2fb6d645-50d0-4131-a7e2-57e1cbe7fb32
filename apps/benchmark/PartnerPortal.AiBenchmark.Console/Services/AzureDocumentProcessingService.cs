using Azure;
using Azure.AI.DocumentIntelligence;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services;

public interface IDocumentProcessingService
{
    Task<List<AiPrediction>> ClassifyDocumentAsync(string filePath, string modelVersion);
    Task<AnalyzeResult> AnalyzeDocumentAsync(string dir, string filePath, string modelVersion);
}

public class AzureDocumentProcessingService : IDocumentProcessingService
{
    private readonly DocumentIntelligenceClient _client;
    private readonly ILogger<AzureDocumentProcessingService> _logger;
    private readonly IConfigurationService _configurationService;
    private readonly string _splitMode;

    public AzureDocumentProcessingService(
        ILogger<AzureDocumentProcessingService> logger,
        IConfiguration config,
        IConfigurationService configurationService
    )
    {
        var endpoint =
            config.GetValue<string>("AzureAI:DocumentIntelligence:Endpoint")
            ?? throw new Exception("AzureAI:DocumentIntelligence:Endpoint is not set");
        var key =
            config.GetValue<string>("AzureAI:DocumentIntelligence:Key")
            ?? throw new Exception("AzureAI:DocumentIntelligence:Key is not set");

        var credential = new AzureKeyCredential(key);
        _client = new DocumentIntelligenceClient(new Uri(endpoint), credential);
        _logger = logger;
        _configurationService = configurationService;
        _splitMode = config.GetValue<string>("SplitMode") ?? "auto";
    }

    public async Task<List<AiPrediction>> ClassifyDocumentAsync(string filePath, string modelVersion)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var predictions = new List<AiPrediction>();

        try
        {
            _logger.LogInformation("Classifying document: {FilePath}", filePath);

            await using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var options = new ClassifyDocumentOptions(modelVersion, await BinaryData.FromStreamAsync(fileStream))
            {
                Split = _splitMode == "auto" ? SplitMode.Auto : SplitMode.None,
            };

            Operation<AnalyzeResult> operation = await _client.ClassifyDocumentAsync(WaitUntil.Completed, options);
            var result = operation.Value;

            stopwatch.Stop();
            _logger.LogInformation(
                "Classification completed for document: {FilePath}. Processing time: {ElapsedMs}ms",
                filePath,
                stopwatch.ElapsedMilliseconds
            );

            var config = await _configurationService.LoadConfigurationAsync();

            await SaveResponseToFile(operation, config.ClassificationTestDataPath, filePath);

            foreach (var doc in result.Documents)
            {
                var documentType = MapDocumentType(doc.DocumentType);
                var confidence = doc.Confidence;

                if (doc.BoundingRegions?.Count > 0)
                {
                    var startPage = doc.BoundingRegions[0].PageNumber;
                    var endPage = doc.BoundingRegions[doc.BoundingRegions.Count - 1].PageNumber;

                    predictions.Add(new AiPrediction(startPage, endPage, documentType, confidence, doc.DocumentType));

                    _logger.LogDebug(
                        "Extracted prediction: {DocumentType} (confidence: {Confidence:P2}) pages {StartPage}-{EndPage}",
                        documentType,
                        confidence,
                        startPage,
                        endPage
                    );
                }
            }

            _logger.LogInformation(
                "Extracted {PredictionCount} predictions from {FilePath}",
                predictions.Count,
                filePath
            );
            return predictions;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Error classifying document: {FilePath}. Time elapsed before error: {ElapsedMs}ms",
                filePath,
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }

    public async Task<AnalyzeResult> AnalyzeDocumentAsync(string dir, string filePath, string modelVersion)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Extracting document: {FilePath}", filePath);

            await using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);

            Operation<AnalyzeResult> operation = await _client.AnalyzeDocumentAsync(
                WaitUntil.Completed,
                modelVersion,
                await BinaryData.FromStreamAsync(fileStream)
            );

            await SaveResponseToFile(operation, dir, filePath);

            stopwatch.Stop();
            _logger.LogInformation(
                "Extraction completed for document: {FilePath}. Processing time: {ElapsedMs}ms",
                filePath,
                stopwatch.ElapsedMilliseconds
            );

            return operation.Value;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Error extracting document: {FilePath}. Time elapsed before error: {ElapsedMs}ms",
                filePath,
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }

    private static DocumentType MapDocumentType(string azureDocumentType)
    {
        return azureDocumentType switch
        {
            "szamla" => DocumentType.Invoice,
            "TIG" => DocumentType.Tig,
            //"szallitolevel" => DocumentType.DeliveryNote,
            _ => DocumentType.Other,
        };
    }

    private async Task SaveResponseToFile(Operation<AnalyzeResult> operation, string dir, string filePath)
    {
        try
        {
            var responsesDir = Path.Combine(dir, "responses");
            Directory.CreateDirectory(responsesDir);

            var fileName = Path.GetFileName(filePath) + ".json";
            var responseFilePath = Path.Combine(responsesDir, fileName);

            _logger.LogDebug("Saving response to: {ResponseFilePath}", responseFilePath);

            var stream = operation.GetRawResponse().ContentStream;
            if (stream != null)
            {
                stream.Position = 0;
                using var sr = new StreamReader(stream);
                string json = await sr.ReadToEndAsync();
                await File.WriteAllTextAsync(responseFilePath, json);
            }
            else
            {
                _logger.LogWarning("ContentStream was null, unable to save raw response for {FilePath}", filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save response file for {FilePath}", filePath);
        }
    }
}

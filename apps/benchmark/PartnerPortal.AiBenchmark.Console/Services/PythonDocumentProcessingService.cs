using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Serialization;
using Azure.AI.DocumentIntelligence;
using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services;

public class PythonDocumentProcessingService : IDocumentProcessingService
{
    private readonly HttpClient _httpClient;

    public PythonDocumentProcessingService()
    {
        _httpClient = new HttpClient();
    }

    public async Task<List<AiPrediction>> ClassifyDocumentAsync(string filePath, string modelVersion)
    {
        try
        {
            using var formData = new MultipartFormDataContent();

            await using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var fileContent = new StreamContent(fileStream);
            fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
            formData.Add(fileContent, "file", Path.GetFileName(filePath));

            var requestUrl = "http://localhost:8000/predict?return_text=true";

            var response = await _httpClient.PostAsync(requestUrl, formData);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(
                    $"Python service returned {response.StatusCode}: {response.ReasonPhrase}"
                );
            }

            var jsonResponse = await response.Content.ReadAsStringAsync();
            var pythonResponse =
                JsonSerializer.Deserialize<PythonPredictionResponse>(jsonResponse)
                ?? throw new JsonException("Failed to deserialize Python service response");

            var predictions = new List<AiPrediction>
            {
                new(
                    startPage: 1,
                    endPage: 1,
                    documentType: MapDocumentType(pythonResponse.DocumentType),
                    confidence: pythonResponse.Confidence,
                    actualDocTypeFromResponse: pythonResponse.DocumentType
                ),
            };

            return predictions;
        }
        catch (Exception ex)
        {
            throw new Exception($"Error calling Python document processing service: {ex.Message}", ex);
        }
    }

    private static DocumentType MapDocumentType(string pythonDocumentType)
    {
        return pythonDocumentType switch
        {
            "DeliveryNote" => DocumentType.Other,
            "CompletionCertificate" => DocumentType.Tig,
            "Invoice" => DocumentType.Invoice,
            "Unknown" => DocumentType.Other,
            _ => DocumentType.Other,
        };
    }

    public Task<AnalyzeResult> AnalyzeDocumentAsync(string dir, string filePath, string modelVersion)
    {
        throw new NotSupportedException(
            "AnalyzeDocumentAsync is not supported for PythonDocumentProcessingService. Use AzureDocumentProcessingService instead."
        );
    }
}

// Response model for Python service API
public record PythonPredictionResponse
{
    [JsonPropertyName("document_type")]
    public string DocumentType { get; set; } = string.Empty;

    [JsonPropertyName("confidence")]
    public double Confidence { get; set; }

    [JsonPropertyName("language")]
    public string Language { get; set; } = string.Empty;

    [JsonPropertyName("processing_time_ms")]
    public int ProcessingTimeMs { get; set; }

    [JsonPropertyName("model_version")]
    public string ModelVersion { get; set; } = string.Empty;

    [JsonPropertyName("features_version")]
    public string FeaturesVersion { get; set; } = string.Empty;

    [JsonPropertyName("warnings")]
    public List<string> Warnings { get; set; } = [];

    [JsonPropertyName("raw_text")]
    public string RawText { get; set; } = string.Empty;

    [JsonPropertyName("weighted_text_sample")]
    public string WeightedTextSample { get; set; } = string.Empty;
}

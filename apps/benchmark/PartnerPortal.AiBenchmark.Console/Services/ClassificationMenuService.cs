using System.Text;
using PartnerPortal.AiBenchmark.Console.Models;
using Spectre.Console;

namespace PartnerPortal.AiBenchmark.Console.Services;

public class ClassificationMenuService
{
    private readonly ICoreService _coreService;
    private readonly IResultsService _resultsService;
    private readonly IReportService _reportService;
    private readonly IDuplicateDetectionService _duplicateDetectionService;
    private readonly IDuplicateReportService _duplicateReportService;
    private readonly IPdfSimilarityService _pdfSimilarityService;
    private readonly IPdfSimilarityReportService _pdfSimilarityReportService;
    private readonly IConfigurationService _configService;
    private readonly IGroundTruthImportService _groundTruthImportService;

    public ClassificationMenuService(
        ICoreService coreService,
        IResultsService resultsService,
        IReportService reportService,
        IDuplicateDetectionService duplicateDetectionService,
        IDuplicateReportService duplicateReportService,
        IPdfSimilarityService pdfSimilarityService,
        IPdfSimilarityReportService pdfSimilarityReportService,
        IConfigurationService configService,
        IGroundTruthImportService groundTruthImportService
    )
    {
        _coreService = coreService;
        _resultsService = resultsService;
        _reportService = reportService;
        _duplicateDetectionService = duplicateDetectionService;
        _duplicateReportService = duplicateReportService;
        _pdfSimilarityService = pdfSimilarityService;
        _pdfSimilarityReportService = pdfSimilarityReportService;
        _configService = configService;
        _groundTruthImportService = groundTruthImportService;
    }

    public async Task StartAsync()
    {
        AnsiConsole.Write(new FigletText("AI Benchmark").LeftJustified().Color(Color.Blue));

        while (true)
        {
            var choice = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("[green]Azure Document AI Benchmark Tool[/]")
                    .PageSize(10)
                    .AddChoices(
                        [
                            "📁 Configure Test Data Path",
                            "📋 Manage Ground Truths",
                            "🚀 Start Analysis",
                            "📊 View Results",
                            "📈 Generate Reports",
                            "ℹ️  Show Current Configuration",
                            "📝 Export PDF filenames to txt",
                            "🔄 Convert to flat folder structure",
                            "🔍 Find Duplicate Files",
                            "📄 Find Similar PDF Files",
                            "❌ Exit",
                        ]
                    )
            );

            try
            {
                switch (choice)
                {
                    case "📁 Configure Test Data Path":
                        await ConfigureTestDataPathAsync();
                        break;
                    case "📋 Manage Ground Truths":
                        await ManageGroundTruthsAsync();
                        break;
                    case "🚀 Start Analysis":
                        await StartAnalysisAsync();
                        break;
                    case "📊 View Results":
                        await ViewResultsAsync();
                        break;
                    case "📈 Generate Reports":
                        await GenerateReportsAsync();
                        break;
                    case "ℹ️  Show Current Configuration":
                        await ShowCurrentConfigurationAsync();
                        break;
                    case "📝 Export PDF filenames to txt":
                        await ExportAndNormalizePdfFilenamesAsync();
                        break;
                    case "🔄 Convert to flat folder structure":
                        ConvertToFlatFolderStructure();
                        break;
                    case "🔍 Find Duplicate Files":
                        await FindDuplicateFilesAsync();
                        break;
                    case "📄 Find Similar PDF Files":
                        await FindSimilarPdfFilesAsync();
                        break;
                    case "❌ Exit":
                        AnsiConsole.MarkupLine("[yellow]Goodbye![/]");
                        return;
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]❌ Error: {ex.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            System.Console.ReadKey();
            AnsiConsole.Clear();
        }
    }

    private async Task ExportAndNormalizePdfFilenamesAsync()
    {
        var config = await _configService.LoadConfigurationAsync();

        if (string.IsNullOrEmpty(config.ClassificationTestDataPath))
        {
            AnsiConsole.MarkupLine("[red]❌ Please configure the test data path first![/]");
            return;
        }

        var allFiles = _coreService.GetFiles(config.ClassificationTestDataPath);
        var pdfPaths = allFiles
            .Where(f => string.Equals(Path.GetExtension(f), ".pdf", StringComparison.OrdinalIgnoreCase))
            .ToList();

        if (pdfPaths.Count == 0)
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No PDF files found in the configured Test Data Path.[/]");
            return;
        }

        // Normalize and rename files to FormC within the folder, overwrite on collisions
        var folderPath = config.ClassificationTestDataPath;
        var renamedCount = 0;
        foreach (var sourcePath in pdfPaths)
        {
            try
            {
                var originalName = Path.GetFileName(sourcePath) ?? string.Empty;
                if (string.IsNullOrEmpty(originalName))
                    continue;

                var normalizedName = originalName.Normalize(NormalizationForm.FormC);
                if (string.Equals(originalName, normalizedName, StringComparison.Ordinal))
                {
                    continue; // already normalized
                }

                var targetPath = Path.Combine(folderPath, normalizedName);

                if (!string.Equals(sourcePath, targetPath, StringComparison.Ordinal))
                {
                    File.Move(sourcePath, targetPath, true);
                    renamedCount++;
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine(
                    $"[red]❌ Failed to rename file:[/] [yellow]{sourcePath}[/] [red]- {ex.Message}[/]"
                );
            }
        }

        // Refresh list after renames
        allFiles = _coreService.GetFiles(config.ClassificationTestDataPath);
        var pdfFiles = allFiles
            .Where(f => string.Equals(Path.GetExtension(f), ".pdf", StringComparison.OrdinalIgnoreCase))
            .Select(f => Path.GetFileName(f) ?? string.Empty)
            .Where(n => !string.IsNullOrEmpty(n))
            .ToList();

        if (pdfFiles.Count == 0)
        {
            AnsiConsole.MarkupLine(
                "[yellow]⚠️  No PDF files found in the configured Test Data Path after renaming.[/]"
            );
            return;
        }

        var defaultFileName = "pdf_filenames.txt";
        var outputFileName = AnsiConsole.Prompt(
            new TextPrompt<string>($"Enter output [green].txt[/] file name (default: {defaultFileName}):").AllowEmpty()
        );

        if (string.IsNullOrWhiteSpace(outputFileName))
        {
            outputFileName = defaultFileName;
        }

        if (!outputFileName.EndsWith(".txt", StringComparison.OrdinalIgnoreCase))
        {
            outputFileName += ".txt";
        }

        var outputPath = Path.Combine(config.ClassificationTestDataPath, outputFileName);

        try
        {
            File.WriteAllLines(outputPath, pdfFiles, new UTF8Encoding(encoderShouldEmitUTF8Identifier: true));
            if (renamedCount > 0)
            {
                AnsiConsole.MarkupLine($"[green]✅ Renamed {renamedCount} file(s) to FormC normalized names.[/]");
            }
            AnsiConsole.MarkupLine(
                $"[green]✅ Exported {pdfFiles.Count} PDF filename(s) to:[/] [yellow]{outputPath}[/]"
            );
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Failed to write file: {ex.Message}[/]");
        }
    }

    private async Task ConfigureTestDataPathAsync()
    {
        AnsiConsole.MarkupLine("[blue]Configure Test Data Path[/]");
        AnsiConsole.WriteLine();

        var config = await _configService.LoadConfigurationAsync();
        if (!string.IsNullOrEmpty(config.ClassificationTestDataPath))
        {
            AnsiConsole.MarkupLine($"[dim]Current path:[/] [yellow]{config.ClassificationTestDataPath}[/]");
            AnsiConsole.WriteLine();
        }

        var newPath = AnsiConsole.Prompt(
            new TextPrompt<string>("Enter the [green]folder path[/] containing test files:").AllowEmpty()
        );

        if (string.IsNullOrEmpty(newPath))
        {
            AnsiConsole.MarkupLine("[yellow] No path provided.[/]");
            return;
        }

        var result = await _coreService.SetTestDataPathAsync(newPath);

        if (result.Contains("does not exist"))
        {
            AnsiConsole.MarkupLine($"[red]❌ {result}[/]");
        }
        else
        {
            AnsiConsole.MarkupLine($"[green]✅ {result}[/]");
        }
    }

    private async Task ManageGroundTruthsAsync()
    {
        var config = await _configService.LoadConfigurationAsync();

        if (string.IsNullOrEmpty(config.ClassificationTestDataPath))
        {
            AnsiConsole.MarkupLine("[red]❌ Please configure the test data path first![/]");
            return;
        }

        var testFiles = _coreService.GetFiles(config.ClassificationTestDataPath);
        if (testFiles.Count == 0)
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No supported files found in the test data directory![/]");
            return;
        }

        while (true)
        {
            var choice = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("[blue]Ground Truth Management[/]")
                    .AddChoices(
                        [
                            "📝 Set Ground Truth for a File",
                            "✏️  Edit Existing Ground Truth",
                            "📋 View All Ground Truths",
                            "🗑️  Remove Ground Truth",
                            "🤖 Auto Set Ground Truth Based on File Name",
                            "📥 Import Ground Truths from Excel",
                            "🔙 Back to Main Menu",
                        ]
                    )
            );

            switch (choice)
            {
                case "📝 Set Ground Truth for a File":
                    await ManageGroundTruthForFilesAsync(config.ClassificationTestDataPath, testFiles);
                    break;
                case "✏️  Edit Existing Ground Truth":
                    await EditGroundTruthAsync(config.ClassificationTestDataPath);
                    break;
                case "📋 View All Ground Truths":
                    await ViewAllGroundTruthsAsync(config.ClassificationTestDataPath);
                    break;
                case "🗑️  Remove Ground Truth":
                    await RemoveGroundTruthAsync(config.ClassificationTestDataPath);
                    break;
                case "🤖 Auto Set Ground Truth Based on File Name":
                    await AutoSetGroundTruthAsync(config.ClassificationTestDataPath, testFiles);
                    break;
                case "📥 Import Ground Truths from Excel":
                    await ImportGroundTruthsFromExcelAsync();
                    break;
                case "🔙 Back to Main Menu":
                    return;
            }

            AnsiConsole.WriteLine();
        }
    }

    private void ConvertToFlatFolderStructure()
    {
        AnsiConsole.MarkupLine("[blue]Convert to Flat Folder Structure[/]");

        AnsiConsole.MarkupLine("[blue]The other parts of the application can only work with flat folder structure.[/]");

        AnsiConsole.MarkupLine("[blue]This will move all files from nested subdirectories to the root folder.[/]");
        AnsiConsole.MarkupLine(
            "[blue]Filenames will be renamed to include the folder structure using double underscores (__) as separators.[/]"
        );

        var table = new Table();
        table.AddColumn(new TableColumn("Original Structure").LeftAligned());
        table.AddColumn(new TableColumn("Flat Structure").LeftAligned());

        table.AddRow(
            new Markup("[yellow]Würth Kft/1/számla.pdf[/]"),
            new Markup("[yellow]Würth Kft__1__számla.pdf[/]")
        );
        table.AddRow(
            new Markup("[yellow]FolderName1/FolderName2/filename.pdf[/]"),
            new Markup("[yellow]FolderName1__FolderName2__filename.pdf[/]")
        );

        AnsiConsole.Write(table);
        AnsiConsole.WriteLine();

        var folderPath = AnsiConsole.Ask<string>("Enter the [green]folder path[/] to convert:");

        AnsiConsole.MarkupLine("[yellow]⚠️ Warning: Make sure you have a backup of the original folder structure.[/]");
        AnsiConsole.WriteLine();

        var confirm = AnsiConsole.Confirm("Are you sure you want to continue?");
        if (!confirm)
        {
            AnsiConsole.MarkupLine("[yellow]Operation cancelled.[/]");
            return;
        }

        try
        {
            AnsiConsole.MarkupLine("[blue]🔄 Converting folder structure...[/]");

            _coreService.ConvertToFlatFolderStructure(folderPath);

            AnsiConsole.MarkupLine("[green]✅ Flat folder structure created successfully![/]");
            AnsiConsole.MarkupLine("[green]All files have been moved to the root directory with updated names.[/]");
        }
        catch (DirectoryNotFoundException ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Directory not found: {ex.Message}[/]");
        }
        catch (InvalidOperationException ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Operation failed: {ex.Message}[/]");
        }
        catch (UnauthorizedAccessException ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Access denied: {ex.Message}[/]");
            AnsiConsole.MarkupLine("[yellow]Make sure you have write permissions to the folder and its files.[/]");
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Unexpected error: {ex.Message}[/]");
        }
    }

    private async Task ManageGroundTruthForFilesAsync(string testDataPath, List<string> testFiles)
    {
        while (true)
        {
            // Load current results to check which files have ground truth
            var results = await _resultsService.LoadResultsAsync(testDataPath);
            var filesWithGroundTruth = results
                .Results.Where(r => r.GroundTruthDocuments.Count > 0)
                .Select(r => r.FileName)
                .ToHashSet();

            var choices = new List<string>();

            // Add files with visual indicators
            foreach (var testFile in testFiles)
            {
                var currentFileName = Path.GetFileName(testFile) ?? "Unknown";
                if (filesWithGroundTruth.Contains(currentFileName))
                {
                    choices.Add($"✅ {currentFileName}");
                }
                else
                {
                    choices.Add($"❌ {currentFileName}");
                }
            }

            choices.Add("🔙 Back to Ground Truth Management");

            // Show summary
            var totalFiles = testFiles.Count;
            var completedFiles = filesWithGroundTruth.Count;
            AnsiConsole.MarkupLine($"[blue]Progress: {completedFiles}/{totalFiles} files have ground truth set[/]");
            AnsiConsole.WriteLine();

            var selection = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .PageSize(50)
                    .EnableSearch()
                    .Title("Select a [green]file[/] to set/edit ground truth:")
                    .AddChoices(choices)
            );

            if (selection == "🔙 Back to Ground Truth Management")
                return;

            // Extract filename (remove the indicator)
            var fileName = selection[2..].Trim(); // Remove "✅ " or "❌ "
            var selectedFile = testFiles.First(f => Path.GetFileName(f) == fileName);
            var hasExistingGroundTruth = filesWithGroundTruth.Contains(fileName);

            // Show current ground truth if exists
            if (hasExistingGroundTruth)
            {
                var existingFile = results.Results.First(r => r.FileName == fileName);
                AnsiConsole.MarkupLine($"[yellow]Current ground truth for {fileName}:[/]");
                foreach (var doc in existingFile.GroundTruthDocuments)
                {
                    var pageRange = doc.ToPageRange();
                    AnsiConsole.MarkupLine($"[dim]  • {pageRange}[/]");
                }
                AnsiConsole.WriteLine();
            }

            await SetGroundTruthAsync(testDataPath, fileName, hasExistingGroundTruth);

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            System.Console.ReadKey();
            AnsiConsole.Clear();
        }
    }

    private async Task SetGroundTruthAsync(string testDataPath, string selectedFileName, bool hasExisting)
    {
        var action = hasExisting ? "Updating" : "Setting";
        AnsiConsole.MarkupLine($"[blue]{action} ground truth for:[/] [yellow]{selectedFileName}[/]");

        if (hasExisting)
        {
            AnsiConsole.MarkupLine("[dim]Current ranges will be replaced with new ones.[/]");
        }
        AnsiConsole.WriteLine();

        var pageRanges = new List<PageRange>();

        AnsiConsole.MarkupLine(
            "[green]Enter page ranges (e.g., \"1-2\") and document types.[/] [yellow]press Enter[/] to finish"
        );
        AnsiConsole.MarkupLine("[dim]Note: Page ranges must be exclusive and sequential (no overlaps or gaps).[/]");
        AnsiConsole.WriteLine();

        while (true)
        {
            var pageRangeInput = AnsiConsole.Prompt(new TextPrompt<string>("Page range:").AllowEmpty());

            if (string.IsNullOrWhiteSpace(pageRangeInput))
                break;

            if (!TryParsePageRange(pageRangeInput, out int startPage, out int endPage))
            {
                AnsiConsole.MarkupLine("[red]❌ Invalid page range format. Use format like '1-2' or '5-5'[/]");
                continue;
            }

            var validationResult = ValidatePageRange(startPage, endPage, pageRanges);
            if (!validationResult.IsValid)
            {
                AnsiConsole.MarkupLine($"[red]❌ {validationResult.ErrorMessage}[/]");
                continue;
            }

            var documentType = AnsiConsole.Prompt(
                new SelectionPrompt<DocumentType>()
                    .Title("Select [green]document type[/]:")
                    .AddChoices(Enum.GetValues<DocumentType>())
            );

            var newRange = new PageRange(startPage, endPage, documentType);
            pageRanges.Add(newRange);

            AnsiConsole.MarkupLine($"[green]✅ Added: {newRange}[/]");

            if (pageRanges.Count > 0)
            {
                var lastRange = pageRanges.OrderBy(pr => pr.StartPage).Last();
                var nextExpected = lastRange.EndPage + 1;
                AnsiConsole.MarkupLine($"[dim]Next range should start from page {nextExpected}[/]");
            }

            AnsiConsole.WriteLine();
        }

        if (pageRanges.Count > 0)
        {
            await _resultsService.AddOrUpdateGroundTruthAsync(testDataPath, selectedFileName, pageRanges);
            AnsiConsole.MarkupLine($"[green]✅ Ground truth saved for {selectedFileName}[/]");
        }
        else
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No page ranges were added.[/]");
        }
    }

    private static bool TryParsePageRange(string input, out int startPage, out int endPage)
    {
        startPage = 0;
        endPage = 0;

        if (string.IsNullOrWhiteSpace(input))
            return false;

        var parts = input.Split('-', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length != 2)
            return false;

        if (!int.TryParse(parts[0].Trim(), out startPage) || !int.TryParse(parts[1].Trim(), out endPage))
            return false;

        return startPage > 0 && endPage > 0 && startPage <= endPage;
    }

    private static PageRangeValidationResult ValidatePageRange(
        int startPage,
        int endPage,
        List<PageRange> existingRanges
    )
    {
        if (existingRanges.Count == 0)
        {
            if (startPage != 1)
            {
                return new PageRangeValidationResult(false, "First page range must start from page 1.");
            }
            return new PageRangeValidationResult(true, string.Empty);
        }

        var sortedRanges = existingRanges.OrderBy(pr => pr.StartPage).ToList();

        foreach (var existingRange in sortedRanges)
        {
            if (RangesOverlap(startPage, endPage, existingRange.StartPage, existingRange.EndPage))
            {
                return new PageRangeValidationResult(
                    false,
                    $"Range {startPage}-{endPage} overlaps with existing range {existingRange.StartPage}-{existingRange.EndPage} ({existingRange.DocumentType})."
                );
            }
        }

        var lastRange = sortedRanges.Last();
        var expectedStartPage = lastRange.EndPage + 1;

        if (startPage != expectedStartPage)
        {
            if (startPage < expectedStartPage)
            {
                return new PageRangeValidationResult(
                    false,
                    $"Range {startPage}-{endPage} creates an overlap. Next range should start from page {expectedStartPage}."
                );
            }
            else
            {
                return new PageRangeValidationResult(
                    false,
                    $"Range {startPage}-{endPage} creates a gap. Next range should start from page {expectedStartPage}, not {startPage}."
                );
            }
        }

        return new PageRangeValidationResult(true, string.Empty);
    }

    private static bool RangesOverlap(int start1, int end1, int start2, int end2)
    {
        return start1 <= end2 && start2 <= end1;
    }

    private record PageRangeValidationResult(bool IsValid, string ErrorMessage);

    private async Task EditGroundTruthAsync(string testDataPath)
    {
        while (true)
        {
            var results = await _resultsService.LoadResultsAsync(testDataPath);
            var groundTruthFiles = results.Results.Where(r => r.GroundTruthDocuments.Count > 0).ToList();

            if (groundTruthFiles.Count == 0)
            {
                AnsiConsole.MarkupLine("[yellow]⚠️  No ground truths configured yet![/]");
                return;
            }

            var choices = new List<string>();
            foreach (var file in groundTruthFiles)
            {
                choices.Add($"✏️  {file.FileName}");
            }
            choices.Add("🔙 Back to Ground Truth Management");

            var selection = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a file to [yellow]edit[/]:")
                    .EnableSearch()
                    .PageSize(50)
                    .AddChoices(choices)
            );

            if (selection == "🔙 Back to Ground Truth Management")
                return;

            var selectedFileName = selection[3..];
            var selectedFile = groundTruthFiles.First(f => f.FileName == selectedFileName);

            AnsiConsole.MarkupLine($"[yellow]Editing ground truth for: {selectedFile.FileName}[/]");

            AnsiConsole.MarkupLine("[blue]Current page ranges:[/]");
            foreach (var doc in selectedFile.GroundTruthDocuments)
            {
                var pageRange = doc.ToPageRange();
                AnsiConsole.MarkupLine($"[dim]  • {pageRange}[/]");
            }
            AnsiConsole.WriteLine();

            AnsiConsole.MarkupLine("[dim]Current ranges will be cleared. Please re-enter all page ranges.[/]");

            var config = await _configService.LoadConfigurationAsync();
            var testFiles = _coreService.GetFiles(config.ClassificationTestDataPath);
            var filePath = testFiles.First(f => Path.GetFileName(f) == selectedFile.FileName);

            await SetGroundTruthAsync(testDataPath, selectedFile.FileName, true);

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            System.Console.ReadKey();
            AnsiConsole.Clear();
        }
    }

    private async Task RemoveGroundTruthAsync(string testDataPath)
    {
        while (true)
        {
            var results = await _resultsService.LoadResultsAsync(testDataPath);
            var groundTruthFiles = results.Results.Where(r => r.GroundTruthDocuments.Count > 0).ToList();

            if (groundTruthFiles.Count == 0)
            {
                AnsiConsole.MarkupLine("[yellow]⚠️  No ground truths configured yet![/]");
                return;
            }

            var choices = new List<string>();
            foreach (var file in groundTruthFiles)
            {
                choices.Add($"🗑️  {file.FileName}");
            }
            choices.Add("🔙 Back to Ground Truth Management");

            var selection = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a file to [red]remove[/] ground truth:")
                    .PageSize(50)
                    .EnableSearch()
                    .AddChoices(choices)
            );

            if (selection == "🔙 Back to Ground Truth Management")
                return;

            var selectedFileName = selection[3..].Trim();
            var selectedFile = groundTruthFiles.First(f => f.FileName == selectedFileName);

            var confirm = AnsiConsole.Confirm(
                $"Are you sure you want to remove ground truth for [yellow]{selectedFile.FileName}[/]?"
            );

            if (confirm)
            {
                selectedFile.GroundTruthDocuments.Clear();

                if (selectedFile.AiPredictions.Count == 0)
                {
                    results.Results.Remove(selectedFile);
                }

                await _resultsService.SaveResultsAsync(testDataPath, results);
                AnsiConsole.MarkupLine($"[green]✅ Ground truth removed for {selectedFile.FileName}[/]");

                AnsiConsole.WriteLine();
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                System.Console.ReadKey();
                AnsiConsole.Clear();
            }
        }
    }

    private async Task AutoSetGroundTruthAsync(string testDataPath, List<string> testFiles)
    {
        var confirm = AnsiConsole.Confirm("Are you sure you want to auto-set ground truths based on file names?");
        if (!confirm)
        {
            return;
        }

        var results = await _resultsService.LoadResultsAsync(testDataPath);
        var groundTruthFiles = results.Results.Where(r => r.GroundTruthDocuments.Count > 0).ToList();

        var processedCount = 0;

        AnsiConsole.MarkupLine("[blue]Auto-setting ground truths based on file names...[/]");
        AnsiConsole.WriteLine();

        foreach (var testFile in testFiles)
        {
            var fileName = Path.GetFileName(testFile);

            DocumentType documentType;

            if (fileName.StartsWith("Invoice__", StringComparison.CurrentCulture))
            {
                documentType = DocumentType.Invoice;
            }
            else if (fileName.StartsWith("CompletionCertificate__", StringComparison.CurrentCulture))
            {
                documentType = DocumentType.Tig;
            }
            else
            {
                documentType = DocumentType.Other;
            }

            var pageRanges = new List<PageRange> { new(1, 1, documentType) };

            try
            {
                await _resultsService.AddOrUpdateGroundTruthAsync(testDataPath, fileName, pageRanges);
                AnsiConsole.MarkupLine($"✅ Set [green]{documentType}[/] ground truth for [yellow]{fileName}[/]");
                processedCount++;
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]❌ Failed to set ground truth for {fileName}: {ex.Message}[/]");
            }
        }

        AnsiConsole.WriteLine();
        AnsiConsole.MarkupLine($"[blue]Auto-setting completed:[/]");
        AnsiConsole.MarkupLine($"[green]✅ Processed: {processedCount} files[/]");
    }

    private async Task ViewAllGroundTruthsAsync(string testDataPath)
    {
        var results = await _resultsService.LoadResultsAsync(testDataPath);
        var groundTruthFiles = results.Results.Where(r => r.GroundTruthDocuments.Count > 0).ToList();

        if (groundTruthFiles.Count == 0)
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No ground truths configured yet![/]");
            return;
        }

        var table = new Table();
        table.AddColumn(new TableColumn("Index").LeftAligned());
        table.AddColumn(new TableColumn("File Name").LeftAligned());
        table.AddColumn(new TableColumn("Page Ranges").LeftAligned());
        int index = 1;
        foreach (var file in groundTruthFiles)
        {
            var ranges = string.Join(
                "\n",
                file.GroundTruthDocuments.Select(doc =>
                {
                    var pageRange = doc.ToPageRange();
                    return pageRange.ToString();
                })
            );
            table.AddRow(index.ToString(), file.FileName, ranges);
            index++;
        }

        AnsiConsole.Write(table);
    }

    private async Task ImportGroundTruthsFromExcelAsync()
    {
        var config = await _configService.LoadConfigurationAsync();
        if (string.IsNullOrEmpty(config.ClassificationTestDataPath))
        {
            AnsiConsole.MarkupLine("[red]❌ Please configure the test data path first![/]");
            return;
        }

        // Offer a dropdown of .xlsx files under the Test Data Path
        var excelFiles = Directory
            .GetFiles(config.ClassificationTestDataPath, "*.xlsx", SearchOption.TopDirectoryOnly)
            .OrderBy(Path.GetFileName)
            .ToList();

        if (excelFiles.Count == 0)
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No .xlsx files found in the configured Test Data Path.[/]");
            return;
        }

        var choices = excelFiles.Select(f => Path.GetFileName(f) ?? f).ToList();
        var selection = AnsiConsole.Prompt(
            new SelectionPrompt<string>()
                .Title("Select an [green]Excel file[/] to import from:")
                .PageSize(20)
                .AddChoices(choices)
        );
        if (string.IsNullOrEmpty(selection))
        {
            AnsiConsole.MarkupLine("[yellow]No file selected.[/]");
            return;
        }
        var cleanPath = Path.Combine(config.ClassificationTestDataPath, selection);

        try
        {
            await _groundTruthImportService.ImportFromExcelAsync(config.ClassificationTestDataPath, cleanPath);
            AnsiConsole.MarkupLine("[green]✅ Ground truths imported successfully from Excel[/]");
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Import failed: {ex.Message}[/]");
            throw;
        }
    }

    private async Task ViewResultsAsync()
    {
        var config = await _configService.LoadConfigurationAsync();

        if (string.IsNullOrEmpty(config.ClassificationTestDataPath))
        {
            AnsiConsole.MarkupLine("[red]❌ Please configure the test data path first![/]");
            return;
        }

        var results = await _resultsService.LoadResultsAsync(config.ClassificationTestDataPath);

        if (results.Results.Count == 0)
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No results available yet![/]");
            return;
        }

        var table = new Table();
        table.AddColumn(new TableColumn("File Name").LeftAligned());
        table.AddColumn(new TableColumn("Ground Truth").Centered());
        table.AddColumn(new TableColumn("AI Predictions").Centered());
        table.AddColumn(new TableColumn("Analysis Date").Centered());
        table.AddColumn(new TableColumn("Model Version").Centered());

        foreach (var file in results.Results)
        {
            var groundTruth = file.GroundTruthDocuments.Count > 0 ? "✅" : "❌";
            var aiPredictions = file.AiPredictions.Count > 0 ? "✅" : "❌";
            var analysisDate = file.AnalysisRunTimestamp?.ToString("yyyy-MM-dd HH:mm") ?? "N/A";
            var modelVersion = file.ModelVersion ?? "N/A";

            table.AddRow(file.FileName, groundTruth, aiPredictions, analysisDate, modelVersion);
        }

        AnsiConsole.Write(table);
    }

    private async Task ShowCurrentConfigurationAsync()
    {
        var config = await _configService.LoadConfigurationAsync();
        var testFileCount = string.IsNullOrEmpty(config.ClassificationTestDataPath)
            ? 0
            : _coreService.GetFiles(config.ClassificationTestDataPath).Count;

        var results = await _resultsService.LoadResultsAsync(config.ClassificationTestDataPath);
        var groundTruthCount = results.Results.Count(r => r.GroundTruthDocuments.Count > 0);
        var aiPredictionCount = results.Results.Count(r => r.AiPredictions.Count > 0);

        var panel = new Panel(
            new Markup(
                $"[blue]Test Data Path:[/] [yellow]{config.ClassificationTestDataPath ?? "Not configured"}[/]\n"
                    + $"[blue]Test Files Found:[/] [green]{testFileCount} file(s)[/]\n"
                    + $"[blue]Ground Truths:[/] [green]{groundTruthCount} file(s) configured[/]\n"
                    + $"[blue]AI Predictions:[/] [green]{aiPredictionCount} file(s) analyzed[/]\n"
                    + $"[blue]Last Modified:[/] [dim]{config.LastModified:yyyy-MM-dd HH:mm:ss}[/]"
            )
        )
            .Header("[blue]Current Configuration[/]")
            .Expand();

        AnsiConsole.Write(panel);
    }

    private async Task StartAnalysisAsync()
    {
        try
        {
            var confirm = AnsiConsole.Confirm("Are you sure you want to start the analysis?");
            if (!confirm)
            {
                return;
            }

            AnsiConsole.MarkupLine("[green]🚀 Starting AI analysis...[/]");

            var result = await AnsiConsole
                .Progress()
                .AutoClear(false)
                .Columns(
                    [
                        new TaskDescriptionColumn(),
                        new ProgressBarColumn(),
                        new PercentageColumn(),
                        new ElapsedTimeColumn(),
                        new SpinnerColumn(),
                    ]
                )
                .StartAsync(async ctx =>
                {
                    var task = ctx.AddTask("[green]Running AI analysis[/]", maxValue: 100);

                    var progress = new Progress<AnalysisProgress>(update =>
                    {
                        var percentage =
                            update.TotalFiles > 0 ? (double)update.ProcessedFiles / update.TotalFiles * 100 : 0;
                        task.Value = percentage;
                        task.Description =
                            $"[green]Processing:[/] [yellow]{update.CurrentFile}[/] ([blue]{update.ProcessedFiles}/{update.TotalFiles}[/])";
                    });

                    var analysisResult = await _resultsService.RunClassificationAsync(progress);

                    task.Value = 100;
                    task.Description = "[green]Analysis completed![/]";

                    return analysisResult;
                });

            AnsiConsole.MarkupLine($"[green]✅ AI analysis completed![/]");
            AnsiConsole.MarkupLine($"[blue]Processed Files:[/] {result.ProcessedFiles}/{result.TotalFiles}");
            AnsiConsole.MarkupLine($"[red]Failed Files: {result.FailedFiles}[/]");
            AnsiConsole.MarkupLine($"[blue]Processing Time:[/] {result.ProcessingTime}");

            if (result.FailedFiles > 0)
            {
                AnsiConsole.MarkupLine(
                    $"[yellow]⚠️  {result.FailedFiles} file(s) failed to process. Check logs for details.[/]"
                );
            }
        }
        catch (InvalidOperationException ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ {ex.Message}[/]");
        }
    }

    private async Task GenerateReportsAsync()
    {
        var config = await _configService.LoadConfigurationAsync();

        if (string.IsNullOrEmpty(config.ClassificationTestDataPath))
        {
            AnsiConsole.MarkupLine("[red]❌ Please configure the test data path first![/]");
            return;
        }

        var results = await _resultsService.LoadResultsAsync(config.ClassificationTestDataPath);

        if (results.Results.Count == 0)
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No results available yet![/]");
            return;
        }

        // Check if we have both ground truth and AI predictions
        var filesWithBoth = results.Results.Count(r => r.GroundTruthDocuments.Count > 0 && r.AiPredictions.Count > 0);

        if (filesWithBoth == 0)
        {
            AnsiConsole.MarkupLine("[yellow]⚠️  No files have both ground truth and AI predictions for comparison![/]");
            return;
        }

        while (true)
        {
            var availableReports = await _reportService.GetAvailableReportsAsync();
            var reportChoices = availableReports.Select(r => r.DisplayName).ToList();
            reportChoices.Add("🔙 Back to Main Menu");

            var choice = AnsiConsole.Prompt(
                new SelectionPrompt<string>().Title("[blue]Report Generation[/]").AddChoices(reportChoices)
            );

            if (choice == "🔙 Back to Main Menu")
            {
                return;
            }

            var selectedReport = availableReports.FirstOrDefault(r => r.DisplayName == choice);
            if (selectedReport != null)
            {
                await _reportService.ExecuteReportAsync(selectedReport.ReportType);
            }
            else
            {
                AnsiConsole.MarkupLine("[red]❌ Unknown report type selected![/]");
            }

            AnsiConsole.WriteLine();
        }
    }

    private async Task FindDuplicateFilesAsync()
    {
        AnsiConsole.MarkupLine("[blue]Find Duplicate Files[/]");
        AnsiConsole.WriteLine();

        var folderPath = AnsiConsole.Prompt(
            new TextPrompt<string>("Enter the [green]folder path[/] to scan for duplicates:")
        );

        try
        {
            var result = await _duplicateDetectionService.FindDuplicatesAsync(folderPath);
            _duplicateReportService.DisplayResults(result);

            if (result.DuplicateGroups.Count > 0)
            {
                AnsiConsole.WriteLine();

                // Ask about exporting
                var exportChoice = AnsiConsole.Confirm("Would you like to export duplicate paths to a text file?");
                if (exportChoice)
                {
                    try
                    {
                        var exportPath = await _duplicateDetectionService.ExportDuplicatesAsync(result, folderPath);
                        AnsiConsole.MarkupLine($"[green]✅ Duplicate report exported to: {exportPath}[/]");
                    }
                    catch (Exception ex)
                    {
                        AnsiConsole.MarkupLine($"[red]❌ Failed to export report: {ex.Message}[/]");
                    }
                }

                AnsiConsole.WriteLine();

                // Ask about deleting duplicates
                var deleteChoice = AnsiConsole.Confirm(
                    $"[bold red]Would you like to delete the {result.TotalDuplicates} duplicate files?[/]\n"
                        + "[dim]This will keep the first file in each group and delete the rest.[/]"
                );

                if (deleteChoice)
                {
                    var confirmDelete = AnsiConsole.Confirm(
                        "[bold red]⚠️  This action cannot be undone! Are you absolutely sure?[/]"
                    );

                    if (confirmDelete)
                    {
                        try
                        {
                            AnsiConsole.MarkupLine("[yellow]🗑️  Deleting duplicate files...[/]");
                            var deletionResult = await _duplicateDetectionService.DeleteDuplicatesAsync(result);
                            _duplicateReportService.DisplayDeletionResults(deletionResult);
                        }
                        catch (Exception ex)
                        {
                            AnsiConsole.MarkupLine($"[red]❌ Error during file deletion: {ex.Message}[/]");
                        }
                    }
                    else
                    {
                        AnsiConsole.MarkupLine("[yellow]Deletion cancelled.[/]");
                    }
                }
            }
        }
        catch (DirectoryNotFoundException ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ {ex.Message}[/]");
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Error during duplicate detection: {ex.Message}[/]");
        }
    }

    private async Task FindSimilarPdfFilesAsync()
    {
        AnsiConsole.MarkupLine("[blue]Find Similar PDF Files[/]");
        AnsiConsole.WriteLine();

        var folderPath = AnsiConsole.Prompt(
            new TextPrompt<string>("Enter the [green]folder path[/] to scan for similar PDFs:").AllowEmpty()
        );

        if (string.IsNullOrEmpty(folderPath))
        {
            return;
        }

        try
        {
            var result = await _pdfSimilarityService.FindSimilarPdfsAsync(folderPath);
            _pdfSimilarityReportService.DisplayResults(result);

            if (result.SimilarityGroups.Count > 0 || result.ScannedPdfs.Count > 0 || result.UngroupedFiles.Count > 0)
            {
                AnsiConsole.WriteLine();

                var exportChoice = AnsiConsole.Confirm("Would you like to export similarity results to a text file?");
                if (exportChoice)
                {
                    try
                    {
                        var exportPath = await _pdfSimilarityService.ExportSimilarityResultsAsync(result, folderPath);
                        AnsiConsole.MarkupLine($"[green]✅ Similarity report exported to: {exportPath}[/]");
                    }
                    catch (Exception ex)
                    {
                        AnsiConsole.MarkupLine($"[red]❌ Failed to export report: {ex.Message}[/]");
                    }
                }

                AnsiConsole.WriteLine();

                var organizeChoice = AnsiConsole.Confirm(
                    "Would you like to organize the files into folders by similarity groups?"
                );
                if (organizeChoice)
                {
                    try
                    {
                        var organizedPath = await _pdfSimilarityService.OrganizeFilesAsync(result, folderPath);
                        AnsiConsole.MarkupLine($"[green]✅ Files organized into: {organizedPath}[/]");
                        AnsiConsole.MarkupLine("[dim]📁 Folder structure:[/]");
                        AnsiConsole.MarkupLine("[dim]  ├── scanned_pdfs/ (PDFs with no text content)[/]");
                        AnsiConsole.MarkupLine("[dim]  ├── similarity_group_XX/ (Groups of similar PDFs)[/]");
                        AnsiConsole.MarkupLine("[dim]  ├── ungrouped_files/ (PDFs that don't match any other)[/]");
                        AnsiConsole.MarkupLine("[dim]  └── organization_summary.txt (detailed summary)[/]");
                    }
                    catch (Exception ex)
                    {
                        AnsiConsole.MarkupLine($"[red]❌ Failed to organize files: {ex.Message}[/]");
                    }
                }

                AnsiConsole.WriteLine();

                var totalInterestingFiles = result.TotalSimilarFiles + result.ScannedPdfs.Count;
                if (totalInterestingFiles > 0)
                {
                    AnsiConsole.MarkupLine(
                        $"[blue]💡 Tip: You found {totalInterestingFiles} files that might need attention:[/]"
                    );
                    if (result.TotalSimilarFiles > 0)
                    {
                        AnsiConsole.MarkupLine(
                            $"[dim]  • {result.TotalSimilarFiles} similar files (potential duplicates or variations)[/]"
                        );
                    }
                    if (result.ScannedPdfs.Count > 0)
                    {
                        AnsiConsole.MarkupLine(
                            $"[dim]  • {result.ScannedPdfs.Count} scanned PDFs (no extractable text)[/]"
                        );
                    }
                    AnsiConsole.MarkupLine(
                        "[dim]Consider reviewing these groups to identify cleanup opportunities.[/]"
                    );
                }
            }
        }
        catch (DirectoryNotFoundException ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ {ex.Message}[/]");
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Error during PDF similarity detection: {ex.Message}[/]");
        }
    }
}

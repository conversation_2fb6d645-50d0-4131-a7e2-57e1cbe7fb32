using PartnerPortal.AiBenchmark.Console.Models;
using PartnerPortal.AiBenchmark.Console.Services.Reports;
using Spectre.Console;

namespace PartnerPortal.AiBenchmark.Console.Services;

public class FieldAnalysisMenuService
{
    private readonly IFieldAnalysisService _fieldAnalysisService;
    private readonly IReportFactory _reportFactory;
    private readonly IConfigurationService _configService;
    private readonly IJsonFieldExtractionService _jsonFieldExtractionService;

    public FieldAnalysisMenuService(
        IFieldAnalysisService fieldAnalysisService,
        IReportFactory reportFactory,
        IConfigurationService configService,
        IJsonFieldExtractionService jsonFieldExtractionService
    )
    {
        _fieldAnalysisService = fieldAnalysisService;
        _reportFactory = reportFactory;
        _configService = configService;
        _jsonFieldExtractionService = jsonFieldExtractionService;
    }

    public async Task StartAsync()
    {
        AnsiConsole.Write(new FigletText("Field Analysis").LeftJustified().Color(Color.CornflowerBlue));

        while (true)
        {
            var choice = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("[green]Azure Document AI Field Analysis Tool[/]")
                    .PageSize(10)
                    .AddChoices(
                        [
                            "📁 Configure Test Data Path",
                            "🚀 Start Analysis",
                            "📊 Generate Reports",
                            "🔍 Find fields in responses",
                            "🔍 Analyze content patterns",
                            "❌ Exit",
                        ]
                    )
            );

            try
            {
                switch (choice)
                {
                    case "📁 Configure Test Data Path":
                        await ConfigureTestDataPathAsync();
                        break;
                    case "🚀 Start Analysis":
                        await StartAnalysisAsync();
                        break;
                    case "📊 Generate Reports":
                        await GenerateReportsAsync();
                        break;
                    case "🔍 Find fields in responses":
                        await FindFieldsInResponsesAsync();
                        break;
                    case "❌ Exit":
                        AnsiConsole.MarkupLine("[yellow]Goodbye![/]");
                        return;
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]❌ Error: {ex.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            System.Console.ReadKey();
            AnsiConsole.Clear();
        }
    }

    private async Task ConfigureTestDataPathAsync()
    {
        var testDataPath = AnsiConsole.Ask<string>("Enter the path to the test data:");
        var cleanTestDataPath = testDataPath.Trim('"', '\'');

        if (!Directory.Exists(cleanTestDataPath))
        {
            AnsiConsole.MarkupLine($"[red]❌ Test data path does not exist: {testDataPath}[/]");
            return;
        }

        var config = await _configService.LoadConfigurationAsync();
        var updatedConfig = config with { FieldAnalysisTestDataPath = cleanTestDataPath };
        await _configService.SaveConfigurationAsync(updatedConfig);

        AnsiConsole.MarkupLine($"[green]✅ Test data path set to: {cleanTestDataPath}[/]");
    }

    private async Task StartAnalysisAsync()
    {
        try
        {
            var documentType = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("[green]Select document type[/]")
                    .PageSize(10)
                    .AddChoices(
                        [
                            nameof(FieldAnalysisDocTypeEnum.CompletionCertificate),
                            nameof(FieldAnalysisDocTypeEnum.Invoice),
                        ]
                    )
            );

            var model = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("[green]Select model[/]")
                    .PageSize(10)
                    .AddChoices(["TIG6", "prebuilt-invoice"])
            );

            var docType = Enum.Parse<FieldAnalysisDocTypeEnum>(documentType);

            var excelPath = AnsiConsole.Ask<string>("Enter the path to the Excel file:");

            excelPath = excelPath.Trim('"', '\'');

            if (!File.Exists(excelPath))
            {
                AnsiConsole.MarkupLine($"[red]❌ Excel file does not exist: {excelPath}[/]");
                return;
            }

            var config = await _configService.LoadConfigurationAsync();
            var documentFolderPath = config.FieldAnalysisTestDataPath;

            if (!Directory.Exists(documentFolderPath))
            {
                AnsiConsole.MarkupLine($"[red]❌ Document folder does not exist: {documentFolderPath}[/]");
                return;
            }

            var confirm = await AnsiConsole.ConfirmAsync("Are you sure you want to start the analysis?");
            if (!confirm)
            {
                return;
            }

            AnsiConsole.MarkupLine("[green]🚀 Starting AI analysis...[/]");

            var result = await AnsiConsole
                .Progress()
                .AutoClear(false)
                .Columns(
                    [
                        new TaskDescriptionColumn(),
                        new ProgressBarColumn(),
                        new PercentageColumn(),
                        new ElapsedTimeColumn(),
                        new SpinnerColumn(),
                    ]
                )
                .StartAsync(async ctx =>
                {
                    var task = ctx.AddTask("[green]Running AI analysis[/]", maxValue: 100);

                    var progress = new Progress<AnalysisProgress>(update =>
                    {
                        var percentage =
                            update.TotalFiles > 0 ? (double)update.ProcessedFiles / update.TotalFiles * 100 : 0;
                        task.Value = percentage;
                        task.Description =
                            $"[green]Processing:[/] [yellow]{update.CurrentFile}[/] ([blue]{update.ProcessedFiles}/{update.TotalFiles}[/])";
                    });

                    var analysisResult = await _fieldAnalysisService.RunExtractionAsync(
                        progress,
                        excelPath,
                        documentFolderPath,
                        model,
                        docType
                    );

                    task.Value = 100;
                    task.Description = "[green]Analysis completed![/]";

                    return analysisResult;
                });

            AnsiConsole.MarkupLine($"[green]✅ AI analysis completed![/]");
            AnsiConsole.MarkupLine($"[blue]Processed Files:[/] {result.ProcessedFiles}/{result.TotalFiles}");
            AnsiConsole.MarkupLine($"[red]Failed Files: {result.FailedFiles}[/]");
            AnsiConsole.MarkupLine($"[blue]Processing Time:[/] {result.ProcessingTime}");

            if (result.FailedFiles > 0)
            {
                AnsiConsole.MarkupLine(
                    $"[yellow]⚠️  {result.FailedFiles} file(s) failed to process. Check logs for details.[/]"
                );
            }
        }
        catch (InvalidOperationException ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ {ex.Message}[/]");
        }
    }

    private async Task GenerateReportsAsync()
    {
        try
        {
            var reports = _reportFactory.GetFieldAnalysisReports().ToList();

            if (reports.Count == 0)
            {
                AnsiConsole.MarkupLine("[yellow]⚠️  No field analysis reports available![/]");
                return;
            }

            var reportChoice = AnsiConsole.Prompt(
                new SelectionPrompt<IReport>()
                    .Title("[green]Select a report to generate[/]")
                    .PageSize(10)
                    .UseConverter(report => report.DisplayName)
                    .AddChoices(reports)
            );

            await reportChoice.ExecuteAsync();
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Error generating report: {ex.Message}[/]");
        }
    }

    private async Task FindFieldsInResponsesAsync()
    {
        try
        {
            var folderPath = AnsiConsole.Ask<string>("Enter the path to the JSON responses folder:");

            AnsiConsole.MarkupLine("[blue]Processing JSON files...[/]");

            await _jsonFieldExtractionService.ExtractTaxRatesFromFolderAsync(folderPath);
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Error: {ex.Message}[/]");
        }
    }
}

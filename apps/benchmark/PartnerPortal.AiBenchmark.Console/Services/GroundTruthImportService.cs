using ClosedXML.Excel;
using Microsoft.Extensions.Logging;
using PartnerPortal.AiBenchmark.Console.Models;

namespace PartnerPortal.AiBenchmark.Console.Services;

public interface IGroundTruthImportService
{
    Task ImportFromExcelAsync(string testDataPath, string excelFilePath);
}

public class GroundTruthImportService : IGroundTruthImportService
{
    private readonly ILogger<GroundTruthImportService> _logger;
    private readonly IResultsService _resultsService;

    public GroundTruthImportService(ILogger<GroundTruthImportService> logger, IResultsService resultsService)
    {
        _logger = logger;
        _resultsService = resultsService;
    }

    public async Task ImportFromExcelAsync(string testDataPath, string excelFilePath)
    {
        if (string.IsNullOrWhiteSpace(testDataPath) || !Directory.Exists(testDataPath))
            throw new InvalidOperationException("Test data path is not configured or does not exist.");

        if (string.IsNullOrWhiteSpace(excelFilePath) || !File.Exists(excelFilePath))
            throw new FileNotFoundException("Excel file not found.", excelFilePath);

        _logger.LogInformation("Starting ground truth import from Excel: {Excel}", excelFilePath);

        using var workbook = new XLWorkbook(excelFilePath);
        var worksheet =
            workbook.Worksheets.FirstOrDefault()
            ?? throw new InvalidOperationException("Excel file does not contain any worksheet.");

        // Header mapping
        var headers = new Dictionary<string, int>(StringComparer.CurrentCultureIgnoreCase);
        var headerRow = worksheet.Row(1);
        var lastCol = worksheet.LastColumnUsed()?.ColumnNumber() ?? 0;
        if (lastCol == 0)
            throw new InvalidOperationException("Excel worksheet appears to be empty.");
        for (int col = 1; col <= lastCol; col++)
        {
            var header = headerRow.Cell(col).GetString().Trim();
            if (!string.IsNullOrEmpty(header))
                headers[header] = col;
        }

        // Required headers
        var required = new[] { "FILE", "SZÁMLA", "TIG", "SZÁLLÍTÓLEVÉL", "MELLÉKLET" };
        foreach (var key in required)
        {
            if (!headers.ContainsKey(key))
            {
                throw new InvalidOperationException($"Missing required column header: {key}");
            }
        }

        var lastRow = worksheet.LastRowUsed()?.RowNumber() ?? 0;
        if (lastRow < 2)
            return; // no data rows
        var rowCount = lastRow;
        var processed = 0;

        // Build quick lookup of allowed files in folder
        var allowedFiles = Directory
            .GetFiles(testDataPath, "*", SearchOption.TopDirectoryOnly)
            .Select(Path.GetFileName)!
            .ToHashSet(StringComparer.CurrentCultureIgnoreCase);

        for (int row = 2; row <= rowCount; row++)
        {
            var fileName = worksheet.Cell(row, headers["FILE"]).GetString().Trim();
            if (string.IsNullOrEmpty(fileName))
                continue; // skip empty rows

            if (!allowedFiles.Contains(fileName))
            {
                _logger.LogWarning("File listed in Excel not found in folder. Skipping. File: {File}", fileName);
                continue; // ignore and move on
            }

            var cellInvoice = worksheet.Cell(row, headers["SZÁMLA"]).GetString();
            var cellTig = worksheet.Cell(row, headers["TIG"]).GetString();
            var cellDelivery = worksheet.Cell(row, headers["SZÁLLÍTÓLEVÉL"]).GetString();
            var cellAttachment = worksheet.Cell(row, headers["MELLÉKLET"]).GetString();

            if (
                string.IsNullOrWhiteSpace(cellInvoice)
                && string.IsNullOrWhiteSpace(cellTig)
                && string.IsNullOrWhiteSpace(cellDelivery)
                && string.IsNullOrWhiteSpace(cellAttachment)
            )
            {
                // If all are empty, skip this file
                _logger.LogInformation("No ranges specified for file in Excel. Skipping file: {File}", fileName);
                continue;
            }

            var pageRanges = new List<PageRange>();

            // Parse each column ranges; semicolon-separated segments
            ParseAndAddRanges(cellInvoice, DocumentType.Invoice, pageRanges);
            ParseAndAddRanges(cellTig, DocumentType.Tig, pageRanges);
            ParseAndAddRanges(cellDelivery, DocumentType.DeliveryNote, pageRanges);
            // MELLÉKLET -> Other
            ParseAndAddRanges(cellAttachment, DocumentType.Other, pageRanges);

            // Validate sequential, no overlaps, starting from 1
            ValidateAllRangesOrThrow(pageRanges);

            await _resultsService.AddOrUpdateGroundTruthAsync(testDataPath, fileName, pageRanges);
            processed++;
        }

        _logger.LogInformation("Ground truth import completed. Processed {Count} row(s)", processed);
    }

    private static void ParseAndAddRanges(string? cellValue, DocumentType type, List<PageRange> target)
    {
        if (string.IsNullOrWhiteSpace(cellValue))
            return;

        var parts = cellValue.Split(';', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
        foreach (var part in parts)
        {
            if (!TryParsePageRange(part, out int start, out int end))
            {
                throw new InvalidOperationException($"Invalid page range format: '{part}'. Expected 'start-end'.");
            }
            target.Add(new PageRange(start, end, type));
        }
    }

    private static bool TryParsePageRange(string input, out int startPage, out int endPage)
    {
        startPage = 0;
        endPage = 0;
        if (string.IsNullOrWhiteSpace(input))
            return false;
        var parts = input.Split('-', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
        if (parts.Length != 2)
            return false;
        if (!int.TryParse(parts[0], out startPage) || !int.TryParse(parts[1], out endPage))
            return false;
        return startPage > 0 && endPage > 0 && startPage <= endPage;
    }

    private static void ValidateAllRangesOrThrow(List<PageRange> ranges)
    {
        if (ranges.Count == 0)
            throw new InvalidOperationException("No page ranges were provided.");

        var sorted = ranges.OrderBy(r => r.StartPage).ToList();

        // First must start at 1
        if (sorted[0].StartPage != 1)
            throw new InvalidOperationException("First page range must start from page 1.");

        // Check overlaps and gaps sequentially over the whole list, regardless of document type
        var currentEnd = sorted[0].EndPage;
        for (int i = 1; i < sorted.Count; i++)
        {
            var r = sorted[i];
            if (r.StartPage <= currentEnd)
            {
                throw new InvalidOperationException(
                    $"Range {r.StartPage}-{r.EndPage} overlaps with existing range ending at {currentEnd}."
                );
            }
            if (r.StartPage != currentEnd + 1)
            {
                throw new InvalidOperationException(
                    $"Range {r.StartPage}-{r.EndPage} creates a gap. Next range should start from page {currentEnd + 1}."
                );
            }
            currentEnd = Math.Max(currentEnd, r.EndPage);
        }
    }
}

namespace PartnerPortal.AiBenchmark.Console.Helpers;

public static class ArrayHelper
{
    public static bool ArraysEqual(int[] array1, int[] array2)
    {
        return array1.SequenceEqual(array2);
    }

    /// <summary>
    /// Checks if two page ranges intersect
    /// Page ranges are expected to be in format [startPage, endPage].
    /// </summary>
    public static bool PageRangesIntersect(int[] range1, int[] range2)
    {
        if (range1.Length != 2 || range2.Length != 2)
            return false;

        int start1 = range1[0],
            end1 = range1[1];
        int start2 = range2[0],
            end2 = range2[1];

        return start1 <= end2 && start2 <= end1;
    }
}

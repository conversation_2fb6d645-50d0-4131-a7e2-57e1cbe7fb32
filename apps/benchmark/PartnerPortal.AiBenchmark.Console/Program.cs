﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PartnerPortal.AiBenchmark.Console.Models;
using PartnerPortal.AiBenchmark.Console.Services;
using PartnerPortal.AiBenchmark.Console.Services.Reports;
using PartnerPortal.AiBenchmark.Console.Services.Reports.Generators;
using PartnerPortal.Backend.Document.Models;
using PartnerPortal.Backend.Document.Services;
using PartnerPortal.Backend.Document.Services.Extractors;
using PartnerPortal.Backend.Shared.Common.Utils.FakeDecryptor;
using Serilog;

try
{
    var configuration = new ConfigurationBuilder()
        .SetBasePath(Directory.GetCurrentDirectory())
        .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
        .AddUserSecrets<Program>()
        .Build();

    Log.Logger = new LoggerConfiguration().ReadFrom.Configuration(configuration).CreateLogger();

    Log.Information("Starting Azure Document AI Benchmark Console Application");

    var host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureAppConfiguration(
            (_, configBuilder) =>
            {
                configBuilder.Add(new EncryptedEnvironmentVariablesConfigurationSource(configBuilder.Build()));
            }
        )
        .ConfigureServices(services =>
        {
            // Core services
            services.AddSingleton<ClassificationMenuService>();
            services.AddSingleton<FieldAnalysisMenuService>();
            services.AddSingleton<ICoreService, CoreService>();
            services.AddSingleton<IConfigurationService, ConfigurationService>();
            services.AddSingleton<IResultsService, ResultsService>();
            services.AddSingleton<IDuplicateDetectionService, DuplicateDetectionService>();
            services.AddSingleton<IDuplicateReportService, DuplicateReportService>();
            services.AddSingleton<IPdfSimilarityService, PdfSimilarityService>();
            services.AddSingleton<IPdfSimilarityReportService, PdfSimilarityReportService>();
            services.AddSingleton<IGroundTruthImportService, GroundTruthImportService>();
            services.AddSingleton<IFieldAnalysisService, FieldAnalysisService>();
            services.AddSingleton<IFieldAnalysisResultsService, FieldAnalysisResultsService>();
            services.AddSingleton<IFieldComparisonService, FieldComparisonService>();
            services.AddSingleton<IJsonFieldExtractionService, JsonExtractionService>();
            services.AddSingleton<IAnalyzeResultExtractor<CompletionCertificateAiData>, CompletionCertExtractor>();
            services.AddSingleton<IAnalyzeResultExtractor<InvoiceAiData>, InvoiceExtractor>();
            // Report services
            services.AddSingleton<IReportService, ReportService>();
            services.AddSingleton<IReportFactory, ReportFactory>();

            // Report types
            services.AddSingleton<AutoSplitComparisonReport>();
            services.AddSingleton<NoSplitComparisonReport>();
            services.AddSingleton<ConfidenceAnalysisReport>();
            services.AddSingleton<FieldAnalysisReport>();

            // Report generators
            services.AddSingleton<IReportGenerator<AutoSplitComparisonReportData>, AutoSplitConsoleReportGenerator>();
            services.AddSingleton<IReportGenerator<AutoSplitComparisonReportData>, AutoSplitExcelReportGenerator>();
            services.AddSingleton<IReportGenerator<NoSplitComparisonReportData>, NoSplitConsoleReportGenerator>();
            services.AddSingleton<IReportGenerator<NoSplitComparisonReportData>, NoSplitExcelReportGenerator>();
            services.AddSingleton<
                IReportGenerator<ConfidenceAnalysisReportData>,
                ConfidenceAnalysisConsoleReportGenerator
            >();
            services.AddSingleton<
                IReportGenerator<ConfidenceAnalysisReportData>,
                ConfidenceAnalysisExcelReportGenerator
            >();
            services.AddSingleton<IReportGenerator<FieldAnalysisReportData>, FieldAnalysisConsoleReportGenerator>();
            services.AddSingleton<IReportGenerator<FieldAnalysisReportData>, FieldAnalysisExcelReportGenerator>();

            var useFakeService = configuration.GetValue<bool>("UseFakeDocumentProcessingService", false);

            if (useFakeService)
            {
                Log.Information("Using FakeDocumentProcessingService for testing");
                services.AddSingleton<IDocumentProcessingService, FakeDocumentProcessingService>();
            }
            else
            {
                Log.Information("Using real DocumentProcessingService with Azure Document Intelligence");
                //services.AddSingleton<IDocumentProcessingService, AzureDocumentProcessingService>();
                services.AddSingleton<IDocumentProcessingService, PythonDocumentProcessingService>();
            }
        })
        .Build();

    Directory.CreateDirectory("logs");

    var modeString = configuration.GetValue<string>("Mode");
    if (!Enum.TryParse<ApplicationMode>(modeString, out var mode))
    {
        Log.Error(
            "Invalid or missing application mode configuration. Expected 'Classification' or 'FieldAnalysis', got: {Mode}",
            modeString
        );
        throw new InvalidOperationException($"Invalid application mode: {modeString}");
    }

    Log.Information("Application mode: {Mode}", mode);

    switch (mode)
    {
        case ApplicationMode.Classification:
            var classificationMenu = host.Services.GetRequiredService<ClassificationMenuService>();
            await classificationMenu.StartAsync();
            break;
        case ApplicationMode.FieldAnalysis:
            var fieldAnalysisMenu = host.Services.GetRequiredService<FieldAnalysisMenuService>();
            await fieldAnalysisMenu.StartAsync();
            break;
        default:
            Log.Error("Unsupported application mode: {Mode}", mode);
            throw new NotSupportedException($"Application mode '{mode}' is not supported");
    }

    Log.Information("Application completed successfully");
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
    Console.WriteLine($@"An error occurred: {ex.Message}");
    Console.WriteLine("Press any key to exit...");
    Console.ReadKey();
}
finally
{
    Log.CloseAndFlush();
}

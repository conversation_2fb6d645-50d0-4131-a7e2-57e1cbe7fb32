namespace PartnerPortal.AiBenchmark.Console.Models;

public enum DocumentType
{
    Invoice,
    Tig,
    DeliveryNote,
    Other,
    None,
}

public record PageRange(int StartPage, int EndPage, DocumentType DocumentType)
{
    public override string ToString() => $"Pages {StartPage}-{EndPage}: {DocumentType}";
}

// Ground truth document structure
public class GroundTruthDocument
{
    public string DocType { get; set; } = string.Empty;
    public int[] PageRange { get; set; } = [];

    public GroundTruthDocument() { }

    public GroundTruthDocument(PageRange pageRange)
    {
        DocType = pageRange.DocumentType.ToString();
        PageRange = [pageRange.StartPage, pageRange.EndPage];
    }

    public PageRange ToPageRange()
    {
        if (PageRange.Length != 2)
            throw new InvalidOperationException("Page range must have exactly 2 elements");

        if (!Enum.TryParse<DocumentType>(DocType, out var docType))
            docType = DocumentType.Other;

        return new PageRange(PageRange[0], PageRange[1], docType);
    }
}

// AI prediction structure
public class AiPrediction
{
    public string DocType { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public int[] PageRange { get; set; } = [];

    /// <summary>
    /// The actual className which was created in Azure Document Intelligence.
    /// </summary>
    public string ActualDocTypeFromResponse { get; set; } = string.Empty;

    public AiPrediction() { }

    public AiPrediction(
        int startPage,
        int endPage,
        DocumentType documentType,
        double confidence,
        string actualDocTypeFromResponse
    )
    {
        DocType = documentType.ToString();
        Confidence = confidence;
        PageRange = [startPage, endPage];
        ActualDocTypeFromResponse = actualDocTypeFromResponse;
    }
}

// File analysis result structure
public class FileAnalysisResult
{
    public string FileName { get; set; } = string.Empty;
    public string? ModelVersion { get; set; }
    public DateTime? AnalysisRunTimestamp { get; set; }
    public List<GroundTruthDocument> GroundTruthDocuments { get; set; } = [];
    public List<AiPrediction> AiPredictions { get; set; } = [];

    public FileAnalysisResult() { }

    public FileAnalysisResult(string fileName)
    {
        FileName = fileName;
    }
}

// Main results container
public class BenchmarkResults
{
    public List<FileAnalysisResult> Results { get; set; } = [];

    public BenchmarkResults() { }
}

// Legacy structures for ground truth input (internal use)
public record GroundTruthFile(string FilePath, List<PageRange> PageRanges)
{
    public string FileName => Path.GetFileName(FilePath) ?? string.Empty;
}

// Configuration structure (simplified)
public record BenchmarkConfiguration(
    string ClassificationTestDataPath,
    string FieldAnalysisTestDataPath,
    DateTime LastModified
);

public class BenchmarkConfigurationJson
{
    public string ClassificationTestDataPath { get; set; } = string.Empty;
    public string FieldAnalysisTestDataPath { get; set; } = string.Empty;
    public DateTime LastModified { get; set; }

    public BenchmarkConfigurationJson() { }

    public BenchmarkConfigurationJson(string classificationTestDataPath, string fieldAnalysisTestDataPath)
    {
        ClassificationTestDataPath = classificationTestDataPath;
        FieldAnalysisTestDataPath = fieldAnalysisTestDataPath;
        LastModified = DateTime.Now;
    }
}

public record AnalysisResult(int ProcessedFiles, int FailedFiles, int TotalFiles, TimeSpan ProcessingTime);

public record ComparisonResult(
    string FileName,
    DocumentType GroundTruthType,
    int[] GroundTruthPageRange,
    DocumentType? PredictedType,
    int[]? PredictedPageRange,
    double? Confidence,
    bool IsCorrectClassification,
    string Status // "Correct", "Incorrect", "Missing Prediction", "Extra Prediction"
);

public record ReportSummary(
    int TotalComparisons,
    int CorrectClassifications,
    int IncorrectClassifications,
    int MissingPredictions,
    int ExtraPredictions,
    double AccuracyPercentage,
    Dictionary<DocumentType, DocumentTypeStats> DocumentTypeStats
);

public record DocumentTypeStats(
    int TotalGroundTruth,
    int CorrectPredictions,
    int IncorrectPredictions,
    int MissingPredictions,
    double AccuracyPercentage
);

public record NoSplitComparisonResult(
    string FileName,
    DocumentType GroundTruthType,
    DocumentType? PredictedType,
    string? ActualDocTypeFromResponse,
    double? Confidence,
    bool IsCorrectClassification,
    string Status // "Correct", "Incorrect", "Missing Prediction", "Extra Prediction", "Missing Ground Truth"
);

public record ConfidenceAnalysisResult(
    string FileName,
    DocumentType GroundTruthType,
    DocumentType? PredictedType,
    double? Confidence,
    bool IsCorrectClassification,
    string Status,
    ConfidenceProblemCategory ProblemCategory,
    int TrainingPriority
);

public enum ConfidenceProblemCategory
{
    HighConfidenceCorrect,
    LowConfidenceCorrect,
    HighConfidenceIncorrect,
    LowConfidenceIncorrect,
    MissingPrediction,
    ExtraPrediction,
}

public record ConfidenceDistribution(
    DocumentType DocumentType,
    int VeryLowCount, // 0.0 - 0.3
    int LowCount, // 0.3 - 0.6
    int MediumCount, // 0.6 - 0.8
    int HighCount, // 0.8 - 1.0
    int TotalCount,
    double AverageConfidence
);

public record ConfidenceAnalysisSummary(
    int TotalDocuments,
    int HighConfidenceCorrect,
    int LowConfidenceCorrect,
    int HighConfidenceIncorrect,
    int LowConfidenceIncorrect,
    int MissingPredictions,
    int ExtraPredictions,
    double OverallAccuracy,
    double AverageConfidence,
    List<ConfidenceDistribution> DistributionByType
);

public record HighestConfidenceInvoiceAccuracyResult(
    string FileName,
    int TotalInvoicePredictions,
    int[] HighestConfidenceInvoicePageRange,
    double HighestConfidenceInvoiceConfidence,
    int[] GroundTruthInvoicePageRange
);

public record FirstPredictionInvoiceFailure(
    string FileName,
    int TotalInvoicePredictions,
    int[] FirstPredictionInvoicePageRange,
    double FirstPredictionInvoiceConfidence,
    int[] GroundTruthInvoicePageRange
);

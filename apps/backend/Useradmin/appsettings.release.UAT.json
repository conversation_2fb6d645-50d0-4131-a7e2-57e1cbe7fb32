{"AlertEmailConfiguration": {"From": "kele.s<PERSON><PERSON>@ibtconsulting.hu", "Password": "Encrypted:PP_ALERTEMAIL_PW", "Port": 587, "SmtpServer": "smtp.office365.com", "Username": "kele.s<PERSON><PERSON>@ibtconsulting.hu"}, "AllowSystemRestOnlyFromIP": "10.10.103.", "BaseUrl": {"SiteUrl": "https://marketon-uat.market.hu/portal-app"}, "BcConfiguration": {"Password": "Encrypted:PP_BC_SOAP_PW", "RestPass": "Encrypted:PP_BC_REST_PW", "RestUser": "BCuser", "Username": "serviceuser", "WebServiceUrl": "http://bcfront1test.office.local:26147/MarketTST_BC26_api/WS/MARK/Codeunit/PortalWebservice"}, "ConnectionStrings": {"DefaultConnection": "Encrypted:PP_DB_CONNECTION"}, "CorsConfiguration": {"AllowedOriginAddresses": "************,ppfront-uat.office.local,marketon-uat.market.hu"}, "DocumentModuleAllowedPartners": ["10240", "11840", "10555", "10894", "11515", "14855", "12678", "10839"], "EmailConfiguration": {"ClientDomain": "ppbackua-uat.market.hu", "From": "<EMAIL>", "Password": "Encrypted:PP_EMAIL_PW", "Port": 25, "SmtpServer": "alert.market.hu", "Username": "<EMAIL>"}, "Jwt": {"AudienceClient": "https://marketon-uat.market.hu/", "IssuerServer": "https://ppbackua-uat.market.hu:7166/"}, "Serilog": {"Enrich": ["WithClientIp"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "PartnerPortal": "Debug", "Quartz": "Warning"}}, "Using": ["Serilog.Enrichers.ClientInfo"], "WriteTo": [{"Args": {"restrictedToMinimumLevel": "Warning"}, "Name": "<PERSON><PERSON><PERSON>"}, {"Args": {"fileSizeLimitBytes": "524288000", "outputTemplate": "[{Timestamp:HH:mm:ss.fff} {ClientIp} {CorrelationId}{Level:u3}] {Message:lj}{NewLine}{Exception}", "path": "log/ua-.log", "retainedFileCountLimit": 100, "rollingInterval": "Day", "rollOnFileSizeLimit": "true"}, "Name": "File"}]}}
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <Version>2.2.1</Version>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9807c34d-c586-4232-85a2-a99e252f9c8b</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="EmailValidation" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Quartz.Serialization.SystemTextJson" />
    <PackageReference Include="CrystalQuartz.AspNetCore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\libs\backend\useradmin\RegSysApiStub\src\src\PartnerPortal.Backend.Useradmin.RegSysApiStub\PartnerPortal.Backend.Useradmin.RegSysApiStub.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\useradmin\UseradminApiStub\src\src\PartnerPortal.Backend.Useradmin.UserAdminApiStub\PartnerPortal.Backend.Useradmin.UserAdminApiStub.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\shared\PartnerPortalDatabase\PartnerPortal.Backend.Shared.PartnerPortalDatabase.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\shared\MarketBcApiClient\PartnerPortal.Backend.Shared.MarketBcApiClient.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\shared\Common\PartnerPortal.Backend.Shared.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="wwwroot\Files\" />
    <Folder Include="wwwroot\Ring\" />
  </ItemGroup>
</Project>

{"AllowedHosts": "*", "BcConfiguration": {"Password": "Password", "RestPass": "BCpass123", "RestUser": "BC", "Username": "Username", "WebServiceUrl": "http://localhost:9347/SoapUiMock/PortalWebservice"}, "CorsConfiguration": {"AllowedOriginAddresses": "localhost"}, "EmailConfiguration": {"From": "<EMAIL>", "Password": "XpkCKz6tFmfzZzbQ2V", "Port": 587, "SmtpServer": "smtp.ethereal.email", "Username": "<EMAIL>"}, "Jwt": {"AudienceClient": "https://localhost:4200/", "IssuerServer": "https://localhost:7166/"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information"}}, "UseSwaggerUI": true}
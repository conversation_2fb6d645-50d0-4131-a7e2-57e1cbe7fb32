<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <Version>2.2.1</Version>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9469b181-a502-46b0-9e01-752f48270a21</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="wwwroot\**" />
    <Content Remove="wwwroot\**" />
    <EmbeddedResource Remove="wwwroot\**" />
    <None Remove="wwwroot\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CrystalQuartz.AspNetCore" />
    <PackageReference Include="MimeTypes">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Quartz.Serialization.SystemTextJson" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\libs\backend\contracts\TigSysApiStub\src\src\PartnerPortal.Backend.Contracts.TigSysApiStub\PartnerPortal.Backend.Contracts.TigSysApiStub.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\contracts\AdditionalWorkApiStub\src\src\PartnerPortal.Backend.Contracts.AdditionalWorkApiStub\PartnerPortal.Backend.Contracts.AdditionalWorkApiStub.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\contracts\ContractsApiStub\src\src\PartnerPortal.Backend.Contracts.ContractsApiStub\PartnerPortal.Backend.Contracts.ContractsApiStub.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\shared\PartnerPortalDatabase\PartnerPortal.Backend.Shared.PartnerPortalDatabase.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\shared\MarketBcApiClient\PartnerPortal.Backend.Shared.MarketBcApiClient.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\shared\Common\PartnerPortal.Backend.Shared.Common.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\document\PartnerPortal.Backend.Document\PartnerPortal.Backend.Document.csproj" />
    <ProjectReference Include="..\..\..\libs\backend\shared\Attachment\PartnerPortal.Backend.Shared.Attachment\PartnerPortal.Backend.Shared.Attachment.csproj" />
  </ItemGroup>
</Project>

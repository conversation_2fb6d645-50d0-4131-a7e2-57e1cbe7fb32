{"AlertEmailConfiguration": {"From": "kele.s<PERSON><PERSON>@ibtconsulting.hu", "Password": "Encrypted:PP_ALERTEMAIL_PW", "Port": 587, "SmtpServer": "smtp.office365.com", "Username": "kele.s<PERSON><PERSON>@ibtconsulting.hu"}, "AllowSystemRestOnlyFromIP": "10.10.103.", "AzureAI": {"DocumentIntelligence": {"Endpoint": "https://marketinvoiceflow.cognitiveservices.azure.com/", "Key": "Encrypted:PP_DOCUMENT_INTELLIGENCE_KEY"}, "ResponsesPath": "\\\\ppfront-dev\\pp_data\\responses-dev"}, "BaseUrl": {"SiteUrl": "http://marketon-dev.market.hu/portal-app"}, "BcConfiguration": {"Password": "Encrypted:PP_BC_SOAP_PW", "RestPass": "Encrypted:PP_BC_REST_PW", "RestUser": "BCuser", "Username": "serviceuser", "WebServiceUrl": "http://bcfront3test.office.local:24547/MarketELOTST_BC24_api/WS/MARK/Codeunit/PortalWebservice"}, "ConnectionStrings": {"DefaultConnection": "Encrypted:PP_DB_CONNECTION"}, "CorsConfiguration": {"AllowedOriginAddresses": "************,ppfront-dev.market.hu,marketon-dev.market.hu"}, "DocumentModule": {"FilesPath": "\\\\ppfront-dev\\pp_data\\documents-dev"}, "EloConfiguration": {"Endpoint": "http://localhost:8088/SoapUiMock/EloWebservice", "MergedDocumentsPath": "\\\\ppfront-dev\\pp_data\\inv-files-dev", "Password": "Password", "Username": "Username"}, "EmailConfiguration": {"From": "<EMAIL>", "Password": "Encrypted:PP_EMAIL_PW", "Port": 25, "SmtpServer": "alert.market.hu", "Username": "<EMAIL>"}, "FileUpload": {"StoredFilesPath": "\\\\ppfront-dev\\pp_data\\tig-files-dev"}, "Jwt": {"AudienceClient": "https://marketon-dev.market.hu/", "IssuerServer": "https://ppbackua-dev.market.hu:7166/"}, "NavConfig": {"NavMode": "Test"}, "Serilog": {"Enrich": ["WithClientIp"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Nav.Invoice.Client": "Debug", "PartnerPortal": "Debug", "Quartz": "Warning"}}, "Using": ["Serilog.Enrichers.ClientInfo"], "WriteTo": [{"Args": {"restrictedToMinimumLevel": "Warning"}, "Name": "<PERSON><PERSON><PERSON>"}, {"Args": {"fileSizeLimitBytes": "524288000", "outputTemplate": "[{Timestamp:HH:mm:ss.fff} {ClientIp} {CorrelationId}{Level:u3}] {Message:lj}{NewLine}{Exception}", "path": "log/co-.log", "retainedFileCountLimit": 100, "rollingInterval": "Day", "rollOnFileSizeLimit": "true"}, "Name": "File"}]}}
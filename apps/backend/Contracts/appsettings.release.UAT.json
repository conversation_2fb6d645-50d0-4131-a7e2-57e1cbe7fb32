{"AlertEmailConfiguration": {"From": "kele.s<PERSON><PERSON>@ibtconsulting.hu", "Password": "Encrypted:PP_ALERTEMAIL_PW", "Port": 587, "SmtpServer": "smtp.office365.com", "Username": "kele.s<PERSON><PERSON>@ibtconsulting.hu"}, "AllowSystemRestOnlyFromIP": "10.10.103.", "AzureAI": {"DocumentIntelligence": {"Endpoint": "https://marketinvoiceflow.cognitiveservices.azure.com/", "Key": "Encrypted:PP_DOCUMENT_INTELLIGENCE_KEY"}, "ResponsesPath": "\\\\ppfront-uat\\pp_data\\responses"}, "BaseUrl": {"SiteUrl": "https://marketon-uat.market.hu/portal-app"}, "BcConfiguration": {"Password": "Encrypted:PP_BC_SOAP_PW", "RestPass": "Encrypted:PP_BC_REST_PW", "RestUser": "BCuser", "Username": "serviceuser", "WebServiceUrl": "http://bcfront1test.office.local:26147/MarketTST_BC26_api/WS/MARK/Codeunit/PortalWebservice"}, "ConnectionStrings": {"DefaultConnection": "Encrypted:PP_DB_CONNECTION"}, "CorsConfiguration": {"AllowedOriginAddresses": "************,ppfront-uat.office.local,marketon-uat.market.hu"}, "DocumentModule": {"FilesPath": "\\\\ppfront-uat\\pp_data\\documents"}, "EloConfiguration": {"Endpoint": "https://elouat9.office.local:9073/ex-elo/services/ELOWebService", "MergedDocumentsPath": "\\\\ppfront-uat\\pp_data\\inv-files", "Password": "Encrypted:PP_ELO_PW", "Username": "Encrypted:PP_ELO_USER"}, "EmailConfiguration": {"ClientDomain": "ppbackua-uat.market.hu", "From": "<EMAIL>", "Password": "Encrypted:PP_EMAIL_PW", "Port": 25, "SmtpServer": "alert.market.hu", "Username": "<EMAIL>"}, "FileUpload": {"StoredFilesPath": "\\\\ppfront-uat\\pp_data\\tig-files"}, "Jwt": {"AudienceClient": "https://marketon-uat.market.hu/", "IssuerServer": "https://ppbackua-uat.market.hu:7166/"}, "NavConfig": {"NavMode": "Test"}, "Serilog": {"Enrich": ["WithClientIp"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Nav.Invoice.Client": "Information", "PartnerPortal": "Debug", "Quartz": "Warning"}}, "Using": ["Serilog.Enrichers.ClientInfo"], "WriteTo": [{"Args": {"restrictedToMinimumLevel": "Warning"}, "Name": "<PERSON><PERSON><PERSON>"}, {"Args": {"fileSizeLimitBytes": "524288000", "outputTemplate": "[{Timestamp:HH:mm:ss.fff} {ClientIp} {CorrelationId}{Level:u3}] {Message:lj}{NewLine}{Exception}", "path": "log/co-.log", "retainedFileCountLimit": 100, "rollingInterval": "Day", "rollOnFileSizeLimit": "true"}, "Name": "File"}]}}
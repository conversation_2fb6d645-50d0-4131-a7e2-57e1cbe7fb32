{"BcConfiguration": {"Password": "Password", "RestPass": "BCpass123", "RestUser": "BC", "Username": "Username", "WebServiceUrl": "http://localhost:9347/SoapUiMock/PortalWebservice"}, "CorsConfiguration": {"AllowedOriginAddresses": "localhost"}, "FileUpload": {"FileCountLimit": 5, "StoredFilesPath": "c:\\files"}, "Jwt": {"AudienceClient": "https://localhost:4200/", "IssuerServer": "https://localhost:7166/"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information"}}, "UseSwaggerUI": true}
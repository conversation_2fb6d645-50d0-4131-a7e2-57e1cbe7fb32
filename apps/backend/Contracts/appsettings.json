{"AllowedHosts": "*", "AuditLogRetentionDays": 90, "AzureAI": {"ClassificationModelId": "choice8", "CompletionCertModelId": "TIG6"}, "BaseUrl": {"SiteUrl": "http://localhost:4200"}, "DaysToEscalation": 2, "DocumentModule": {"FileCountLimit": 40, "FileSizeLimit": 10485760, "PermittedExtensions": [".pdf", ".jpeg", ".jpg", ".png", ".bmp", ".tiff", ".tif", ".heif"]}, "EmailToSecondaryApproverCron": "0 0 1 * * ?", "EmailToSecondaryApproverHours": 96, "EscalationCron": "0 0 * ? * * *", "FileUpload": {"FileCountLimit": 25, "FileSizeLimit": 52428800, "PermittedExtensions": [".pdf", ".docx", ".xlsx", ".jpg", ".png", ".tif"]}, "HardDeleteDocumentCron": "0 0 4 * * ?", "HardDeleteDocumentDays": 90, "MarketCustomers": {"CustomerDetails": {"11163": {"Code": "OKM", "ExchangeKey": "7ca3521TVY2CU72M", "Login": "0P3FonxGqarP1Bq", "Password": "OKM_Kft_IBT_2025_NAV_TESZT", "SignKey": "59-834f-b4b382b30747521TVY2CUZ7H", "TaxNumber": "11313667"}, "11901": {"Code": "MARS"}, "12745": {"Code": "MORA", "ExchangeKey": "5745517TBDZO0IN6", "Login": "uc9sm4xdszcgwe0", "Password": "IBTtechnikai25", "SignKey": "e8-89bb-d757f484ba73517TBDZP25GJ", "TaxNumber": "13327824"}, "13774": {"Code": "MARK", "ExchangeKey": "5044521TKXK4AJ7F", "Login": "xpdagmxelrmztzo", "Password": "MarkeT_IbT_2026", "SignKey": "c2-b56f-ee1c2032ff15521TKXK4B6IH", "TaxNumber": "14776355"}, "14855": {"Code": "LEAN"}, "15355": {"Code": "VISZ", "ExchangeKey": "dfac522PN1OF12VN", "Login": "8i058b1u6nweqea", "Password": "IBT_Vilati_teszthez", "SignKey": "ba-a744-7d6714235569522PN1OF2GDQ", "TaxNumber": "25440661"}, "21874": {"Code": "MRKT"}}}, "NavConfig": {"SoftwareType": {"SoftwareDevContact": "<EMAIL>", "SoftwareDevName": "IBT Premier Consulting Kft.", "SoftwareId": "HU26606242-0000000", "SoftwareMainVersion": "1.0.0", "SoftwareName": "MarketON", "SoftwareOperation": "LocalSoftware"}}, "UnfilledDocumentDeleteCron": "0 0 2 * * ?", "UnfilledDocumentHoursDelete": 2}
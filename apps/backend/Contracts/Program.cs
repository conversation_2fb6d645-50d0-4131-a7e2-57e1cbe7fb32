using System.Diagnostics;
using CrystalQuartz.AspNetCore;
using PartnerPortal.Backend.Contracts.Controllers.Examples;
using PartnerPortal.Backend.Contracts.Extensions;
using PartnerPortal.Backend.Shared.Common.CommonExtensions;
using PartnerPortal.Backend.Shared.Common.Exceptions;
using PartnerPortal.Backend.Shared.Common.Utils.FakeDecryptor;
using Quartz;
using Serilog;
using Serilog.Context;
using Serilog.Settings.Configuration;
using Serilog.Sinks.File;

namespace PartnerPortal.Backend.Contracts;

public class Program
{
    public static async Task Main(string[] args)
    {
        try
        {
            var builder = WebApplication.CreateBuilder(args);

            builder.Configuration.Sources.Add(
                new EncryptedEnvironmentVariablesConfigurationSource(builder.Configuration)
            );

            Directory.SetCurrentDirectory(builder.Environment.ContentRootPath);
            var cwdCorrect = Directory.GetCurrentDirectory();

            // Log.Information("Initial working directory: {cwdInitial}", cwdInitial);

            // Configure Logging (switch to using "Serilog" only if its config block presents in appsettings*.json, e.g. to log both to Console as well as daily rolling files)
            bool useSerilog = builder.Configuration.GetSection("Serilog").Exists();

            if (useSerilog)
            {
                UseSerilog(builder);
                Log.Information("Correct working directory: {cwdCorrect}", cwdCorrect);
            }

            Log.Information("Contracts module building started");

            Log.Information("Current environment: {Environment}", builder.Environment.EnvironmentName);

            // enable using SwaggerUI only if configured in env appsettings
            bool useSwaggerUi = builder.Configuration.GetValue<bool>("UseSwaggerUI");
            {
                builder.Services.AddSwaggerUi<PostTigStatusRequestExample>(builder.Configuration);
            }

            builder.Services.ConfigureMarketApplicationServices(builder.Configuration);

            builder.Services.ConfigureMarketIdentityService();

            var app = builder.Build();

            // TODO temporarily enable development settings also for release.DEV!
            // Configure the HTTP request pipeline.
            // if (app.Environment.IsDevelopment())     // FIXME uncomment later for proper releasing!
            // {
            //app.UseDeveloperExceptionPage();

            if (useSwaggerUi)
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }
            // }
            // else
            // {
            app.UseCors("MarketCorsPolicy");
            app.MarketUseExceptionHandler("/Error");

            // }

            if (useSerilog)
            {
                // app.MarketUseCorrelationIdInLogContext();
                app.UseCustomSerilogRequestLogging();
            }

            app.UseHttpsRedirection();

            app.UseAuthentication();

            app.UseAuthorization();

            app.MapControllers();

            // Only enable CrystalQuartz and LaunchBrowser for DEV and UAT environments
            if (app.Environment.EnvironmentName is "Development" or "UAT" or "DEV")
            {
                var schedulerFactory = app.Services.GetRequiredService<ISchedulerFactory>();
                var scheduler = await schedulerFactory.GetScheduler();
                app.UseCrystalQuartz(() => scheduler);

                if (builder.Configuration.GetValue<bool>("LaunchSwaggerUI"))
                {
                    LaunchBrowser();
                }
            }

            app.Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Error happened during Contracts module start");
            throw;
        }
    }

    private static void LaunchBrowser()
    {
        _ = Task.Run(async () =>
        {
            await Task.Delay(1000);
            Process.Start(
                new ProcessStartInfo { FileName = "http://localhost:5243/swagger/index.html", UseShellExecute = true }
            );
        });
    }

    private static void UseSerilog(WebApplicationBuilder builder)
    {
        if (builder.Environment.EnvironmentName != "Development")
        {
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Configuration)
                .Enrich.FromLogContext()
                .Filter.ByExcluding(logEvent => logEvent.Exception is IClientException)
                .CreateLogger();

            LogContext.PushProperty("CorrelationId", "<no http context for correlation id> ");

            builder.Host.UseSerilog();
        }
        else
        {
            builder.Logging.ClearProviders();

            // see https://stackoverflow.com/questions/66276966/visual-studio-produce-single-file-publish-serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Configuration, ConfigurationAssemblySource.AlwaysScanDllFiles)
                .Enrich.FromLogContext()
                .CreateLogger();

            builder.Host.UseSerilog();
        }
    }
}
